package com.ctrip.car.market.job.domain.service

import com.ctrip.car.market.crossrecommend.service.contract.dto.RecommendProductDTO
import com.ctrip.car.market.job.domain.config.CommonCityQueryConfig
import com.ctrip.car.market.job.domain.proxy.CarCrossRecommendedServiceClientProxy
import com.ctrip.car.market.job.repository.dao.CarKalabCityDao
import com.ctrip.car.market.job.repository.dao.PkgRealtimedataDao
import com.ctrip.car.market.job.repository.entity.CarKalabCity
import com.ctrip.framework.clogging.agent.log.ILog
import org.springframework.test.util.ReflectionTestUtils
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import java.sql.Timestamp

import static org.mockito.Mockito.*

class ProductManagerServiceTest extends Specification {
    @Mock
    ILog log
    @Mock
    CarCrossRecommendedServiceClientProxy carCrossRecommendedServiceClientProxy
    @Mock
    PkgRealtimedataDao pkgRealtimedataDao
    @Mock
    CarKalabCityDao carKalabCityDao
    @InjectMocks
    ProductManagerService productManagerService

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "start where cityQueryConfigList=#cityQueryConfigList then expect: #expectedResult"() {
        given:
        when(carCrossRecommendedServiceClientProxy.queryProductByRequest(any())).thenReturn([new RecommendProductDTO("productImg", "productName", "subProductName", "h5Url", "appUrl", "pcUrl", "weChatUrl", "alipayUrl", ["serviceLabels"], ["marketLabels"], 0 as BigDecimal, "cornerMark", 0 as BigDecimal, ["productLabels"], ["activityLabels"], 0, 0 as BigDecimal, 0 as BigDecimal, "productId", 0, "trans", "seat", "doorNo", "luggageNo", "vehicleLabel", "vendorLogo", "vendorScore", "vendorName", "cityName", "productCategoryName", 0 as BigDecimal, "vehicleGroupName", "productShortName", "storeName", "brandName", 1l, 1l, 1l, "vehicleGroupCode", "fuel", "displacement", "takeWay")])
        when(pkgRealtimedataDao.batchInsert(any())).thenReturn([0] as int[])
        when(carKalabCityDao.queryBy(any())).thenReturn([new CarKalabCity(cityId: 1l, tripValidStatus: 0, ctripValidStatus: 0, minPrice: 0, ctripAppUrl: "ctripAppUrl", ctripH5Url: "ctripH5Url", datachangeLasttime: new Timestamp(0, 0, 0, 0, 0, 0, 0))])
        when(carKalabCityDao.batchUpdate(any())).thenReturn([0] as int[])

        expect:
        productManagerService.start(cityQueryConfigList) == expectedResult

        where:
        cityQueryConfigList                                                     || expectedResult
        []                                                                      || false
        null                                                                    || false
        [new CommonCityQueryConfig(pickUpCityIds: null)]                        || false
        [new CommonCityQueryConfig(pickUpCityIds: "1,2")]                       || true
        [new CommonCityQueryConfig(pickUpCityIds: "1,2", returnCityIds: "2,1")] || true
    }


    @Unroll
    def "Update City Price where cityId=#cityId and info2=#info2 then expect: #expectedResult"() {
        given:
        when(carKalabCityDao.queryBy(any())).thenReturn([new CarKalabCity(cityId: 1l, tripValidStatus: 0, ctripValidStatus: 0, minPrice: 0, ctripAppUrl: "ctripAppUrl", ctripH5Url: "ctripH5Url", datachangeLasttime: new Timestamp(0, 0, 0, 0, 0, 0, 0))])
        when(carKalabCityDao.batchUpdate(any())).thenReturn([0] as int[])

        expect:
        productManagerService.UpdateCityPrice(cityId, info2) == expectedResult

        where:
        cityId | info2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     || expectedResult
        1l     | new RecommendProductDTO("productImg", "productName", "subProductName", "h5Url", "appUrl", "pcUrl", "weChatUrl", "alipayUrl", ["serviceLabels"], ["marketLabels"], 0 as BigDecimal, "cornerMark", 0 as BigDecimal, ["productLabels"], ["activityLabels"], 0, 0 as BigDecimal, 0 as BigDecimal, "productId", 0, "trans", "seat", "doorNo", "luggageNo", "vehicleLabel", "vendorLogo", "vendorScore", "vendorName", "cityName", "productCategoryName", 0 as BigDecimal, "vehicleGroupName", "productShortName", "storeName", "brandName", 1l, 1l, 1l, "vehicleGroupCode", "fuel", "displacement", "takeWay") || 1
    }

    @Unroll
    def "test buildCarRecommendProductRequestType"(){
        given:
        CommonCityQueryConfig commonCityQueryConfig1 = new CommonCityQueryConfig(cacheMinutes: 1, pickupDate: "2024-09-10 10:00:00", rentDays: 1, businessType: 35, vehicleGroupIds: "88")
        when:
        def method = ReflectionTestUtils.invokeMethod(productManagerService, "buildCarRecommendProductRequestType", commonCityQueryConfig1, 2l, 2l)
        then:
        method != null
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme