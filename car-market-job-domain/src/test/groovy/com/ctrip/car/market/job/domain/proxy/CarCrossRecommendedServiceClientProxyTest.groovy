package com.ctrip.car.market.job.domain.proxy

import com.ctrip.car.market.crossrecommend.service.contract.CarCrossRecommendedServiceClient
import com.ctrip.car.market.crossrecommend.service.contract.dto.RecommendProductDTO
import com.ctrip.car.market.crossrecommend.service.contract.servicetype.CarRecommendProductRequestType
import com.ctrip.car.market.crossrecommend.service.contract.servicetype.CarRecommendProductResponseType
import com.ctrip.car.market.job.domain.service.UGCDataManager
import com.ctrip.framework.clogging.agent.log.ILog
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class CarCrossRecommendedServiceClientProxyTest extends Specification {
    @Mock
    ILog log
    @Mock
    UGCDataManager ugcDataManager
    @Mock
    CarCrossRecommendedServiceClient client
    @InjectMocks
    CarCrossRecommendedServiceClientProxy carCrossRecommendedServiceClientProxy

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "test query Product By Request  response null"() {
        given:
        when(client.carRecommendProduct(any())).thenReturn(null)

        when:
        List<RecommendProductDTO> result = carCrossRecommendedServiceClientProxy.queryProductByRequest(new CarRecommendProductRequestType())

        then:
        result == null
    }
    @Unroll
    def "test query Product By Request  response result null"() {
        given:
        when(client.carRecommendProduct(any())).thenReturn(new CarRecommendProductResponseType(recommendProducts: null))

        when:
        List<RecommendProductDTO> result = carCrossRecommendedServiceClientProxy.queryProductByRequest(new CarRecommendProductRequestType())

        then:
        result == null
    }
    @Unroll
    def "test query Product By Request throw"() {
        given:
        when(client.carRecommendProduct(any())).thenThrow(new Exception())
        when:
        List<RecommendProductDTO> result = carCrossRecommendedServiceClientProxy.queryProductByRequest(new CarRecommendProductRequestType())

        then:
        result == null
    }
    @Unroll
    def "test query Product By Request  succ"() {
        given:
        when(client.carRecommendProduct(any())).thenReturn(new CarRecommendProductResponseType(recommendProducts: [new RecommendProductDTO(productId: 1l)]))

        when:
        List<RecommendProductDTO> result = carCrossRecommendedServiceClientProxy.queryProductByRequest(new CarRecommendProductRequestType( pickUpCityId: 1l))

        then:
        result == [new RecommendProductDTO(productId: 1l)]
    }

}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme