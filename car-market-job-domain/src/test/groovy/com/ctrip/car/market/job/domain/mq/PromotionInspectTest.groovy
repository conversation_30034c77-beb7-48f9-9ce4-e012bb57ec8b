package com.ctrip.car.market.job.domain.mq

import com.ctrip.arch.canal.ColumnData
import com.ctrip.arch.canal.DataChange
import com.ctrip.car.market.job.domain.config.JobConfig
import com.ctrip.car.market.job.domain.dto.LimitingConditions
import com.ctrip.car.market.job.domain.proxy.PromotionProxy
import com.ctrip.car.market.job.domain.utils.JsonUtil
import com.ctrip.car.market.job.repository.entity.CpnRestrictedCondition
import com.ctrip.car.market.job.repository.service.RestrictedService
import com.ctrip.soa.platform.account.promocodeservice.data.v1.PromotionStrategyItem
import com.ctrip.soa.platform.account.promocodeservice.message.v1.GetPromotionStrategyResponseType
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

class PromotionInspectTest extends Specification {

    def restrictedService = Mock(RestrictedService)

    def promotionProxy = Mock(PromotionProxy)

    def jobConfig = Mock(JobConfig)

    def testInstance = new PromotionInspect(
            restrictedService: restrictedService,
            promotionProxy: promotionProxy,
            jobConfig: jobConfig
    )

    def test_message() {
        BaseMessage message = new BaseMessage()
        DataChange dc = new DataChange()
        dc.setEventType("INSERT")
        dc.setTableName("cpn_restricted_condition")
        dc.setAfterColumnList([new ColumnData(name: "conditionId", value: "1")])
        message.setProperty("dataChange", JsonUtil.toJSONString(dc))

        given:
        restrictedService.queryRestrictedCondition(_ as Integer) >> new CpnRestrictedCondition(promotionId: 1, conditionName: "NEW", isValid: 1, content: JsonUtil.toJSONString(new LimitingConditions(isISD: true)))

        promotionProxy.getPromotionStrategy(_ as Integer) >> new GetPromotionStrategyResponseType(promotionStrategy: new PromotionStrategyItem(userProductLineIDs: "18"))

        jobConfig.isSendEmail() >> false

        expect:
        testInstance.changeMessage(message)
    }
}
