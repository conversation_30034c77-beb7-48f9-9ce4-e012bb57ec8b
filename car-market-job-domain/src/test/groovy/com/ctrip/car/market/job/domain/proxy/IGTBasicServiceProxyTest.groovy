package com.ctrip.car.market.job.domain.proxy

import com.ctrip.igt.basicservice.interfaces.IGTBasicServiceClient
import com.ctrip.igt.basicservice.interfaces.dto.BasicAirportDTO
import com.ctrip.igt.basicservice.interfaces.message.BasicAirportRequestType
import com.ctrip.igt.basicservice.interfaces.message.BasicAirportResponseType
import spock.lang.*

class IGTBasicServiceProxyTest extends Specification {

    def client = Mock(IGTBasicServiceClient)
    def iGTBasicServiceProxy = new IGTBasicServiceProxy(client: client)

    def "test get Poi Id By Poi Code"() {
        when:
        client.basicAirportQuery(_ as BasicAirportRequestType) >>> [null, new Exception(), new BasicAirportResponseType(result: new ArrayList<BasicAirportDTO>()), new BasicAirportResponseType(result: Arrays.asList(new BasicAirportDTO(code: "code")))]
        List<BasicAirportDTO> result1 = iGTBasicServiceProxy.basicAirportQuery(["poiCode"])
        List<BasicAirportDTO> result2 = iGTBasicServiceProxy.basicAirportQuery(["poiCode"])
        List<BasicAirportDTO> result3 = iGTBasicServiceProxy.basicAirportQuery(["poiCode"])
        List<BasicAirportDTO> result4 = iGTBasicServiceProxy.basicAirportQuery(["poiCode"])

        then:
        result1 == new ArrayList()
        result2 == new ArrayList()
        result3 == new ArrayList()
        result4 == [new BasicAirportDTO(code: "code")]

        }
}


//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme