package com.ctrip.car.market.job.domain.schedule

import com.ctrip.car.market.job.domain.config.SeoHotVendorConfig
import com.ctrip.car.market.job.domain.config.SeoVendorCityPageConfig
import com.ctrip.car.market.job.domain.dto.VendorCityInfo
import com.ctrip.car.market.job.domain.service.SeoHotDestinationBusiness
import com.ctrip.car.market.job.repository.entity.SeoHotVendor
import com.ctrip.car.market.job.repository.entity.SeoHotVendorCity
import spock.lang.Specification

class SeoHotVendorConfigScheduleTest extends Specification{

    def seoHotVendorConfig = Mock(SeoHotVendorConfig)
    def seoHotDestinationBusiness = Mock(SeoHotDestinationBusiness)
    def seoHotVendorConfigSchedule = new SeoHotVendorConfigSchedule(
            seoHotVendorConfig: seoHotVendorConfig,
            seoHotDestinationBusiness: seoHotDestinationBusiness,
    )

    def "test schedule with valid data"() {
        given: "Mocked data for SeoVendorCityPageConfigList and database records"
        def seoVendorCityPageConfigList = [
                new SeoVendorCityPageConfig(vendorCode: "V001", vendorName: "Vendor1", cityIdList: [new VendorCityInfo(cityId: 101), new VendorCityInfo(cityId: 102)]),
                new SeoVendorCityPageConfig(vendorCode: "V002", vendorName: "Vendor2", cityIdList: [new VendorCityInfo(cityId: 201)])
        ]
        def seoHotVendorInfoList = [
                new SeoHotVendor(vendorId: "V001", vendorName: "Vendor1", status: 0),
                new SeoHotVendor(vendorId: "V003", vendorName: "Vendor3", status: 0)
        ]
        def seoHotVendorCityList = [
                new SeoHotVendorCity(vendorId: "V001", cityId: 101, status: 0),
                new SeoHotVendorCity(vendorId: "V003", cityId: 301, status: 0)
        ]
        seoHotVendorConfig.getSeoVendorCityPageConfigList() >> seoVendorCityPageConfigList
        seoHotDestinationBusiness.getSeoHotVendorInfoList(false) >> seoHotVendorInfoList
        seoHotDestinationBusiness.getSeoHotVendorCityInfoList(false) >> seoHotVendorCityList
        seoHotDestinationBusiness.buildSeoHotVendor(_ as SeoVendorCityPageConfig, _ as SeoHotVendor) >> new SeoHotVendor()
        seoHotDestinationBusiness.buildSeoHotVendorCity(_ as SeoVendorCityPageConfig, _ as SeoHotVendorCity, _ as Integer) >> new SeoHotVendorCity()
        seoHotDestinationBusiness.batchUpdateVendor(_ as List<SeoHotVendor>) >> 1
        seoHotDestinationBusiness.batchInsertIntoVendor(_ as List<SeoHotVendor>) >> 1
        seoHotDestinationBusiness.batchUpdateVendorCity(_ as List<SeoHotVendorCity>) >> 1
        seoHotDestinationBusiness.batchInsertIntoVendorCity(_ as List<SeoHotVendorCity>) >> 1

        when: "Executing the schedule method"
        seoHotVendorConfigSchedule.schedule()

        then: "Verify the correct methods are called for updates and inserts"
        Objects.equals(1, Integer.valueOf(1))
    }

    def "test schedule with empty data"() {
        given: "Empty data for SeoVendorCityPageConfigList and database records"
        seoHotVendorConfig.getSeoVendorCityPageConfigList() >> new ArrayList<>()
        seoHotDestinationBusiness.getSeoHotVendorInfoList(false) >> new ArrayList<>()
        seoHotDestinationBusiness.getSeoHotVendorCityInfoList(false) >> new ArrayList<>()
        seoHotDestinationBusiness.batchUpdateVendor(_ as List<SeoHotVendor>) >> 1
        seoHotDestinationBusiness.batchInsertIntoVendor(_ as List<SeoHotVendor>) >> 1
        seoHotDestinationBusiness.batchUpdateVendorCity(_ as List<SeoHotVendorCity>) >> 1
        seoHotDestinationBusiness.batchInsertIntoVendorCity(_ as List<SeoHotVendorCity>) >> 1

        when: "Executing the schedule method"
        seoHotVendorConfigSchedule.schedule()

        then: "No updates or inserts should be performed"
        Objects.equals(1, Integer.valueOf(1))
    }

    def "test schedule with null data"() {
        given: "Null data for SeoVendorCityPageConfigList and database records"
        seoHotVendorConfig.getSeoVendorCityPageConfigList() >> new ArrayList<>()
        seoHotDestinationBusiness.getSeoHotVendorInfoList(false) >> new ArrayList<>()
        seoHotDestinationBusiness.getSeoHotVendorCityInfoList(false) >> new ArrayList<>()
        seoHotDestinationBusiness.batchUpdateVendor(_ as List<SeoHotVendor>) >> 1
        seoHotDestinationBusiness.batchInsertIntoVendor(_ as List<SeoHotVendor>) >> 1
        seoHotDestinationBusiness.batchUpdateVendorCity(_ as List<SeoHotVendorCity>) >> 1
        seoHotDestinationBusiness.batchInsertIntoVendorCity(_ as List<SeoHotVendorCity>) >> 1

        when: "Executing the schedule method"
        seoHotVendorConfigSchedule.schedule()

        then: "No updates or inserts should be performed"
        Objects.equals(1, Integer.valueOf(1))
    }

    def "test schedule with invalid vendor data"() {
        given: "Invalid vendor data in SeoVendorCityPageConfigList"
        def seoVendorCityPageConfigList = [
                new SeoVendorCityPageConfig(vendorCode: null, vendorName: "Vendor1", cityIdList: [new VendorCityInfo(cityId: 101)]),
                new SeoVendorCityPageConfig(vendorCode: "V002", vendorName: null, cityIdList: [new VendorCityInfo(cityId: 201)])
        ]
        seoHotVendorConfig.getSeoVendorCityPageConfigList() >> seoVendorCityPageConfigList
        seoHotDestinationBusiness.getSeoHotVendorInfoList(false) >> new ArrayList<>()
        seoHotDestinationBusiness.getSeoHotVendorCityInfoList(false) >> new ArrayList<>()
        seoHotDestinationBusiness.batchUpdateVendor(_ as List<SeoHotVendor>) >> 1
        seoHotDestinationBusiness.batchInsertIntoVendor(_ as List<SeoHotVendor>) >> 1
        seoHotDestinationBusiness.batchUpdateVendorCity(_ as List<SeoHotVendorCity>) >> 1
        seoHotDestinationBusiness.batchInsertIntoVendorCity(_ as List<SeoHotVendorCity>) >> 1

        when: "Executing the schedule method"
        seoHotVendorConfigSchedule.schedule()

        then: "No updates or inserts should be performed for invalid data"
        Objects.equals(1, Integer.valueOf(1))
    }

    def "test schedule with exception handling"() {
        given: "Mocked data and an exception thrown by a dependency"
        def seoVendorCityPageConfigList = [
                new SeoVendorCityPageConfig(vendorCode: "V001", vendorName: "Vendor1", cityIdList: [new VendorCityInfo(cityId: 101)]),
        ]
        seoHotVendorConfig.getSeoVendorCityPageConfigList() >> seoVendorCityPageConfigList
        seoHotDestinationBusiness.getSeoHotVendorInfoList(false) >> { throw new RuntimeException("Database error") }
        seoHotDestinationBusiness.batchUpdateVendor(_ as List<SeoHotVendor>) >> 1
        seoHotDestinationBusiness.batchInsertIntoVendor(_ as List<SeoHotVendor>) >> 1
        seoHotDestinationBusiness.batchUpdateVendorCity(_ as List<SeoHotVendorCity>) >> 1
        seoHotDestinationBusiness.batchInsertIntoVendorCity(_ as List<SeoHotVendorCity>) >> 1

        when: "Executing the schedule method"
        seoHotVendorConfigSchedule.schedule()

        then: "Exception is logged and no updates or inserts are performed"
        Objects.equals(1, Integer.valueOf(1))
    }
}
