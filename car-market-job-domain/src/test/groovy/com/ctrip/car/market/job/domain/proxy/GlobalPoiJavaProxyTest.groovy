package com.ctrip.car.market.job.domain.proxy

import com.ctrip.gs.globalpoi.soa.contract.BizCodeEnum
import com.ctrip.gs.globalpoi.soa.contract.GetPoiIdsByBizCodeRequestType
import com.ctrip.gs.globalpoi.soa.contract.GetPoiIdsByBizCodeResponseType
import com.ctrip.gs.globalpoi.soa.contract.GlobalPoiJavaClient
import spock.lang.*

class GlobalPoiJavaProxyTest extends Specification {

    def client = Mock(GlobalPoiJavaClient)
    def globalPoiJavaProxy = new GlobalPoiJavaProxy(client: client)

    def "test get Poi Id By Poi Code"() {
        when:
        client.getPoiIdsByBizCode(_ as GetPoiIdsByBizCodeRequestType) >>> [null, new Exception(), new GetPoiIdsByBizCodeResponseType(result: new HashMap<String, Long>()), new GetPoiIdsByBizCodeResponseType(result: new HashMap<String, Long>(){{put("1",1l)}})]
        Map<String, Long> result1 = globalPoiJavaProxy.getPoiIdByPoiCode(["poiCode"], BizCodeEnum.THREECODE_AIRPORT)
        Map<String, Long> result2 = globalPoiJavaProxy.getPoiIdByPoiCode(["poiCode"], BizCodeEnum.THREECODE_AIRPORT)
        Map<String, Long> result3 = globalPoiJavaProxy.getPoiIdByPoiCode(["poiCode"], BizCodeEnum.THREECODE_AIRPORT)
        Map<String, Long> result4 = globalPoiJavaProxy.getPoiIdByPoiCode(["1"], BizCodeEnum.THREECODE_AIRPORT)

        then:
        result1 == new HashMap()
        result2 == new HashMap()
        result3 == new HashMap()
        result4 == new HashMap(){{put("1",1l)}}
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme