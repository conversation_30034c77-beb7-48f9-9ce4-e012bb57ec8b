package com.ctrip.car.market.job.domain.proxy

import com.ctrip.car.customer.common.util.LogUtil
import com.ctrip.car.market.job.domain.config.CarServiceConfig
import com.ctrip.car.market.job.domain.utils.RedisUtil
import com.ctrip.car.osd.translate.dto.TranslateRequestInfo
import com.ctrip.car.osd.translate.dto.TranslateRequestType
import com.ctrip.car.osd.translate.dto.TranslateResponseInfo
import com.ctrip.car.osd.translate.dto.TranslateResponseType
import com.ctrip.car.osd.translate.dto.TranslateResultInfo
import com.ctrip.car.osd.translate.dto.TranslateType
import com.ctrip.car.osd.translate.methodtype.CarTranslateServiceClient
import com.ctrip.framework.clogging.agent.log.ILog
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import static org.mockito.Mockito.*


/*@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)*/
@PrepareForTest([RedisUtil.class])
@SuppressStaticInitializationFor([
        "com.ctrip.framework.clogging.agent.log.LogManager",
        "com.ctrip.car.market.job.domain.utils.RedisUtil",

])
@PowerMockIgnore(["jdk.internal.reflect.*", "javax.xml.*", "org.xml.*", "org.w3c.*", "java.lang.LinkageError", "com.sun.org.*", "javax.net.ssl.*", "javax.management.*", "javax.crypto.*"])
class CarTranslateProxyTest extends Specification {

    @Mock
    LogUtil log
    @Mock
    CarTranslateServiceClient client

    @Mock
    CarServiceConfig carServiceConfig;

    @InjectMocks
    CarTranslateProxy carTranslateProxy

    def setupSpec() {
        PowerMockito.mockStatic(RedisUtil.class)
        ILog log = Mock(ILog.class)

    }

    def setup() {
        PowerMockito.mockStatic(RedisUtil.class)
    }

    @Unroll
    def "translate where request=#request then expect: #expectedResult null"() {
        given:
        when(client.translate(request)).thenReturn(expectedResult)
        expect:
        carTranslateProxy.translate(request) == expectedResult

        where:
        request || expectedResult
        null    || null
    }

    @Unroll
    def "get Translate Result By Content Cache where language=#language and content=#content then expect: #expectedResult null"() {
        given:
        when(client.translate(any())).thenReturn(null)
        when(carServiceConfig.getValueFromConfConfigByKey("SEOCONENTLANGUAGETMSKEY")).thenReturn("abd")
        PowerMockito.when(RedisUtil.getCache(anyString())).thenReturn("abc")
        expect:
        carTranslateProxy.getTranslateResultByContentCache(content, language) == expectedResult

        where:
        language   | content   || expectedResult
        "language" | "content" || "abc"
    }

    @Unroll
    def "get Translate Result By Content Cache where language=#language and content=#content then expect: #expectedResult null 2"() {
        given:
        when(client.translate(any())).thenReturn("abd")
        when(carServiceConfig.getValueFromConfConfigByKey("SEOCONENTLANGUAGETMSKEY")).thenReturn(10)
        PowerMockito.when(RedisUtil.getCache(anyString())).thenReturn("abc")
        expect:
        carTranslateProxy.getTranslateResultByContentCache(content, language) == expectedResult

        where:
        language   | content   || expectedResult
        "language" | "content" || "abc"
    }

    @Unroll
    def "get Translate Result By Content where language=#language and content=#content then expect: #expectedResult null 3"() {
        expect:
        carTranslateProxy.getTranslateResultByContent(content, language) == expectedResult

        where:
        language   | content   || expectedResult
        "language" | "content" || null
        "language" | ""        || ""
    }


    @Unroll
    def "get Translate Result By Content where language=#language and content=#content then expect: #expectedResult  "() {
        given:
        when(client.translate(any())).thenReturn(new TranslateResponseType(responseInfo: [new TranslateResponseInfo(content: "content", results: [new TranslateResultInfo(targetLanguage: "language", result: "result")])]))
        expect:
        carTranslateProxy.getTranslateResultByContent(content, language) == expectedResult

        where:
        language   | content   || expectedResult
        "language" | "content" || "result"
        "zh_CN"    | "content" || null
        "zh_CN"    | "abc"     || null
    }

    @Unroll
    def " getTranslateResultByKey  "() {
        given:
        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "en_US",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|513"

                        )]
                )

        )).thenReturn(new TranslateResponseType(responseInfo: [new TranslateResponseInfo(standardKey: "carcommoditydb|car_brand|brand_name|id|513", content: "content", results: [new TranslateResultInfo(targetLanguage: "en_US", result: "branden")])]))
        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "zh_CN",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|512"

                        )]
                )

        )).thenReturn(new TranslateResponseType(responseInfo: [new TranslateResponseInfo(standardKey: "carcommoditydb|car_brand|brand_name|id|512", content: "content", results: [new TranslateResultInfo(targetLanguage: "zh_CN", result: "brandcn")])]))

        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "zh_CN",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|516"

                        )]
                )

        )).thenReturn(null)
        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "zh_CN",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|517"

                        )]
                )

        )).thenReturn(new TranslateResponseType(responseInfo: null))

        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "zh_CN",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|518"

                        )]
                )

        )).thenReturn(new TranslateResponseType(responseInfo: [new TranslateResponseInfo(standardKey: "2222", content: "content", results: [new TranslateResultInfo(targetLanguage: "zh_CN", result: "brandcn")])]))
        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "zh_CN",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|519"

                        )]
                )

        )).thenReturn(new TranslateResponseType(responseInfo: [new TranslateResponseInfo(standardKey: "carcommoditydb|car_brand|brand_name|id|519", content: "content", results: [new TranslateResultInfo(targetLanguage: "zh_CN", result: "brandcn")])]))


        expect:
        carTranslateProxy.getTranslateResultByKey(key, language) == expectedResult

        where:
        language | key                                          || expectedResult
        "zh_CN"  | "carcommoditydb|car_brand|brand_name|id|512" || "brandcn"
        "en_US"  | "carcommoditydb|car_brand|brand_name|id|513" || "branden"
        "en_US"  | "carcommoditydb|car_brand|brand_name|id|513" || "branden"
        "en_US"  | "carcommoditydb|car_brand|brand_name|id|516" || null
        "en_US"  | "carcommoditydb|car_brand|brand_name|id|517" || null
        "en_US"  | "carcommoditydb|car_brand|brand_name|id|519" || null
        "en_US" || null                                         || ""
    }


    @Unroll
    def "getTranslateResultByKey"() {
        given:
        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "en_US",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|513"

                        )]
                )

        )).thenReturn(new TranslateResponseType(responseInfo: [new TranslateResponseInfo(standardKey: "carcommoditydb|car_brand|brand_name|id|513", content: "content", results: [new TranslateResultInfo(targetLanguage: "en_US", result: "branden")])]))
        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "zh_CN",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|512"

                        )]
                )

        )).thenReturn(new TranslateResponseType(responseInfo: [new TranslateResponseInfo(standardKey: "carcommoditydb|car_brand|brand_name|id|512", content: "content", results: [new TranslateResultInfo(targetLanguage: "zh_CN", result: "brandcn")])]))

        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "zh_CN",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|516"

                        )]
                )

        )).thenReturn(null)
        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "zh_CN",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|517"

                        )]
                )

        )).thenReturn(new TranslateResponseType(responseInfo: null))

        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "zh_CN",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|518"

                        )]
                )

        )).thenReturn(new TranslateResponseType(responseInfo: [new TranslateResponseInfo(standardKey: "2222", content: "content", results: [new TranslateResultInfo(targetLanguage: "zh_CN", result: "brandcn")])]))
        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "zh_CN",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|519"

                        )]
                )

        )).thenReturn(new TranslateResponseType(responseInfo: [new TranslateResponseInfo(standardKey: "carcommoditydb|car_brand|brand_name|id|519", content: "content", results: [new TranslateResultInfo(targetLanguage: "zh_CN", result: "brandcn")])]))


        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "zh_CN",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|520"

                        )]
                )

        )).thenReturn(new TranslateResponseType(responseInfo: [new TranslateResponseInfo(standardKey: "carcommoditydb|car_brand|brand_name|id|5201", content: "content", results: [new TranslateResultInfo(targetLanguage: "zh_CN", result: "brandcn")])]))

        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "zh_CN",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|521"

                        )]
                )

        )).thenReturn(new TranslateResponseType(responseInfo: [new TranslateResponseInfo(standardKey: "carcommoditydb|car_brand|brand_name|id|521", content: "content", results: [new TranslateResultInfo(targetLanguage: "zh_CN2", result: "brandcn")])]))
        when(client.translate(
                new TranslateRequestType(
                        buCode: "car_calabi",
                        translateType: TranslateType.tms,
                        params: [new TranslateRequestInfo(
                                sourceLanguage: null,
                                targetLanguage: "zh_CN",
                                content: null,
                                standardKey: "carcommoditydb|car_brand|brand_name|id|522"

                        )]
                )

        )).thenThrow(new Exception())

        expect:
        carTranslateProxy.getTranslateResultByKeyCache(key, language) == expectedResult

        where:
        language | key                                          || expectedResult
        "zh_CN"  | "carcommoditydb|car_brand|brand_name|id|512" || "brandcn"
        "en_US"  | "carcommoditydb|car_brand|brand_name|id|513" || "branden"
        "en_US"  | "carcommoditydb|car_brand|brand_name|id|515" || null
        "en_US"  | "carcommoditydb|car_brand|brand_name|id|516" || null
        "en_US"  | "carcommoditydb|car_brand|brand_name|id|517" || null
        "en_US"  | "carcommoditydb|car_brand|brand_name|id|519" || null
        "en_US"  | ""                                           || ""
        "zh_CN"  | "carcommoditydb|car_brand|brand_name|id|520" || null
        "zh_CN"  | "carcommoditydb|car_brand|brand_name|id|521" || null
        "zh_CN"  | "carcommoditydb|car_brand|brand_name|id|522" || null
    }
}

