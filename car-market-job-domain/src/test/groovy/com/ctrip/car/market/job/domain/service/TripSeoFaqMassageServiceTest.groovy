package com.ctrip.car.market.job.domain.service

import com.ctrip.car.market.job.domain.config.CarServiceConfig
import com.ctrip.car.market.job.domain.proxy.CarTranslateProxy
import com.ctrip.car.market.job.repository.dao.carseodb.SeoFaqDao
import com.ctrip.car.market.job.repository.entity.carseodb.SeoFaq
import com.ctrip.framework.clogging.agent.log.ILog
import qunar.tc.qmq.MessageProducer
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class TripSeoFaqMassageServiceTest extends Specification {
    @Mock
    ILog log
    @Mock
    MessageProducer messageProducer
    @Mock
    SeoFaqDao seoFaqDao
    @Mock
    CarTranslateProxy carTranslateProxy
    @Mock
    CarServiceConfig carServiceConfig
    @Mock
    List<String> languages
    @InjectMocks
    TripSeoFaqMassageService tripSeoFaqMassageService

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    @Unroll
    def "execute"() {
        given:
        when(messageProducer.generateMessage(anyString())).thenReturn(null)
        when(seoFaqDao.count(any())).thenReturn(250)
        when(seoFaqDao.queryAllByPage(anyInt(), anyInt(), any())).thenReturn([new SeoFaq(vendorsContent: "[{'vendorName':'RAKURAKU Rental Car','price':875}]", hotVendorName: "hotVendorName", hotGroupName: "hotGroupName", active: Boolean.TRUE, groupEName: "groupEName", cityId: 1l)])
        when(carTranslateProxy.getTranslateResultByContentCache(anyString(), anyString())).thenReturn("getTranslateResultByContentCacheResponse")
        when(carServiceConfig.getValueFromConfConfigByKey("sendFaqMassagePageSize")).thenReturn("100")
        when(carServiceConfig.getValueFromConfConfigByKey("tripSeoLanguages")).thenReturn("CN,EN,KR,JY,ES,IT,DE,FR,TH,HK")

        when(languages.add(anyString())).thenReturn(true)
        when(languages.stream()).thenReturn(null)

        expect:
        tripSeoFaqMassageService.execute() == expectedResult

        where:
        expectedResult << true
    }


    @Unroll
    def "execute true"() {
        given:
        when(messageProducer.generateMessage(anyString())).thenReturn(null)
        when(seoFaqDao.count(any())).thenReturn(0)
        when(seoFaqDao.queryAllByPage(anyInt(), anyInt(), any())).thenReturn([new SeoFaq(vendorsContent: "[{'vendorName':'RAKURAKU Rental Car','price':875}]", hotVendorName: "hotVendorName", hotGroupName: "hotGroupName", active: Boolean.FALSE, groupEName: "groupEName", cityId: 1l)])
        when(carTranslateProxy.getTranslateResultByContentCache(anyString(), anyString())).thenReturn("getTranslateResultByContentCacheResponse")
        when(carServiceConfig.getValueFromConfConfigByKey("sendFaqMassagePageSize")).thenReturn("100")
        when(carServiceConfig.getValueFromConfConfigByKey("tripSeoLanguages")).thenReturn("CN,EN,KR,JY,ES,IT,DE,FR,TH,HK")

        when(languages.add(anyString())).thenReturn(true)
        when(languages.stream()).thenReturn(null)

        expect:
        tripSeoFaqMassageService.execute() == expectedResult

        where:
        expectedResult << true
    }


    @Unroll
    def "execute no vendorcontent"() {
        given:
        when(messageProducer.generateMessage(anyString())).thenReturn(null)
        when(seoFaqDao.count(any())).thenReturn(0)
        when(seoFaqDao.queryAllByPage(anyInt(), anyInt(), any())).thenReturn([new SeoFaq(vendorsContent: "", hotVendorName: "hotVendorName", hotGroupName: "hotGroupName", active: Boolean.TRUE, groupEName: "groupEName", cityId: 1l)])
        when(carTranslateProxy.getTranslateResultByContentCache(anyString(), anyString())).thenReturn("getTranslateResultByContentCacheResponse")
        when(carServiceConfig.getValueFromConfConfigByKey("sendFaqMassagePageSize")).thenReturn("")
        when(carServiceConfig.getValueFromConfConfigByKey("tripSeoLanguages")).thenReturn("")

        when(languages.add(anyString())).thenReturn(true)
        when(languages.stream()).thenReturn(null)

        expect:
        tripSeoFaqMassageService.execute() == expectedResult

        where:
        expectedResult << true
    }
    @Unroll
    def "execute exception false "() {
        given:
        when(messageProducer.generateMessage(anyString())).thenReturn(null)
        when(seoFaqDao.count(any())).thenReturn(0)
        when(seoFaqDao.queryAllByPage(anyInt(), anyInt(), any())).thenReturn([new SeoFaq(vendorsContent: "", hotVendorName: "hotVendorName", hotGroupName: "hotGroupName", active: Boolean.TRUE, groupEName: "groupEName", cityId: 1l)])
        when(carTranslateProxy.getTranslateResultByContentCache(anyString(), anyString())).thenReturn("23")
        when(carServiceConfig.getValueFromConfConfigByKey("sendFaqMassagePageSize")).thenReturn("kkk")
        when(carServiceConfig.getValueFromConfConfigByKey("tripSeoLanguages")).thenReturn("")

        when(languages.add(anyString())).thenReturn(true)
        when(languages.stream()).thenReturn(null)

        expect:
        tripSeoFaqMassageService.execute() == expectedResult

        where:
        expectedResult << false
    }
}

