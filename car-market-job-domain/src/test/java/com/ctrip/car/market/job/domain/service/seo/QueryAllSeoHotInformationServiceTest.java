package com.ctrip.car.market.job.domain.service.seo;

import com.ctrip.car.market.job.common.entity.seo.SeoHotInformationDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotInformationMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotInformation;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllSeoHotInformationServiceTest {

    @Mock
    private MarketDBService mockService;
    @Mock
    private SeoHotInformationMapper mockMapper;

    @InjectMocks
    private QueryAllSeoHotInformationService queryAllSeoHotInformationServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure MarketDBService.queryALLInformation(...).
        final SeoHotInformation seoHotInformation = new SeoHotInformation();
        seoHotInformation.setId(0L);
        seoHotInformation.setPoiType(0);
        seoHotInformation.setPoiCode("poiCode");
        seoHotInformation.setCountryId(0);
        seoHotInformation.setCityId(0);
        final List<SeoHotInformation> seoHotInformations = Arrays.asList(seoHotInformation);
        when(mockService.queryALLInformation()).thenReturn(seoHotInformations);

        // Configure SeoHotInformationMapper.to(...).
        final SeoHotInformationDO seoHotInformationDO = new SeoHotInformationDO();
        seoHotInformationDO.setId(0L);
        seoHotInformationDO.setPoiType(0);
        seoHotInformationDO.setPoiCode("poiCode");
        seoHotInformationDO.setCountryId(0);
        seoHotInformationDO.setCityId(0);
        when(mockMapper.to(any(SeoHotInformation.class))).thenReturn(seoHotInformationDO);

        // Run the test
        final List<SeoHotInformationDO> result = queryAllSeoHotInformationServiceUnderTest.load("area", "key");

        // Verify the results
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_MarketDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryALLInformation()).thenReturn(Collections.emptyList());

        // Run the test
        final List<SeoHotInformationDO> result = queryAllSeoHotInformationServiceUnderTest.load("area", "key");

        // Verify the results
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_MarketDBServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryALLInformation()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllSeoHotInformationServiceUnderTest.load("area", "key"))
                .isInstanceOf(SQLException.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure MarketDBService.queryALLInformation(...).
        final SeoHotInformation seoHotInformation = new SeoHotInformation();
        seoHotInformation.setId(0L);
        seoHotInformation.setPoiType(0);
        seoHotInformation.setPoiCode("poiCode");
        seoHotInformation.setCountryId(0);
        seoHotInformation.setCityId(0);
        final List<SeoHotInformation> seoHotInformations = Arrays.asList(seoHotInformation);
        when(mockService.queryALLInformation()).thenReturn(seoHotInformations);

        // Configure SeoHotInformationMapper.to(...).
        final SeoHotInformationDO seoHotInformationDO = new SeoHotInformationDO();
        seoHotInformationDO.setId(0L);
        seoHotInformationDO.setPoiType(0);
        seoHotInformationDO.setPoiCode("poiCode");
        seoHotInformationDO.setCountryId(0);
        seoHotInformationDO.setCityId(0);
        when(mockMapper.to(any(SeoHotInformation.class))).thenReturn(seoHotInformationDO);

        // Run the test
        final Map<String, List<SeoHotInformationDO>> result = queryAllSeoHotInformationServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")));

        // Verify the results
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void testLoadAll_MarketDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryALLInformation()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, List<SeoHotInformationDO>> result = queryAllSeoHotInformationServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")));

        // Verify the results
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void testLoadAll_MarketDBServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryALLInformation()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllSeoHotInformationServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")))).isInstanceOf(SQLException.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure MarketDBService.queryALLInformation(...).
        final SeoHotInformation seoHotInformation = new SeoHotInformation();
        seoHotInformation.setId(0L);
        seoHotInformation.setPoiType(0);
        seoHotInformation.setPoiCode("poiCode");
        seoHotInformation.setCountryId(0);
        seoHotInformation.setCityId(0);
        final List<SeoHotInformation> seoHotInformations = Arrays.asList(seoHotInformation);
        when(mockService.queryALLInformation()).thenReturn(seoHotInformations);

        // Configure SeoHotInformationMapper.to(...).
        final SeoHotInformationDO seoHotInformationDO = new SeoHotInformationDO();
        seoHotInformationDO.setId(0L);
        seoHotInformationDO.setPoiType(0);
        seoHotInformationDO.setPoiCode("poiCode");
        seoHotInformationDO.setCountryId(0);
        seoHotInformationDO.setCityId(0);
        when(mockMapper.to(any(SeoHotInformation.class))).thenReturn(seoHotInformationDO);

        // Run the test
        final Map<String, List<SeoHotInformationDO>> result = queryAllSeoHotInformationServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.assertTrue(Objects.nonNull(result));
        // Verify the results
    }

    @Test
    public void testPreLoad_MarketDBServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryALLInformation()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<String, List<SeoHotInformationDO>> result = queryAllSeoHotInformationServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void testPreLoad_MarketDBServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryALLInformation()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllSeoHotInformationServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
