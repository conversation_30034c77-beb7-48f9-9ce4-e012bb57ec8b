package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.repository.entity.activity.ActCtripactinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllActivityTempMappingServiceTest {

    @Mock
    private ActivityService mockService;

    @InjectMocks
    private QueryAllActivityTempMappingService queryAllActivityTempMappingServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure ActivityService.queryAllActivity(...).
        final ActCtripactinfo actCtripactinfo = new ActCtripactinfo();
        actCtripactinfo.setId(0L);
        actCtripactinfo.setTempId(0L);
        actCtripactinfo.setVendorType(0);
        actCtripactinfo.setVendorId(0L);
        actCtripactinfo.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<ActCtripactinfo> actCtripactinfos = Arrays.asList(actCtripactinfo);
        when(mockService.queryAllActivity()).thenReturn(actCtripactinfos);

        // Run the test
        final List<Long> result = queryAllActivityTempMappingServiceUnderTest.load("area", 0L);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList(0L));
    }

    @Test
    public void testLoad_ActivityServiceThrowsException() throws Throwable {
        // Setup
        when(mockService.queryAllActivity()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityTempMappingServiceUnderTest.load("area", 0L))
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll_ActivityServiceReturnsNoItems() throws Throwable {
        // Setup
        final Map<Long, List<Long>> expectedResult = new HashMap<>();
        when(mockService.queryAllActivity()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, List<Long>> result = queryAllActivityTempMappingServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testLoadAll_ActivityServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllActivity()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityTempMappingServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad_ActivityServiceReturnsNoItems() throws Exception {
        // Setup
        final Map<Long, List<Long>> expectedResult = new HashMap<>();
        when(mockService.queryAllActivity()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, List<Long>> result = queryAllActivityTempMappingServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPreLoad_ActivityServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllActivity()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityTempMappingServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
