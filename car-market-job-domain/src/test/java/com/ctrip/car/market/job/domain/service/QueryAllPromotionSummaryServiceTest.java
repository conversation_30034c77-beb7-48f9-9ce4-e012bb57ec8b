package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.CpnPromotionSummaryDO;
import com.ctrip.car.market.job.domain.mapper.PromotionSummaryMapper;
import com.ctrip.car.market.job.repository.entity.CpnPromotionSummary;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllPromotionSummaryServiceTest {

    @Mock
    private RestrictedService mockService;
    @Mock
    private PromotionSummaryMapper mockMapper;

    @InjectMocks
    private QueryAllPromotionSummaryService queryAllPromotionSummaryServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllPromotionSummary(...).
        final CpnPromotionSummary cpnPromotionSummary = new CpnPromotionSummary();
        cpnPromotionSummary.setSummaryID(0L);
        cpnPromotionSummary.setActivityName("activityName");
        cpnPromotionSummary.setProjectID(0);
        cpnPromotionSummary.setProjectName("projectName");
        cpnPromotionSummary.setChannel(0);
        cpnPromotionSummary.setChannelName("channelName");
        cpnPromotionSummary.setChannelType("channelType");
        cpnPromotionSummary.setCostType(0);
        cpnPromotionSummary.setSharePercent(new BigDecimal("0.00"));
        cpnPromotionSummary.setShareOther("shareOther");
        cpnPromotionSummary.setModifyUser("modifyUser");
        cpnPromotionSummary.setIsValid(0);
        cpnPromotionSummary.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummary.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummary.setActivityID(0);
        final List<CpnPromotionSummary> cpnPromotionSummaries = Arrays.asList(cpnPromotionSummary);
        when(mockService.queryAllPromotionSummary()).thenReturn(cpnPromotionSummaries);

        // Configure PromotionSummaryMapper.to(...).
        final CpnPromotionSummaryDO cpnPromotionSummaryDO = new CpnPromotionSummaryDO();
        cpnPromotionSummaryDO.setSummaryID(0L);
        cpnPromotionSummaryDO.setActivityName("activityName");
        cpnPromotionSummaryDO.setProjectID(0);
        cpnPromotionSummaryDO.setProjectName("projectName");
        cpnPromotionSummaryDO.setChannel(0);
        cpnPromotionSummaryDO.setChannelName("channelName");
        cpnPromotionSummaryDO.setChannelType("channelType");
        cpnPromotionSummaryDO.setCostType(0);
        cpnPromotionSummaryDO.setSharePercent(new BigDecimal("0.00"));
        cpnPromotionSummaryDO.setShareOther("shareOther");
        cpnPromotionSummaryDO.setModifyUser("modifyUser");
        cpnPromotionSummaryDO.setIsValid(0);
        cpnPromotionSummaryDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummaryDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummaryDO.setActivityID(0);
        when(mockMapper.to(any(CpnPromotionSummary.class))).thenReturn(cpnPromotionSummaryDO);

        // Run the test
        final List<CpnPromotionSummaryDO> result = queryAllPromotionSummaryServiceUnderTest.load("area", 0);
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllPromotionSummary()).thenReturn(Collections.emptyList());

        // Configure PromotionSummaryMapper.to(...).
        final CpnPromotionSummaryDO cpnPromotionSummaryDO = new CpnPromotionSummaryDO();
        cpnPromotionSummaryDO.setSummaryID(0L);
        cpnPromotionSummaryDO.setActivityName("activityName");
        cpnPromotionSummaryDO.setProjectID(0);
        cpnPromotionSummaryDO.setProjectName("projectName");
        cpnPromotionSummaryDO.setChannel(0);
        cpnPromotionSummaryDO.setChannelName("channelName");
        cpnPromotionSummaryDO.setChannelType("channelType");
        cpnPromotionSummaryDO.setCostType(0);
        cpnPromotionSummaryDO.setSharePercent(new BigDecimal("0.00"));
        cpnPromotionSummaryDO.setShareOther("shareOther");
        cpnPromotionSummaryDO.setModifyUser("modifyUser");
        cpnPromotionSummaryDO.setIsValid(0);
        cpnPromotionSummaryDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummaryDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummaryDO.setActivityID(0);
        when(mockMapper.to(any(CpnPromotionSummary.class))).thenReturn(cpnPromotionSummaryDO);

        // Run the test
        final List<CpnPromotionSummaryDO> result = queryAllPromotionSummaryServiceUnderTest.load("area", 0);

        // Verify the results
        Assert.isNull(result);
    }

    @Test
    public void testLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllPromotionSummary()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllPromotionSummaryServiceUnderTest.load("area", 0))
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllPromotionSummary(...).
        final CpnPromotionSummary cpnPromotionSummary = new CpnPromotionSummary();
        cpnPromotionSummary.setSummaryID(0L);
        cpnPromotionSummary.setActivityName("activityName");
        cpnPromotionSummary.setProjectID(0);
        cpnPromotionSummary.setProjectName("projectName");
        cpnPromotionSummary.setChannel(0);
        cpnPromotionSummary.setChannelName("channelName");
        cpnPromotionSummary.setChannelType("channelType");
        cpnPromotionSummary.setCostType(0);
        cpnPromotionSummary.setSharePercent(new BigDecimal("0.00"));
        cpnPromotionSummary.setShareOther("shareOther");
        cpnPromotionSummary.setModifyUser("modifyUser");
        cpnPromotionSummary.setIsValid(0);
        cpnPromotionSummary.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummary.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummary.setActivityID(0);
        final List<CpnPromotionSummary> cpnPromotionSummaries = Arrays.asList(cpnPromotionSummary);
        when(mockService.queryAllPromotionSummary()).thenReturn(cpnPromotionSummaries);

        // Configure PromotionSummaryMapper.to(...).
        final CpnPromotionSummaryDO cpnPromotionSummaryDO = new CpnPromotionSummaryDO();
        cpnPromotionSummaryDO.setSummaryID(0L);
        cpnPromotionSummaryDO.setActivityName("activityName");
        cpnPromotionSummaryDO.setProjectID(0);
        cpnPromotionSummaryDO.setProjectName("projectName");
        cpnPromotionSummaryDO.setChannel(0);
        cpnPromotionSummaryDO.setChannelName("channelName");
        cpnPromotionSummaryDO.setChannelType("channelType");
        cpnPromotionSummaryDO.setCostType(0);
        cpnPromotionSummaryDO.setSharePercent(new BigDecimal("0.00"));
        cpnPromotionSummaryDO.setShareOther("shareOther");
        cpnPromotionSummaryDO.setModifyUser("modifyUser");
        cpnPromotionSummaryDO.setIsValid(0);
        cpnPromotionSummaryDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummaryDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummaryDO.setActivityID(0);
        when(mockMapper.to(any(CpnPromotionSummary.class))).thenReturn(cpnPromotionSummaryDO);

        // Run the test
        final Map<Integer, List<CpnPromotionSummaryDO>> result = queryAllPromotionSummaryServiceUnderTest.loadAll(
                "area", new HashSet<>(Arrays.asList(0)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllPromotionSummary()).thenReturn(Collections.emptyList());

        // Configure PromotionSummaryMapper.to(...).
        final CpnPromotionSummaryDO cpnPromotionSummaryDO = new CpnPromotionSummaryDO();
        cpnPromotionSummaryDO.setSummaryID(0L);
        cpnPromotionSummaryDO.setActivityName("activityName");
        cpnPromotionSummaryDO.setProjectID(0);
        cpnPromotionSummaryDO.setProjectName("projectName");
        cpnPromotionSummaryDO.setChannel(0);
        cpnPromotionSummaryDO.setChannelName("channelName");
        cpnPromotionSummaryDO.setChannelType("channelType");
        cpnPromotionSummaryDO.setCostType(0);
        cpnPromotionSummaryDO.setSharePercent(new BigDecimal("0.00"));
        cpnPromotionSummaryDO.setShareOther("shareOther");
        cpnPromotionSummaryDO.setModifyUser("modifyUser");
        cpnPromotionSummaryDO.setIsValid(0);
        cpnPromotionSummaryDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummaryDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummaryDO.setActivityID(0);
        when(mockMapper.to(any(CpnPromotionSummary.class))).thenReturn(cpnPromotionSummaryDO);

        // Run the test
        final Map<Integer, List<CpnPromotionSummaryDO>> result = queryAllPromotionSummaryServiceUnderTest.loadAll(
                "area", new HashSet<>(Arrays.asList(0)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllPromotionSummary()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllPromotionSummaryServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0)))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure RestrictedService.queryAllPromotionSummary(...).
        final CpnPromotionSummary cpnPromotionSummary = new CpnPromotionSummary();
        cpnPromotionSummary.setSummaryID(0L);
        cpnPromotionSummary.setActivityName("activityName");
        cpnPromotionSummary.setProjectID(0);
        cpnPromotionSummary.setProjectName("projectName");
        cpnPromotionSummary.setChannel(0);
        cpnPromotionSummary.setChannelName("channelName");
        cpnPromotionSummary.setChannelType("channelType");
        cpnPromotionSummary.setCostType(0);
        cpnPromotionSummary.setSharePercent(new BigDecimal("0.00"));
        cpnPromotionSummary.setShareOther("shareOther");
        cpnPromotionSummary.setModifyUser("modifyUser");
        cpnPromotionSummary.setIsValid(0);
        cpnPromotionSummary.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummary.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummary.setActivityID(0);
        final List<CpnPromotionSummary> cpnPromotionSummaries = Arrays.asList(cpnPromotionSummary);
        when(mockService.queryAllPromotionSummary()).thenReturn(cpnPromotionSummaries);

        // Configure PromotionSummaryMapper.to(...).
        final CpnPromotionSummaryDO cpnPromotionSummaryDO = new CpnPromotionSummaryDO();
        cpnPromotionSummaryDO.setSummaryID(0L);
        cpnPromotionSummaryDO.setActivityName("activityName");
        cpnPromotionSummaryDO.setProjectID(0);
        cpnPromotionSummaryDO.setProjectName("projectName");
        cpnPromotionSummaryDO.setChannel(0);
        cpnPromotionSummaryDO.setChannelName("channelName");
        cpnPromotionSummaryDO.setChannelType("channelType");
        cpnPromotionSummaryDO.setCostType(0);
        cpnPromotionSummaryDO.setSharePercent(new BigDecimal("0.00"));
        cpnPromotionSummaryDO.setShareOther("shareOther");
        cpnPromotionSummaryDO.setModifyUser("modifyUser");
        cpnPromotionSummaryDO.setIsValid(0);
        cpnPromotionSummaryDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummaryDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummaryDO.setActivityID(0);
        when(mockMapper.to(any(CpnPromotionSummary.class))).thenReturn(cpnPromotionSummaryDO);

        // Run the test
        final Map<Integer, List<CpnPromotionSummaryDO>> result = queryAllPromotionSummaryServiceUnderTest.preLoad(
                "area", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllPromotionSummary()).thenReturn(Collections.emptyList());

        // Configure PromotionSummaryMapper.to(...).
        final CpnPromotionSummaryDO cpnPromotionSummaryDO = new CpnPromotionSummaryDO();
        cpnPromotionSummaryDO.setSummaryID(0L);
        cpnPromotionSummaryDO.setActivityName("activityName");
        cpnPromotionSummaryDO.setProjectID(0);
        cpnPromotionSummaryDO.setProjectName("projectName");
        cpnPromotionSummaryDO.setChannel(0);
        cpnPromotionSummaryDO.setChannelName("channelName");
        cpnPromotionSummaryDO.setChannelType("channelType");
        cpnPromotionSummaryDO.setCostType(0);
        cpnPromotionSummaryDO.setSharePercent(new BigDecimal("0.00"));
        cpnPromotionSummaryDO.setShareOther("shareOther");
        cpnPromotionSummaryDO.setModifyUser("modifyUser");
        cpnPromotionSummaryDO.setIsValid(0);
        cpnPromotionSummaryDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummaryDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionSummaryDO.setActivityID(0);
        when(mockMapper.to(any(CpnPromotionSummary.class))).thenReturn(cpnPromotionSummaryDO);

        // Run the test
        final Map<Integer, List<CpnPromotionSummaryDO>> result = queryAllPromotionSummaryServiceUnderTest.preLoad(
                "area", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllPromotionSummary()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllPromotionSummaryServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
