package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.GiftItemconfigDO;
import com.ctrip.car.market.job.domain.mapper.GiftItemconfigMapper;
import com.ctrip.car.market.job.repository.entity.GiftItemconfig;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllGifItemConfigServiceTest {

    @Mock
    private RestrictedService mockService;
    @Mock
    private GiftItemconfigMapper mockMapper;

    @InjectMocks
    private QueryAllGifItemConfigService queryAllGifItemConfigServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllGifItemConfig(...).
        final GiftItemconfig giftItemconfig = new GiftItemconfig();
        giftItemconfig.setItemid(0L);
        giftItemconfig.setGiftNo("giftNo");
        giftItemconfig.setIsdefault(0);
        giftItemconfig.setItemno(0);
        giftItemconfig.setPercent(0);
        giftItemconfig.setUrl("url");
        giftItemconfig.setUrlh5("urlh5");
        giftItemconfig.setPromotion(0);
        giftItemconfig.setItemremark("itemremark");
        giftItemconfig.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfig.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfig.setImageUrl("imageUrl");
        giftItemconfig.setConfig("config");
        final List<GiftItemconfig> giftItemconfigs = Arrays.asList(giftItemconfig);
        when(mockService.queryAllGifItemConfig()).thenReturn(giftItemconfigs);

        // Configure GiftItemconfigMapper.to(...).
        final GiftItemconfigDO giftItemconfigDO = new GiftItemconfigDO();
        giftItemconfigDO.setItemid(0L);
        giftItemconfigDO.setGiftNo("giftNo");
        giftItemconfigDO.setIsdefault(0);
        giftItemconfigDO.setItemno(0);
        giftItemconfigDO.setPercent(0);
        giftItemconfigDO.setUrl("url");
        giftItemconfigDO.setUrlh5("urlh5");
        giftItemconfigDO.setPromotion(0);
        giftItemconfigDO.setItemremark("itemremark");
        giftItemconfigDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfigDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfigDO.setImageUrl("imageUrl");
        giftItemconfigDO.setConfig("config");
        when(mockMapper.to(any(GiftItemconfig.class))).thenReturn(giftItemconfigDO);

        // Run the test
        final List<GiftItemconfigDO> result = queryAllGifItemConfigServiceUnderTest.load("area", "key");
        Assert.isNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllGifItemConfig()).thenReturn(Collections.emptyList());

        // Configure GiftItemconfigMapper.to(...).
        final GiftItemconfigDO giftItemconfigDO = new GiftItemconfigDO();
        giftItemconfigDO.setItemid(0L);
        giftItemconfigDO.setGiftNo("giftNo");
        giftItemconfigDO.setIsdefault(0);
        giftItemconfigDO.setItemno(0);
        giftItemconfigDO.setPercent(0);
        giftItemconfigDO.setUrl("url");
        giftItemconfigDO.setUrlh5("urlh5");
        giftItemconfigDO.setPromotion(0);
        giftItemconfigDO.setItemremark("itemremark");
        giftItemconfigDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfigDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfigDO.setImageUrl("imageUrl");
        giftItemconfigDO.setConfig("config");
        when(mockMapper.to(any(GiftItemconfig.class))).thenReturn(giftItemconfigDO);

        // Run the test
        final List<GiftItemconfigDO> result = queryAllGifItemConfigServiceUnderTest.load("area", "key");

        // Verify the results
        Assert.isNull(result);
    }

    @Test
    public void testLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllGifItemConfig()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllGifItemConfigServiceUnderTest.load("area", "key"))
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllGifItemConfig(...).
        final GiftItemconfig giftItemconfig = new GiftItemconfig();
        giftItemconfig.setItemid(0L);
        giftItemconfig.setGiftNo("giftNo");
        giftItemconfig.setIsdefault(0);
        giftItemconfig.setItemno(0);
        giftItemconfig.setPercent(0);
        giftItemconfig.setUrl("url");
        giftItemconfig.setUrlh5("urlh5");
        giftItemconfig.setPromotion(0);
        giftItemconfig.setItemremark("itemremark");
        giftItemconfig.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfig.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfig.setImageUrl("imageUrl");
        giftItemconfig.setConfig("config");
        final List<GiftItemconfig> giftItemconfigs = Arrays.asList(giftItemconfig);
        when(mockService.queryAllGifItemConfig()).thenReturn(giftItemconfigs);

        // Configure GiftItemconfigMapper.to(...).
        final GiftItemconfigDO giftItemconfigDO = new GiftItemconfigDO();
        giftItemconfigDO.setItemid(0L);
        giftItemconfigDO.setGiftNo("giftNo");
        giftItemconfigDO.setIsdefault(0);
        giftItemconfigDO.setItemno(0);
        giftItemconfigDO.setPercent(0);
        giftItemconfigDO.setUrl("url");
        giftItemconfigDO.setUrlh5("urlh5");
        giftItemconfigDO.setPromotion(0);
        giftItemconfigDO.setItemremark("itemremark");
        giftItemconfigDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfigDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfigDO.setImageUrl("imageUrl");
        giftItemconfigDO.setConfig("config");
        when(mockMapper.to(any(GiftItemconfig.class))).thenReturn(giftItemconfigDO);

        // Run the test
        final Map<String, List<GiftItemconfigDO>> result = queryAllGifItemConfigServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllGifItemConfig()).thenReturn(Collections.emptyList());

        // Configure GiftItemconfigMapper.to(...).
        final GiftItemconfigDO giftItemconfigDO = new GiftItemconfigDO();
        giftItemconfigDO.setItemid(0L);
        giftItemconfigDO.setGiftNo("giftNo");
        giftItemconfigDO.setIsdefault(0);
        giftItemconfigDO.setItemno(0);
        giftItemconfigDO.setPercent(0);
        giftItemconfigDO.setUrl("url");
        giftItemconfigDO.setUrlh5("urlh5");
        giftItemconfigDO.setPromotion(0);
        giftItemconfigDO.setItemremark("itemremark");
        giftItemconfigDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfigDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfigDO.setImageUrl("imageUrl");
        giftItemconfigDO.setConfig("config");
        when(mockMapper.to(any(GiftItemconfig.class))).thenReturn(giftItemconfigDO);

        // Run the test
        final Map<String, List<GiftItemconfigDO>> result = queryAllGifItemConfigServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllGifItemConfig()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllGifItemConfigServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure RestrictedService.queryAllGifItemConfig(...).
        final GiftItemconfig giftItemconfig = new GiftItemconfig();
        giftItemconfig.setItemid(0L);
        giftItemconfig.setGiftNo("giftNo");
        giftItemconfig.setIsdefault(0);
        giftItemconfig.setItemno(0);
        giftItemconfig.setPercent(0);
        giftItemconfig.setUrl("url");
        giftItemconfig.setUrlh5("urlh5");
        giftItemconfig.setPromotion(0);
        giftItemconfig.setItemremark("itemremark");
        giftItemconfig.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfig.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfig.setImageUrl("imageUrl");
        giftItemconfig.setConfig("config");
        final List<GiftItemconfig> giftItemconfigs = Arrays.asList(giftItemconfig);
        when(mockService.queryAllGifItemConfig()).thenReturn(giftItemconfigs);

        // Configure GiftItemconfigMapper.to(...).
        final GiftItemconfigDO giftItemconfigDO = new GiftItemconfigDO();
        giftItemconfigDO.setItemid(0L);
        giftItemconfigDO.setGiftNo("giftNo");
        giftItemconfigDO.setIsdefault(0);
        giftItemconfigDO.setItemno(0);
        giftItemconfigDO.setPercent(0);
        giftItemconfigDO.setUrl("url");
        giftItemconfigDO.setUrlh5("urlh5");
        giftItemconfigDO.setPromotion(0);
        giftItemconfigDO.setItemremark("itemremark");
        giftItemconfigDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfigDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfigDO.setImageUrl("imageUrl");
        giftItemconfigDO.setConfig("config");
        when(mockMapper.to(any(GiftItemconfig.class))).thenReturn(giftItemconfigDO);

        // Run the test
        final Map<String, List<GiftItemconfigDO>> result = queryAllGifItemConfigServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllGifItemConfig()).thenReturn(Collections.emptyList());

        // Configure GiftItemconfigMapper.to(...).
        final GiftItemconfigDO giftItemconfigDO = new GiftItemconfigDO();
        giftItemconfigDO.setItemid(0L);
        giftItemconfigDO.setGiftNo("giftNo");
        giftItemconfigDO.setIsdefault(0);
        giftItemconfigDO.setItemno(0);
        giftItemconfigDO.setPercent(0);
        giftItemconfigDO.setUrl("url");
        giftItemconfigDO.setUrlh5("urlh5");
        giftItemconfigDO.setPromotion(0);
        giftItemconfigDO.setItemremark("itemremark");
        giftItemconfigDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfigDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        giftItemconfigDO.setImageUrl("imageUrl");
        giftItemconfigDO.setConfig("config");
        when(mockMapper.to(any(GiftItemconfig.class))).thenReturn(giftItemconfigDO);

        // Run the test
        final Map<String, List<GiftItemconfigDO>> result = queryAllGifItemConfigServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllGifItemConfig()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllGifItemConfigServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
