package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.ChannelNumberDO;
import com.ctrip.car.market.job.domain.mapper.ChannelNumberMapper;
import com.ctrip.car.market.job.repository.entity.ChannelNumber;
import com.ctrip.car.market.job.repository.service.LabelService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllChannelNumberServiceTest {

    @Mock
    private LabelService mockService;
    @Mock
    private ChannelNumberMapper mockMapper;

    @InjectMocks
    private QueryAllChannelNumberService queryAllChannelNumberServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure LabelService.queryAllChannelNumber(...).
        final ChannelNumber channelNumber = new ChannelNumber();
        channelNumber.setId(0L);
        channelNumber.setChannelName("channelName");
        channelNumber.setTertiaryChannelId(0L);
        channelNumber.setCooperationId("cooperationId");
        channelNumber.setCooperationName("cooperationName");
        channelNumber.setCooperationModeId("cooperationModeId");
        channelNumber.setCooperationModeName("cooperationModeName");
        channelNumber.setPageLocationId("pageLocationId");
        channelNumber.setPageLocationName("pageLocationName");
        channelNumber.setChannelMarketId(0L);
        channelNumber.setRemark("remark");
        channelNumber.setOperatorId("operatorId");
        channelNumber.setOperator("operator");
        channelNumber.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        channelNumber.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<ChannelNumber> channelNumbers = Arrays.asList(channelNumber);
        when(mockService.queryAllChannelNumber()).thenReturn(channelNumbers);

        // Configure ChannelNumberMapper.to(...).
        final ChannelNumberDO channelNumberDO = new ChannelNumberDO();
        channelNumberDO.setId(0L);
        channelNumberDO.setChannelName("channelName");
        channelNumberDO.setTertiaryChannelId(0L);
        channelNumberDO.setCooperationId("cooperationId");
        channelNumberDO.setCooperationName("cooperationName");
        channelNumberDO.setCooperationModeId("cooperationModeId");
        channelNumberDO.setCooperationModeName("cooperationModeName");
        channelNumberDO.setPageLocationId("pageLocationId");
        channelNumberDO.setPageLocationName("pageLocationName");
        channelNumberDO.setChannelMarketId(0L);
        channelNumberDO.setRemark("remark");
        channelNumberDO.setOperatorId("operatorId");
        channelNumberDO.setOperator("operator");
        channelNumberDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        channelNumberDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ChannelNumber.class))).thenReturn(channelNumberDO);

        // Run the test
        final ChannelNumberDO result = queryAllChannelNumberServiceUnderTest.load("area", 0L);
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_LabelServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllChannelNumber()).thenReturn(Collections.emptyList());

        // Configure ChannelNumberMapper.to(...).
        final ChannelNumberDO channelNumberDO = new ChannelNumberDO();
        channelNumberDO.setId(0L);
        channelNumberDO.setChannelName("channelName");
        channelNumberDO.setTertiaryChannelId(0L);
        channelNumberDO.setCooperationId("cooperationId");
        channelNumberDO.setCooperationName("cooperationName");
        channelNumberDO.setCooperationModeId("cooperationModeId");
        channelNumberDO.setCooperationModeName("cooperationModeName");
        channelNumberDO.setPageLocationId("pageLocationId");
        channelNumberDO.setPageLocationName("pageLocationName");
        channelNumberDO.setChannelMarketId(0L);
        channelNumberDO.setRemark("remark");
        channelNumberDO.setOperatorId("operatorId");
        channelNumberDO.setOperator("operator");
        channelNumberDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        channelNumberDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ChannelNumber.class))).thenReturn(channelNumberDO);

        // Run the test
        final ChannelNumberDO result = queryAllChannelNumberServiceUnderTest.load("area", 0L);
        Assert.isNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_LabelServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllChannelNumber()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllChannelNumberServiceUnderTest.load("area", 0L)).isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure LabelService.queryAllChannelNumber(...).
        final ChannelNumber channelNumber = new ChannelNumber();
        channelNumber.setId(0L);
        channelNumber.setChannelName("channelName");
        channelNumber.setTertiaryChannelId(0L);
        channelNumber.setCooperationId("cooperationId");
        channelNumber.setCooperationName("cooperationName");
        channelNumber.setCooperationModeId("cooperationModeId");
        channelNumber.setCooperationModeName("cooperationModeName");
        channelNumber.setPageLocationId("pageLocationId");
        channelNumber.setPageLocationName("pageLocationName");
        channelNumber.setChannelMarketId(0L);
        channelNumber.setRemark("remark");
        channelNumber.setOperatorId("operatorId");
        channelNumber.setOperator("operator");
        channelNumber.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        channelNumber.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<ChannelNumber> channelNumbers = Arrays.asList(channelNumber);
        when(mockService.queryAllChannelNumber()).thenReturn(channelNumbers);

        // Configure ChannelNumberMapper.to(...).
        final ChannelNumberDO channelNumberDO = new ChannelNumberDO();
        channelNumberDO.setId(0L);
        channelNumberDO.setChannelName("channelName");
        channelNumberDO.setTertiaryChannelId(0L);
        channelNumberDO.setCooperationId("cooperationId");
        channelNumberDO.setCooperationName("cooperationName");
        channelNumberDO.setCooperationModeId("cooperationModeId");
        channelNumberDO.setCooperationModeName("cooperationModeName");
        channelNumberDO.setPageLocationId("pageLocationId");
        channelNumberDO.setPageLocationName("pageLocationName");
        channelNumberDO.setChannelMarketId(0L);
        channelNumberDO.setRemark("remark");
        channelNumberDO.setOperatorId("operatorId");
        channelNumberDO.setOperator("operator");
        channelNumberDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        channelNumberDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ChannelNumber.class))).thenReturn(channelNumberDO);

        // Run the test
        final Map<Long, ChannelNumberDO> result = queryAllChannelNumberServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_LabelServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllChannelNumber()).thenReturn(Collections.emptyList());

        // Configure ChannelNumberMapper.to(...).
        final ChannelNumberDO channelNumberDO = new ChannelNumberDO();
        channelNumberDO.setId(0L);
        channelNumberDO.setChannelName("channelName");
        channelNumberDO.setTertiaryChannelId(0L);
        channelNumberDO.setCooperationId("cooperationId");
        channelNumberDO.setCooperationName("cooperationName");
        channelNumberDO.setCooperationModeId("cooperationModeId");
        channelNumberDO.setCooperationModeName("cooperationModeName");
        channelNumberDO.setPageLocationId("pageLocationId");
        channelNumberDO.setPageLocationName("pageLocationName");
        channelNumberDO.setChannelMarketId(0L);
        channelNumberDO.setRemark("remark");
        channelNumberDO.setOperatorId("operatorId");
        channelNumberDO.setOperator("operator");
        channelNumberDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        channelNumberDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ChannelNumber.class))).thenReturn(channelNumberDO);

        // Run the test
        final Map<Long, ChannelNumberDO> result = queryAllChannelNumberServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_LabelServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllChannelNumber()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllChannelNumberServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure LabelService.queryAllChannelNumber(...).
        final ChannelNumber channelNumber = new ChannelNumber();
        channelNumber.setId(0L);
        channelNumber.setChannelName("channelName");
        channelNumber.setTertiaryChannelId(0L);
        channelNumber.setCooperationId("cooperationId");
        channelNumber.setCooperationName("cooperationName");
        channelNumber.setCooperationModeId("cooperationModeId");
        channelNumber.setCooperationModeName("cooperationModeName");
        channelNumber.setPageLocationId("pageLocationId");
        channelNumber.setPageLocationName("pageLocationName");
        channelNumber.setChannelMarketId(0L);
        channelNumber.setRemark("remark");
        channelNumber.setOperatorId("operatorId");
        channelNumber.setOperator("operator");
        channelNumber.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        channelNumber.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<ChannelNumber> channelNumbers = Arrays.asList(channelNumber);
        when(mockService.queryAllChannelNumber()).thenReturn(channelNumbers);

        // Configure ChannelNumberMapper.to(...).
        final ChannelNumberDO channelNumberDO = new ChannelNumberDO();
        channelNumberDO.setId(0L);
        channelNumberDO.setChannelName("channelName");
        channelNumberDO.setTertiaryChannelId(0L);
        channelNumberDO.setCooperationId("cooperationId");
        channelNumberDO.setCooperationName("cooperationName");
        channelNumberDO.setCooperationModeId("cooperationModeId");
        channelNumberDO.setCooperationModeName("cooperationModeName");
        channelNumberDO.setPageLocationId("pageLocationId");
        channelNumberDO.setPageLocationName("pageLocationName");
        channelNumberDO.setChannelMarketId(0L);
        channelNumberDO.setRemark("remark");
        channelNumberDO.setOperatorId("operatorId");
        channelNumberDO.setOperator("operator");
        channelNumberDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        channelNumberDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ChannelNumber.class))).thenReturn(channelNumberDO);

        // Run the test
        final Map<Long, ChannelNumberDO> result = queryAllChannelNumberServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_LabelServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllChannelNumber()).thenReturn(Collections.emptyList());

        // Configure ChannelNumberMapper.to(...).
        final ChannelNumberDO channelNumberDO = new ChannelNumberDO();
        channelNumberDO.setId(0L);
        channelNumberDO.setChannelName("channelName");
        channelNumberDO.setTertiaryChannelId(0L);
        channelNumberDO.setCooperationId("cooperationId");
        channelNumberDO.setCooperationName("cooperationName");
        channelNumberDO.setCooperationModeId("cooperationModeId");
        channelNumberDO.setCooperationModeName("cooperationModeName");
        channelNumberDO.setPageLocationId("pageLocationId");
        channelNumberDO.setPageLocationName("pageLocationName");
        channelNumberDO.setChannelMarketId(0L);
        channelNumberDO.setRemark("remark");
        channelNumberDO.setOperatorId("operatorId");
        channelNumberDO.setOperator("operator");
        channelNumberDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        channelNumberDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ChannelNumber.class))).thenReturn(channelNumberDO);

        // Run the test
        final Map<Long, ChannelNumberDO> result = queryAllChannelNumberServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_LabelServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllChannelNumber()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllChannelNumberServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
