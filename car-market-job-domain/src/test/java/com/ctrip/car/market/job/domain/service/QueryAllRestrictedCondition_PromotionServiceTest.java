package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.CpnRestrictedConditionDO;
import com.ctrip.car.market.job.domain.mapper.RestrictedConditionMapper;
import com.ctrip.car.market.job.repository.entity.CpnRestrictedCondition;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllRestrictedCondition_PromotionServiceTest {

    @Mock
    private RestrictedService mockService;
    @Mock
    private RestrictedConditionMapper mockMapper;

    @InjectMocks
    private QueryAllRestrictedCondition_PromotionService queryAllRestrictedCondition_promotionServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllRestrictedCondition(...).
        final CpnRestrictedCondition cpnRestrictedCondition = new CpnRestrictedCondition();
        cpnRestrictedCondition.setConditionId(0);
        cpnRestrictedCondition.setPromotionId(0);
        cpnRestrictedCondition.setConditionName("conditionName");
        cpnRestrictedCondition.setContent("content");
        cpnRestrictedCondition.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedCondition.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedCondition.setIsValid(0);
        cpnRestrictedCondition.setGroupId(0);
        cpnRestrictedCondition.setGroupVersion(0);
        final List<CpnRestrictedCondition> cpnRestrictedConditions = Arrays.asList(cpnRestrictedCondition);
        when(mockService.queryAllRestrictedCondition()).thenReturn(cpnRestrictedConditions);

        // Configure RestrictedConditionMapper.to(...).
        final CpnRestrictedConditionDO cpnRestrictedConditionDO = new CpnRestrictedConditionDO();
        cpnRestrictedConditionDO.setConditionId(0);
        cpnRestrictedConditionDO.setPromotionId(0);
        cpnRestrictedConditionDO.setConditionName("conditionName");
        cpnRestrictedConditionDO.setContent("content");
        cpnRestrictedConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedConditionDO.setIsValid(0);
        cpnRestrictedConditionDO.setGroupId(0);
        cpnRestrictedConditionDO.setGroupVersion(0);
        when(mockMapper.to(any(CpnRestrictedCondition.class))).thenReturn(cpnRestrictedConditionDO);

        // Run the test
        final List<CpnRestrictedConditionDO> result = queryAllRestrictedCondition_promotionServiceUnderTest.load("area",
                0);
        Assert.isNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllRestrictedCondition()).thenReturn(Collections.emptyList());

        // Configure RestrictedConditionMapper.to(...).
        final CpnRestrictedConditionDO cpnRestrictedConditionDO = new CpnRestrictedConditionDO();
        cpnRestrictedConditionDO.setConditionId(0);
        cpnRestrictedConditionDO.setPromotionId(0);
        cpnRestrictedConditionDO.setConditionName("conditionName");
        cpnRestrictedConditionDO.setContent("content");
        cpnRestrictedConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedConditionDO.setIsValid(0);
        cpnRestrictedConditionDO.setGroupId(0);
        cpnRestrictedConditionDO.setGroupVersion(0);
        when(mockMapper.to(any(CpnRestrictedCondition.class))).thenReturn(cpnRestrictedConditionDO);

        // Run the test
        final List<CpnRestrictedConditionDO> result = queryAllRestrictedCondition_promotionServiceUnderTest.load("area",
                0);

        // Verify the results
        Assert.isNull(result);
    }

    @Test
    public void testLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllRestrictedCondition()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllRestrictedCondition_promotionServiceUnderTest.load("area", 0))
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure RestrictedService.queryAllRestrictedCondition(...).
        final CpnRestrictedCondition cpnRestrictedCondition = new CpnRestrictedCondition();
        cpnRestrictedCondition.setConditionId(0);
        cpnRestrictedCondition.setPromotionId(0);
        cpnRestrictedCondition.setConditionName("conditionName");
        cpnRestrictedCondition.setContent("content");
        cpnRestrictedCondition.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedCondition.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedCondition.setIsValid(0);
        cpnRestrictedCondition.setGroupId(0);
        cpnRestrictedCondition.setGroupVersion(0);
        final List<CpnRestrictedCondition> cpnRestrictedConditions = Arrays.asList(cpnRestrictedCondition);
        when(mockService.queryAllRestrictedCondition()).thenReturn(cpnRestrictedConditions);

        // Configure RestrictedConditionMapper.to(...).
        final CpnRestrictedConditionDO cpnRestrictedConditionDO = new CpnRestrictedConditionDO();
        cpnRestrictedConditionDO.setConditionId(0);
        cpnRestrictedConditionDO.setPromotionId(0);
        cpnRestrictedConditionDO.setConditionName("conditionName");
        cpnRestrictedConditionDO.setContent("content");
        cpnRestrictedConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedConditionDO.setIsValid(0);
        cpnRestrictedConditionDO.setGroupId(0);
        cpnRestrictedConditionDO.setGroupVersion(0);
        when(mockMapper.to(any(CpnRestrictedCondition.class))).thenReturn(cpnRestrictedConditionDO);

        // Run the test
        final Map<Integer, List<CpnRestrictedConditionDO>> result = queryAllRestrictedCondition_promotionServiceUnderTest.preLoad(
                "area", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.notNull(result);
    }

    @Test
    public void testPreLoad_RestrictedServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllRestrictedCondition()).thenReturn(Collections.emptyList());

        // Configure RestrictedConditionMapper.to(...).
        final CpnRestrictedConditionDO cpnRestrictedConditionDO = new CpnRestrictedConditionDO();
        cpnRestrictedConditionDO.setConditionId(0);
        cpnRestrictedConditionDO.setPromotionId(0);
        cpnRestrictedConditionDO.setConditionName("conditionName");
        cpnRestrictedConditionDO.setContent("content");
        cpnRestrictedConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedConditionDO.setIsValid(0);
        cpnRestrictedConditionDO.setGroupId(0);
        cpnRestrictedConditionDO.setGroupVersion(0);
        when(mockMapper.to(any(CpnRestrictedCondition.class))).thenReturn(cpnRestrictedConditionDO);

        // Run the test
        final Map<Integer, List<CpnRestrictedConditionDO>> result = queryAllRestrictedCondition_promotionServiceUnderTest.preLoad(
                "area", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.notNull(result);
    }

    @Test
    public void testPreLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllRestrictedCondition()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllRestrictedCondition_promotionServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllRestrictedCondition(...).
        final CpnRestrictedCondition cpnRestrictedCondition = new CpnRestrictedCondition();
        cpnRestrictedCondition.setConditionId(0);
        cpnRestrictedCondition.setPromotionId(0);
        cpnRestrictedCondition.setConditionName("conditionName");
        cpnRestrictedCondition.setContent("content");
        cpnRestrictedCondition.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedCondition.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedCondition.setIsValid(0);
        cpnRestrictedCondition.setGroupId(0);
        cpnRestrictedCondition.setGroupVersion(0);
        final List<CpnRestrictedCondition> cpnRestrictedConditions = Arrays.asList(cpnRestrictedCondition);
        when(mockService.queryAllRestrictedCondition()).thenReturn(cpnRestrictedConditions);

        // Configure RestrictedConditionMapper.to(...).
        final CpnRestrictedConditionDO cpnRestrictedConditionDO = new CpnRestrictedConditionDO();
        cpnRestrictedConditionDO.setConditionId(0);
        cpnRestrictedConditionDO.setPromotionId(0);
        cpnRestrictedConditionDO.setConditionName("conditionName");
        cpnRestrictedConditionDO.setContent("content");
        cpnRestrictedConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedConditionDO.setIsValid(0);
        cpnRestrictedConditionDO.setGroupId(0);
        cpnRestrictedConditionDO.setGroupVersion(0);
        when(mockMapper.to(any(CpnRestrictedCondition.class))).thenReturn(cpnRestrictedConditionDO);

        // Run the test
        final Map<Integer, List<CpnRestrictedConditionDO>> result = queryAllRestrictedCondition_promotionServiceUnderTest.loadAll(
                "area", new HashSet<>(Arrays.asList(0)));

        Assert.notNull(result);
    }

    @Test
    public void testLoadAll_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllRestrictedCondition()).thenReturn(Collections.emptyList());

        // Configure RestrictedConditionMapper.to(...).
        final CpnRestrictedConditionDO cpnRestrictedConditionDO = new CpnRestrictedConditionDO();
        cpnRestrictedConditionDO.setConditionId(0);
        cpnRestrictedConditionDO.setPromotionId(0);
        cpnRestrictedConditionDO.setConditionName("conditionName");
        cpnRestrictedConditionDO.setContent("content");
        cpnRestrictedConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnRestrictedConditionDO.setIsValid(0);
        cpnRestrictedConditionDO.setGroupId(0);
        cpnRestrictedConditionDO.setGroupVersion(0);
        when(mockMapper.to(any(CpnRestrictedCondition.class))).thenReturn(cpnRestrictedConditionDO);

        // Run the test
        final Map<Integer, List<CpnRestrictedConditionDO>> result = queryAllRestrictedCondition_promotionServiceUnderTest.loadAll(
                "area", new HashSet<>(Arrays.asList(0)));

        Assert.notNull(result);
    }

    @Test
    public void testLoadAll_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllRestrictedCondition()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllRestrictedCondition_promotionServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0)))).isInstanceOf(Exception.class);
    }
}
