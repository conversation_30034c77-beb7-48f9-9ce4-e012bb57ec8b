package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.crossrecommend.service.contract.dto.SeoZone;
import com.ctrip.car.market.job.domain.config.QueryProductConfig;
import com.ctrip.car.market.job.domain.dto.QueryProductRequestConfig;
import com.ctrip.car.market.job.domain.dto.SeoStoreInfoDTO;
import com.ctrip.car.market.job.domain.proxy.OsdBasicDataProxy;
import com.ctrip.car.market.job.domain.proxy.ProductProxy;
import com.ctrip.car.market.job.domain.utils.QConfigUtil;
import com.ctrip.car.market.job.repository.dao.CarKalabCityDao;
import com.ctrip.car.market.job.repository.dao.carseodb.SeoTripproductDao;
import com.ctrip.car.market.job.repository.entity.CarKalabCity;
import com.ctrip.car.osd.shopping.api.entity.*;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import java.util.*;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate()
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
@PrepareForTest({
        QConfigUtil.class
})
@SuppressStaticInitializationFor({
        "com.ctrip.car.market.job.domain.utils.QConfigUtil"
})
public class TripSeoProductServiceTest {

    @Mock
    private QueryProductConfig queryProductConfig;
    @Mock
    private CarKalabCityDao carKalabCityDao;
    @Mock
    private SeoTripproductDao seoTripproductDao;
    @Mock
    private OsdBasicDataProxy osdBasicDataProxy;
    @Mock
    private ProductProxy productProxy;

    @InjectMocks
    private TripSeoProductService tripSeoProductService;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.initMocks(this);
        MemberModifier.field(TripSeoProductService.class, "queryProductConfig").set(tripSeoProductService, queryProductConfig);
        MemberModifier.field(TripSeoProductService.class, "carKalabCityDao").set(tripSeoProductService, carKalabCityDao);
        MemberModifier.field(TripSeoProductService.class, "seoTripproductDao").set(tripSeoProductService, seoTripproductDao);
        MemberModifier.field(TripSeoProductService.class, "osdBasicDataProxy").set(tripSeoProductService, osdBasicDataProxy);
        MemberModifier.field(TripSeoProductService.class, "productProxy").set(tripSeoProductService, productProxy);

        PowerMockito.mockStatic(QConfigUtil.class);
        PowerMockito.when(QConfigUtil.getConfigOrDefault(any(), any())).thenReturn("1,2");
        PowerMockito.when(QConfigUtil.getConfigLong(any(), any())).thenReturn(1L);

        QueryProductRequestConfig config = new QueryProductRequestConfig();
        config.setHour(1);
        config.setPickupTimeAddDays(1);
        config.setRentalDays(1);
        when(queryProductConfig.getConfig()).thenReturn(config);

        when(seoTripproductDao.queryBy(any(), any())).thenReturn(null);
        when(seoTripproductDao.batchInsert(any(), any())).thenReturn(null);
        when(seoTripproductDao.batchDelete(any(), any())).thenReturn(null);

        List<CarKalabCity> carKalabCityList = new ArrayList<>();
        CarKalabCity city1 = new CarKalabCity();
        city1.setCountryId(32L);
        city1.setProvinceId(32L);
        city1.setCityId(32L);
        CarKalabCity city2 = new CarKalabCity();
        city2.setCountryId(33L);
        city2.setProvinceId(33L);
        city2.setCityId(33L);
        CarKalabCity city3 = new CarKalabCity();
        city3.setCountryId(53L);
        city3.setProvinceId(53L);
        city3.setCityId(53L);
        carKalabCityList.add(city1);
        carKalabCityList.add(city2);
        carKalabCityList.add(city3);
        when(carKalabCityDao.queryByParams(any(), eq(1))).thenReturn(carKalabCityList);

        when(osdBasicDataProxy.getZone(any(), any())).thenReturn(new SeoZone());

        QueryProductResponseType responseType = new QueryProductResponseType();
        VehicleInfo vehicleInfo = new VehicleInfo();
        vehicleInfo.setBrandId(1);
        vehicleInfo.setId(1L);
        responseType.setVehicles(Collections.singletonList(vehicleInfo));
        responseType.setVendors(new ArrayList<>());
        when(productProxy.queryProduct(any())).thenReturn(responseType);
    }

    @Test
    public void testUpdateProduct() throws Throwable {
        tripSeoProductService.updateProduct(1);
    }

    @Test
    public void testConvertStore() throws Throwable {
        tripSeoProductService.convertStore(new ArrayList<>());
        tripSeoProductService.convertStore(Collections.singletonList(new StoreInfo()));

        StoreInfo storeInfo = new StoreInfo();
        storeInfo.setCommentInfoDto(new CommentInfo());
        storeInfo.getCommentInfoDto().setOverallRating(1D);
        List<SeoStoreInfoDTO> seoStoreInfoDTOList = tripSeoProductService.convertStore(Collections.singletonList(storeInfo));
        Assert.assertTrue(CollectionUtils.isNotEmpty(seoStoreInfoDTOList));
    }
}
