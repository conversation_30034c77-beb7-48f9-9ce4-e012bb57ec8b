package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.common.entity.act.ActivityTempContent;
import com.ctrip.car.market.job.common.entity.ActTempInfoDO;
import com.ctrip.car.market.job.domain.mapper.ActTempInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.ActCtriptempinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllActivityTempServiceTest {

    @Mock
    private ActivityService mockService;
    @Mock
    private ActTempInfoMapper mockMapper;

    @InjectMocks
    private QueryAllActivityTempService queryAllActivityTempServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure ActivityService.queryAllTemp(...).
        final ActCtriptempinfo actCtriptempinfo = new ActCtriptempinfo();
        actCtriptempinfo.setTmpId(0L);
        actCtriptempinfo.setTemplateType(0);
        actCtriptempinfo.setName("name");
        actCtriptempinfo.setTempContent("tempContent");
        final ActivityTempContent content = new ActivityTempContent();
        actCtriptempinfo.setContent(content);
        final List<ActCtriptempinfo> actCtriptempinfos = Arrays.asList(actCtriptempinfo);
        when(mockService.queryAllTemp()).thenReturn(actCtriptempinfos);

        // Configure ActTempInfoMapper.to(...).
        final ActTempInfoDO actTempInfoDO = new ActTempInfoDO();
        actTempInfoDO.setTmpId(0L);
        actTempInfoDO.setTemplateType(0);
        actTempInfoDO.setName("name");
        actTempInfoDO.setRemark("remark");
        final ActivityTempContent content1 = new ActivityTempContent();
        actTempInfoDO.setContent(content1);
        when(mockMapper.to(any(ActCtriptempinfo.class))).thenReturn(actTempInfoDO);

        // Run the test
        final ActTempInfoDO result = queryAllActivityTempServiceUnderTest.load("area", 0L);

        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void testLoad_ActivityServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllTemp()).thenReturn(Collections.emptyList());

        // Run the test
        final ActTempInfoDO result = queryAllActivityTempServiceUnderTest.load("area", 0L);

        Assert.assertTrue(Objects.isNull(result));
    }

    @Test
    public void testLoad_ActivityServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllTemp()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityTempServiceUnderTest.load("area", 0L))
                .isInstanceOf(SQLException.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure ActivityService.queryAllTemp(...).
        final ActCtriptempinfo actCtriptempinfo = new ActCtriptempinfo();
        actCtriptempinfo.setTmpId(0L);
        actCtriptempinfo.setTemplateType(0);
        actCtriptempinfo.setName("name");
        actCtriptempinfo.setTempContent("tempContent");
        final ActivityTempContent content = new ActivityTempContent();
        actCtriptempinfo.setContent(content);
        final List<ActCtriptempinfo> actCtriptempinfos = Arrays.asList(actCtriptempinfo);
        when(mockService.queryAllTemp()).thenReturn(actCtriptempinfos);

        // Configure ActTempInfoMapper.to(...).
        final ActTempInfoDO actTempInfoDO = new ActTempInfoDO();
        actTempInfoDO.setTmpId(0L);
        actTempInfoDO.setTemplateType(0);
        actTempInfoDO.setName("name");
        actTempInfoDO.setRemark("remark");
        final ActivityTempContent content1 = new ActivityTempContent();
        actTempInfoDO.setContent(content1);
        when(mockMapper.to(any(ActCtriptempinfo.class))).thenReturn(actTempInfoDO);

        // Run the test
        final Map<Long, ActTempInfoDO> result = queryAllActivityTempServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));

        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void testLoadAll_ActivityServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllTemp()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, ActTempInfoDO> result = queryAllActivityTempServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));

        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testLoadAll_ActivityServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllTemp()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityTempServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)))).isInstanceOf(SQLException.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure ActivityService.queryAllTemp(...).
        final ActCtriptempinfo actCtriptempinfo = new ActCtriptempinfo();
        actCtriptempinfo.setTmpId(0L);
        actCtriptempinfo.setTemplateType(0);
        actCtriptempinfo.setName("name");
        actCtriptempinfo.setTempContent("tempContent");
        final ActivityTempContent content = new ActivityTempContent();
        actCtriptempinfo.setContent(content);
        final List<ActCtriptempinfo> actCtriptempinfos = Arrays.asList(actCtriptempinfo);
        when(mockService.queryAllTemp()).thenReturn(actCtriptempinfos);

        // Configure ActTempInfoMapper.to(...).
        final ActTempInfoDO actTempInfoDO = new ActTempInfoDO();
        actTempInfoDO.setTmpId(0L);
        actTempInfoDO.setTemplateType(0);
        actTempInfoDO.setName("name");
        actTempInfoDO.setRemark("remark");
        final ActivityTempContent content1 = new ActivityTempContent();
        actTempInfoDO.setContent(content1);
        when(mockMapper.to(any(ActCtriptempinfo.class))).thenReturn(actTempInfoDO);

        // Run the test
        final Map<Long, ActTempInfoDO> result = queryAllActivityTempServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void testPreLoad_ActivityServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllTemp()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, ActTempInfoDO> result = queryAllActivityTempServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testPreLoad_ActivityServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllTemp()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityTempServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
