package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.CpnGroupRcConditionDO;
import com.ctrip.car.market.job.domain.mapper.GroupRcConditionMapper;
import com.ctrip.car.market.job.repository.entity.CpnGroupRcCondition;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllGroupRcConditionServiceTest {

    @Mock
    private RestrictedService mockService;
    @Mock
    private GroupRcConditionMapper mockMapper;

    @InjectMocks
    private QueryAllGroupRcConditionService queryAllGroupRcConditionServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllGroupRcCondition(...).
        final CpnGroupRcCondition cpnGroupRcCondition = new CpnGroupRcCondition();
        cpnGroupRcCondition.setGroupID(0);
        cpnGroupRcCondition.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcCondition.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcCondition.setCurrentVersion(0);
        final List<CpnGroupRcCondition> cpnGroupRcConditions = Arrays.asList(cpnGroupRcCondition);
        when(mockService.queryAllGroupRcCondition()).thenReturn(cpnGroupRcConditions);

        // Configure GroupRcConditionMapper.to(...).
        final CpnGroupRcConditionDO cpnGroupRcConditionDO = new CpnGroupRcConditionDO();
        cpnGroupRcConditionDO.setGroupID(0);
        cpnGroupRcConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcConditionDO.setCurrentVersion(0);
        when(mockMapper.to(any(CpnGroupRcCondition.class))).thenReturn(cpnGroupRcConditionDO);

        // Run the test
        final CpnGroupRcConditionDO result = queryAllGroupRcConditionServiceUnderTest.load("area", 0);
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllGroupRcCondition()).thenReturn(Collections.emptyList());

        // Configure GroupRcConditionMapper.to(...).
        final CpnGroupRcConditionDO cpnGroupRcConditionDO = new CpnGroupRcConditionDO();
        cpnGroupRcConditionDO.setGroupID(0);
        cpnGroupRcConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcConditionDO.setCurrentVersion(0);
        when(mockMapper.to(any(CpnGroupRcCondition.class))).thenReturn(cpnGroupRcConditionDO);

        // Run the test
        final CpnGroupRcConditionDO result = queryAllGroupRcConditionServiceUnderTest.load("area", 0);
        Assert.isNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllGroupRcCondition()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllGroupRcConditionServiceUnderTest.load("area", 0))
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllGroupRcCondition(...).
        final CpnGroupRcCondition cpnGroupRcCondition = new CpnGroupRcCondition();
        cpnGroupRcCondition.setGroupID(0);
        cpnGroupRcCondition.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcCondition.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcCondition.setCurrentVersion(0);
        final List<CpnGroupRcCondition> cpnGroupRcConditions = Arrays.asList(cpnGroupRcCondition);
        when(mockService.queryAllGroupRcCondition()).thenReturn(cpnGroupRcConditions);

        // Configure GroupRcConditionMapper.to(...).
        final CpnGroupRcConditionDO cpnGroupRcConditionDO = new CpnGroupRcConditionDO();
        cpnGroupRcConditionDO.setGroupID(0);
        cpnGroupRcConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcConditionDO.setCurrentVersion(0);
        when(mockMapper.to(any(CpnGroupRcCondition.class))).thenReturn(cpnGroupRcConditionDO);

        // Run the test
        final Map<Integer, CpnGroupRcConditionDO> result = queryAllGroupRcConditionServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllGroupRcCondition()).thenReturn(Collections.emptyList());

        // Configure GroupRcConditionMapper.to(...).
        final CpnGroupRcConditionDO cpnGroupRcConditionDO = new CpnGroupRcConditionDO();
        cpnGroupRcConditionDO.setGroupID(0);
        cpnGroupRcConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcConditionDO.setCurrentVersion(0);
        when(mockMapper.to(any(CpnGroupRcCondition.class))).thenReturn(cpnGroupRcConditionDO);

        // Run the test
        final Map<Integer, CpnGroupRcConditionDO> result = queryAllGroupRcConditionServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllGroupRcCondition()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllGroupRcConditionServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0)))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure RestrictedService.queryAllGroupRcCondition(...).
        final CpnGroupRcCondition cpnGroupRcCondition = new CpnGroupRcCondition();
        cpnGroupRcCondition.setGroupID(0);
        cpnGroupRcCondition.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcCondition.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcCondition.setCurrentVersion(0);
        final List<CpnGroupRcCondition> cpnGroupRcConditions = Arrays.asList(cpnGroupRcCondition);
        when(mockService.queryAllGroupRcCondition()).thenReturn(cpnGroupRcConditions);

        // Configure GroupRcConditionMapper.to(...).
        final CpnGroupRcConditionDO cpnGroupRcConditionDO = new CpnGroupRcConditionDO();
        cpnGroupRcConditionDO.setGroupID(0);
        cpnGroupRcConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcConditionDO.setCurrentVersion(0);
        when(mockMapper.to(any(CpnGroupRcCondition.class))).thenReturn(cpnGroupRcConditionDO);

        // Run the test
        final Map<Integer, CpnGroupRcConditionDO> result = queryAllGroupRcConditionServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllGroupRcCondition()).thenReturn(Collections.emptyList());

        // Configure GroupRcConditionMapper.to(...).
        final CpnGroupRcConditionDO cpnGroupRcConditionDO = new CpnGroupRcConditionDO();
        cpnGroupRcConditionDO.setGroupID(0);
        cpnGroupRcConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupRcConditionDO.setCurrentVersion(0);
        when(mockMapper.to(any(CpnGroupRcCondition.class))).thenReturn(cpnGroupRcConditionDO);

        // Run the test
        final Map<Integer, CpnGroupRcConditionDO> result = queryAllGroupRcConditionServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllGroupRcCondition()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllGroupRcConditionServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
