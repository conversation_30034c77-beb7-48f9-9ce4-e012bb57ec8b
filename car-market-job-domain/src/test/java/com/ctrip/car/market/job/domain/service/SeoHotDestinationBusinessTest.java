package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.customer.common.util.SharkUtil;
import com.ctrip.car.market.job.domain.config.SeoVendorCityPageConfig;
import com.ctrip.car.market.job.domain.constant.CommonConstant;
import com.ctrip.car.market.job.domain.dto.*;
import com.ctrip.car.market.job.domain.enums.SeoHotDestinationEnum;
import com.ctrip.car.market.job.domain.proxy.GlobalPoiJavaProxy;
import com.ctrip.car.market.job.domain.proxy.IGTBasicServiceProxy;
import com.ctrip.car.market.job.domain.utils.QConfigUtil;
import com.ctrip.car.market.job.repository.dao.*;
import com.ctrip.car.market.job.repository.entity.*;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.geo.domain.value.Country;
import com.ctrip.igt.basicservice.interfaces.dto.BasicAirportDTO;
import com.ctrip.igt.basicservice.interfaces.message.BasicAirportResponseType;
import com.ctrip.platform.dal.dao.DalHints;
import org.checkerframework.checker.units.qual.A;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;
import spock.lang.Unroll;

import java.sql.SQLException;
import java.util.*;

import static org.mockito.Mockito.*;
@PrepareForTest({
        QConfigUtil.class,
        SharkUtil.class
})
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
@SuppressStaticInitializationFor({
        "com.ctrip.car.market.job.domain.utils.QConfigUtil",
        "com.ctrip.car.customer.common.util.SharkUtil"
})
@RunWith(PowerMockRunner.class)
public class SeoHotDestinationBusinessTest {
    @Mock
    SeoHotCountryinfoDao seoHotCountryinfoDao;
    @Mock
    SeoHotDestinatioinfoDao seoHotDestinatioinfoDao;
    @Mock
    SeoHotCityinfoDao seoHotCityinfoDao;
    @Mock
    SeoHotInformationDao seoHotInformationDao;
    @Mock
    IGTBasicServiceProxy igtBasicServiceProxy;
    @Mock
    GlobalPoiJavaProxy globalPoiJavaProxy;
    @Mock
    SeoHotVendorDao seoHotVendorDao;
    @Mock
    SeoHotVendorCityDao seoHotVendorCityDao;
    @Mock
    CarKalabCityDao carKalabCityDao;
    @Mock
    CityRepository cityRepository;
    @InjectMocks
    SeoHotDestinationBusiness seoHotDestinationBusiness;

    @Before
    public void before() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(QConfigUtil.class);
        PowerMockito.mockStatic(SharkUtil.class);
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("pull.batch.size"),eq("500"))).thenReturn("500");
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("sql.batch.size"),eq("200"))).thenReturn("200");
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("sql.query.batch.size"),eq("500"))).thenReturn("500");
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("seo.ibu.qmq.locale"),eq("en-ID,en-PH,nl-NL,pt-BR,tr-TR,pt-PT,de-DE,fr-FR,th-TH,en-AU,en-SG,es-ES,it-IT,en-CA,en-GB,en-US,en-XX,ja-JP,ko-KR,zh-HK,zh-TW"))).thenReturn("en-ID,en-XX,en-GB");
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("seo.ibu.qmq.pre.url"),eq("https://www.trip.com/carhire/"))).thenReturn("https://www.trip.com/carhire/");
        when(QConfigUtil.getSeoHotConfigOrDefault(eq("twCityList"),eq("617,720,3845,3847,3848,3849,5152,5589,6954,7203,7523,7524,7570,7614,7662,7805,7808,7809,7810,7811,650358,669328"))).thenReturn("617,720,3845,3847,3848,3849,5152,5589,6954,7203,7523,7524,7570,7614,7662,7805,7808,7809,7810,7811,650358,669328");
    }

    @Test
    @Unroll
    public void testGetSeoHotCityinfoList() throws Exception {
        when(seoHotCityinfoDao.query(anyString(), any(DalHints.class), anyVararg())).thenReturn(Collections.emptyList());
        List<SeoHotCityinfo> result = seoHotDestinationBusiness.getSeoHotCityinfoList(true);
        List<SeoHotCityinfo> result2 = seoHotDestinationBusiness.getSeoHotCityinfoList(false);
        Assert.assertEquals(result, Collections.emptyList());
        Assert.assertEquals(result2, Collections.emptyList());
    }

    @Test
    @Unroll
    public void testGetSeoHotCountryinfoList() throws Exception {
        when(seoHotCountryinfoDao.query(anyString(), any(DalHints.class), anyVararg())).thenReturn(Collections.emptyList());

        List<SeoHotCountryinfo> result = seoHotDestinationBusiness.getSeoHotCountryinfoList(true);
        List<SeoHotCountryinfo> result2 = seoHotDestinationBusiness.getSeoHotCountryinfoList(false);
        Assert.assertEquals(Collections.emptyList(), result);
        Assert.assertEquals(Collections.emptyList(), result2);
    }

    @Test
    @Unroll
    public void testGetSeoHotDestinationinfoList() throws Exception {
        when(seoHotDestinatioinfoDao.query(anyString(), any(DalHints.class), anyVararg())).thenReturn(Collections.emptyList());
        List<SeoHotDestinatioinfo> result = seoHotDestinationBusiness.getSeoHotDestinationinfoList(true);
        List<SeoHotDestinatioinfo> result2 = seoHotDestinationBusiness.getSeoHotDestinationinfoList(false);
        Assert.assertEquals(Collections.emptyList(), result);
        Assert.assertEquals(Collections.emptyList(), result2);
    }

    @Test
    @Unroll
    public void testGetSeoHotInformation() throws Exception {
        when(seoHotInformationDao.query(anyString(), any(DalHints.class), anyVararg())).thenReturn(Collections.emptyList());
        List<SeoHotInformation> result = seoHotDestinationBusiness.getSeoHotInformation(true);
        List<SeoHotInformation> result2 = seoHotDestinationBusiness.getSeoHotInformation(false);
        Assert.assertEquals(Collections.emptyList(), result);
        Assert.assertEquals(Collections.emptyList(), result2);
    }

    @Test
    @Unroll
    public void testGetSeoHotVendorInfoList() throws Exception {
        when(seoHotVendorDao.query(anyString(), any(DalHints.class), anyVararg())).thenReturn(Collections.emptyList());
        List<SeoHotVendor> result = seoHotDestinationBusiness.getSeoHotVendorInfoList(true);
        List<SeoHotVendor> result2 = seoHotDestinationBusiness.getSeoHotVendorInfoList(false);
        Assert.assertEquals(Collections.emptyList(), result);
        Assert.assertEquals(Collections.emptyList(), result2);
    }


    @Test
    @Unroll
    public void testGetSeoHotVendorCityInfoList() throws Exception {
        when(seoHotVendorCityDao.query(anyString(), any(DalHints.class), anyVararg())).thenReturn(Collections.emptyList());
        List<SeoHotVendorCity> result = seoHotDestinationBusiness.getSeoHotVendorCityInfoList(true);
        List<SeoHotVendorCity> result2 = seoHotDestinationBusiness.getSeoHotVendorCityInfoList(false);
        Assert.assertEquals(Collections.emptyList(), result);
        Assert.assertEquals(Collections.emptyList(), result2);
    }


    @Test
    @Unroll
    public void testBatchInsertIntoCity() throws Exception {
        when(seoHotCityinfoDao.batchInsert(any(DalHints.class), anyListOf(SeoHotCityinfo.class))).thenReturn(new int[]{0});
        int i = seoHotDestinationBusiness.batchInsertIntoCity(Collections.singletonList(new SeoHotCityinfo()));
        Assert.assertEquals(i, 200);
    }

    @Test
    @Unroll
    public void testBatchInsertIntoCountry() throws Exception {
        when(seoHotCountryinfoDao.batchInsert(any(DalHints.class), anyListOf(SeoHotCountryinfo.class))).thenReturn(new int[]{0});
        int i = seoHotDestinationBusiness.batchInsertIntoCountry(Collections.singletonList(new SeoHotCountryinfo()));
        Assert.assertEquals(i, 200);
    }

    @Test
    @Unroll
    public void testBatchInsertIntoDestination() throws Exception {
        when(seoHotDestinatioinfoDao.batchInsert(any(DalHints.class), anyListOf(SeoHotDestinatioinfo.class))).thenReturn(new int[]{0});
        int i = seoHotDestinationBusiness.batchInsertIntoDestination(Collections.singletonList(new SeoHotDestinatioinfo()));
        Assert.assertEquals(i, 200);
    }

    @Test
    @Unroll
    public void testBatchInsertIntoInformation() throws Exception {
        when(seoHotInformationDao.batchInsert(any(DalHints.class), anyListOf(SeoHotInformation.class))).thenReturn(new int[]{0});
        int i = seoHotDestinationBusiness.batchInsertIntoInformation(Collections.singletonList(new SeoHotInformation()));
        Assert.assertEquals(i, 200);
    }

    @Test
    @Unroll
    public void testBatchInsertIntoVendor() throws Exception {
        when(seoHotVendorDao.batchInsert(any(DalHints.class), anyListOf(SeoHotVendor.class))).thenReturn(new int[]{0});
        int i = seoHotDestinationBusiness.batchInsertIntoVendor(Collections.singletonList(new SeoHotVendor()));
        Assert.assertEquals(i, 200);
    }

    @Test
    @Unroll
    public void testBatchInsertIntoVendorCity() throws Exception {
        when(seoHotVendorCityDao.batchInsert(any(DalHints.class), anyListOf(SeoHotVendorCity.class))).thenReturn(new int[]{0});
        int i = seoHotDestinationBusiness.batchInsertIntoVendorCity(Collections.singletonList(new SeoHotVendorCity()));
        Assert.assertEquals(i, 200);
    }


    @Test
    @Unroll
    public void testBatchUpdateDestination() throws Exception {
        List<SeoHotDestinatioinfo> seoHotDestinatioinfoList = Collections.singletonList(new SeoHotDestinatioinfo(){{setPoiCode("11");}});
        when(seoHotDestinatioinfoDao.batchUpdate(any(DalHints.class), anyListOf(SeoHotDestinatioinfo.class))).thenReturn(new int[]{0});
        seoHotDestinationBusiness.batchUpdateDestination(seoHotDestinatioinfoList);
        seoHotDestinationBusiness.batchUpdateDestination(new ArrayList<>());
        Assert.assertEquals(seoHotDestinatioinfoList.get(0).getPoiCode(), "11");
    }

    @Test
    @Unroll
    public void testBatchUpdateCity() throws Exception {
        List<SeoHotCityinfo> seoHotCityinfoList = Collections.singletonList(new SeoHotCityinfo(){{setCityId(11);}});
        when(seoHotCityinfoDao.batchUpdate(any(DalHints.class), anyListOf(SeoHotCityinfo.class))).thenReturn(new int[]{0});
        seoHotDestinationBusiness.batchUpdateCity(seoHotCityinfoList);
        seoHotDestinationBusiness.batchUpdateCity(new ArrayList<>());
        Assert.assertEquals(seoHotCityinfoList.get(0).getCityId().toString(), "11");
    }

    @Test
    @Unroll
    public void testBatchUpdateCountry() throws Exception {
        List<SeoHotCountryinfo> seoHotCountryinfoList = Collections.singletonList(new SeoHotCountryinfo(){{setCountryId(1);}});
        when(seoHotCountryinfoDao.batchUpdate(any(DalHints.class), anyListOf(SeoHotCountryinfo.class))).thenReturn(new int[]{0});
        seoHotDestinationBusiness.batchUpdateCountry(seoHotCountryinfoList);
        seoHotDestinationBusiness.batchUpdateCountry(new ArrayList<>());
        Assert.assertEquals(seoHotCountryinfoList.get(0).getCountryId().toString(), "1");
    }

    @Test
    @Unroll
    public void testBatchUpdateVendor() throws Exception {
        List<SeoHotVendor> seoHotVendorList = Collections.singletonList(new SeoHotVendor(){{setVendorId("1");}});
        when(seoHotVendorDao.batchUpdate(any(DalHints.class), anyListOf(SeoHotVendor.class))).thenReturn(new int[]{0});
        seoHotDestinationBusiness.batchUpdateVendor(seoHotVendorList);
        seoHotDestinationBusiness.batchUpdateVendor(new ArrayList<>());
        Assert.assertEquals(seoHotVendorList.get(0).getVendorId(), "1");
    }

    @Test
    @Unroll
    public void testBatchUpdateVendorCity() throws Exception {
        List<SeoHotVendorCity> seoHotVendorCityList = Collections.singletonList(new SeoHotVendorCity(){{setVendorId("1");}});
        when(seoHotVendorCityDao.batchUpdate(any(DalHints.class), anyListOf(SeoHotVendorCity.class))).thenReturn(new int[]{0});
        seoHotDestinationBusiness.batchUpdateVendorCity(seoHotVendorCityList);
        seoHotDestinationBusiness.batchUpdateVendorCity(new ArrayList<>());
        Assert.assertEquals(seoHotVendorCityList.get(0).getVendorId(), "1");
    }



    @Test
    public void testBuildNewCountry() throws Exception {
        when(seoHotCountryinfoDao.batchInsert(any(DalHints.class), anyListOf(SeoHotCountryinfo.class))).thenReturn(new int[]{0});
        HashSet<Long> set = new HashSet<>();
        set.add(1L);
        Country country = Country.builder().englishName("yyy").build();
        seoHotDestinationBusiness.buildNewCountry(new HashMap<Long, Country>() {{
            put(1L, country);
        }}, set
        );
        Assert.assertEquals("yyy", country.getEnglishName());
    }

    @Test
    public void testBuildNewCountry2() throws Exception {
        when(seoHotCountryinfoDao.batchInsert(any(DalHints.class), anyListOf(SeoHotCountryinfo.class))).thenReturn(new int[]{0});
        HashSet<Long> set = new HashSet<>();
        set.add(89L);
        Country country = Country.builder().englishName("yyy").build();
        seoHotDestinationBusiness.buildNewCountry(new HashMap<Long, Country>() {{
                                                      put(89L, country);
                                                  }}, set
        );
        Assert.assertEquals("yyy", country.getEnglishName());
    }

    @Test
    public void testBuildNewCity() throws Exception {
        when(seoHotCityinfoDao.batchInsert(any(DalHints.class), anyListOf(SeoHotCityinfo.class))).thenReturn(new int[]{0});
        Map<Long, City> cityMap = new HashMap<>();

        cityMap.put(1L, City.builder().englishName("shanghai").build());
        Map<Long, Country> countryMap = new HashMap<>();
        Country china = Country.builder().englishName("china").id(1L).build();
        countryMap.put(1L, china);
        Map<Long, GetHostDestinationInfo> requestInsertCity = new HashMap<>();
        GetHostDestinationInfo getHostDestinationInfo = new GetHostDestinationInfo();
        getHostDestinationInfo.setPickupcountryid("1");
        requestInsertCity.put(1L ,  getHostDestinationInfo);
        Map<Integer, SeoHotCountryinfo> countryinfoMap = new HashMap<>();
        seoHotDestinationBusiness.buildNewCity(cityMap, countryMap, requestInsertCity, countryinfoMap );
        Assert.assertEquals("shanghai", cityMap.get(1L).getEnglishName());
    }
    @Test
    public void testBuildNewCity4() throws Exception {
        when(seoHotCityinfoDao.batchInsert(any(DalHints.class), anyListOf(SeoHotCityinfo.class))).thenReturn(new int[]{0});
        Map<Long, City> cityMap = new HashMap<>();

        cityMap.put(1L, City.builder().englishName("shanghai").build());
        Map<Long, Country> countryMap = new HashMap<>();
        Country china = Country.builder().englishName("china").id(89L).build();
        countryMap.put(89L, china);
        Map<Long, GetHostDestinationInfo> requestInsertCity = new HashMap<>();
        GetHostDestinationInfo getHostDestinationInfo = new GetHostDestinationInfo();
        getHostDestinationInfo.setPickupcountryid("89");
        requestInsertCity.put(89L ,  getHostDestinationInfo);
        Map<Integer, SeoHotCountryinfo> countryinfoMap = new HashMap<>();
        seoHotDestinationBusiness.buildNewCity(cityMap, countryMap, requestInsertCity, countryinfoMap );
        Assert.assertEquals("shanghai", cityMap.get(1L).getEnglishName());
    }

    @Test
    public void testBuildNewCity2() throws Exception {
        when(seoHotCityinfoDao.batchInsert(any(DalHints.class), anyListOf(SeoHotCityinfo.class))).thenReturn(new int[]{0});
        Map<Long, City> cityMap = new HashMap<>();

        cityMap.put(1L, City.builder().englishName("shanghai").build());
        Map<Long, Country> countryMap = new HashMap<>();
        Country china = Country.builder().englishName("china").id(89L).build();
        countryMap.put(89L, china);
        Map<Long, GetHostDestinationInfo> requestInsertCity = new HashMap<>();
        GetHostDestinationInfo getHostDestinationInfo = new GetHostDestinationInfo();
        requestInsertCity.put(1L, getHostDestinationInfo);
        Map<Integer, SeoHotCountryinfo> countryinfoMap = new HashMap<>();
        seoHotDestinationBusiness.buildNewCity(cityMap, countryMap, requestInsertCity, countryinfoMap );
        Assert.assertEquals("shanghai", cityMap.get(1L).getEnglishName());
    }

    @Test
    public void testBuildNewDestination() throws Exception {
        BasicAirportResponseType basicAirportResponseType = new BasicAirportResponseType();
        List<BasicAirportDTO> result = new ArrayList<>();
        BasicAirportDTO basicAirportDTO = new BasicAirportDTO();
        basicAirportDTO.setCityId(1L);
        basicAirportDTO.setCountryId(1L);
        basicAirportDTO.setCode("shanghai_hongqiao");
        basicAirportDTO.setEnglishName("shanghai_hongqiao");
        result.add(basicAirportDTO);
        basicAirportResponseType.setResult(result);
        when(seoHotDestinatioinfoDao.batchInsert(any(DalHints.class), anyListOf(SeoHotDestinatioinfo.class))).thenReturn(new int[]{0});
        when(igtBasicServiceProxy.basicAirportQuery(anyListOf(String.class))).thenReturn(result);
        when(globalPoiJavaProxy.getPoiIdByPoiCode(anyListOf(String.class), anyObject())).thenReturn(new HashMap<String, Long>(){{
            put("shanghai_hongqiao", 10000L);
        }});
        Map<Long, City> cityMap = new HashMap<>();
        City city = City.builder().englishName("shanghai").build();
        cityMap.put(2L, city);

        Map<Long, Country> countryMap = new HashMap<>();
        Country china = Country.builder().englishName("china").id(1L).build();
        countryMap.put(1L, china);
        Map<String, GetHostDestinationInfo> getHostDestinationInfoMap = new HashMap<>();
        GetHostDestinationInfo getHostDestinationInfo = new GetHostDestinationInfo();
        getHostDestinationInfo.setAdvanceorder_cnt(1L);
        getHostDestinationInfo.setPickupcityid("1");
        getHostDestinationInfo.setPickupcountryid("1");
        getHostDestinationInfo.setPickuplocationcode("SHA");
        getHostDestinationInfo.setPickuplocationname("shanghai_hongqiao");
        getHostDestinationInfoMap.put("shanghai_hongqiao", getHostDestinationInfo);
        seoHotDestinationBusiness.buildNewDestination(getHostDestinationInfoMap, countryMap, cityMap);
        Assert.assertEquals("shanghai", cityMap.get(2L).getEnglishName());
    }

    @Test
    public void testBuildNewDestination1() throws Exception {
        BasicAirportResponseType basicAirportResponseType = new BasicAirportResponseType();
        List<BasicAirportDTO> result = new ArrayList<>();
        BasicAirportDTO basicAirportDTO = new BasicAirportDTO();
        basicAirportDTO.setCode("shanghai_hongqiao");
        result.add(basicAirportDTO);
        basicAirportResponseType.setResult(result);
        when(seoHotDestinatioinfoDao.batchInsert(any(DalHints.class), anyListOf(SeoHotDestinatioinfo.class))).thenReturn(new int[]{0});
        when(igtBasicServiceProxy.basicAirportQuery(anyListOf(String.class))).thenReturn(result);
        when(globalPoiJavaProxy.getPoiIdByPoiCode(anyListOf(String.class), anyObject())).thenReturn(new HashMap<String, Long>(){{
            put("shanghai_hongqiao", 10000L);
        }});
        Map<Long, City> cityMap = new HashMap<>();
        City city = City.builder().englishName("shanghai").build();
        cityMap.put(2L, city);
        Map<Long, Country> countryMap = new HashMap<>();
        Country china = Country.builder().id(89L).englishName("china").build();
        countryMap.put(89L, china);
        Map<String, GetHostDestinationInfo> getHostDestinationInfoMap = new HashMap<>();
        GetHostDestinationInfo getHostDestinationInfo = new GetHostDestinationInfo();
        getHostDestinationInfo.setPickupcityid("1");
        getHostDestinationInfo.setPickupcountryid("89");
        getHostDestinationInfo.setPickuplocationcode("SHA");
        getHostDestinationInfo.setPickuplocationname("shanghai_hongqiao");
        getHostDestinationInfoMap.put("shanghai_hongqiao", getHostDestinationInfo);
        seoHotDestinationBusiness.buildNewDestination(getHostDestinationInfoMap, countryMap, cityMap);
        Assert.assertEquals("shanghai", cityMap.get(2L).getEnglishName());
    }

    @Test
    public void testBuildNewDestination2() throws Exception {
        when(seoHotDestinatioinfoDao.batchInsert(any(DalHints.class), anyListOf(SeoHotDestinatioinfo.class))).thenReturn(new int[]{0});
        when(igtBasicServiceProxy.basicAirportQuery(anyListOf(String.class))).thenReturn(new ArrayList<BasicAirportDTO>());
        when(globalPoiJavaProxy.getPoiIdByPoiCode(anyListOf(String.class), anyObject())).thenReturn(new HashMap<String, Long>());
        seoHotDestinationBusiness.buildNewDestination(new HashMap<String,GetHostDestinationInfo>(), null, null);

    }

    @Test
    public void testBuildNewDestination3() throws Exception {
        BasicAirportResponseType basicAirportResponseType = new BasicAirportResponseType();
        List<BasicAirportDTO> result = new ArrayList<>();
        BasicAirportDTO basicAirportDTO = new BasicAirportDTO();
        basicAirportDTO.setCode("shanghai_hongqiao");
        result.add(basicAirportDTO);
        basicAirportResponseType.setResult(result);
        when(seoHotDestinatioinfoDao.batchInsert(any(DalHints.class), anyListOf(SeoHotDestinatioinfo.class))).thenReturn(new int[]{0});
        when(igtBasicServiceProxy.basicAirportQuery(anyListOf(String.class))).thenReturn(result);
        when(globalPoiJavaProxy.getPoiIdByPoiCode(anyListOf(String.class), anyObject())).thenReturn(new HashMap<String, Long>(){{
            put("shanghai_hongqiao", 10000L);
        }});
        Map<Long, City> cityMap = new HashMap<>();
        City city = City.builder().englishName("shanghai").build();
        cityMap.put(2L, city);
        Map<Long, Country> countryMap = new HashMap<>();
        Country china = Country.builder().englishName("china").id(2L).build();
        countryMap.put(2L, china);
        Map<String, GetHostDestinationInfo> getHostDestinationInfoMap = new HashMap<>();
        GetHostDestinationInfo getHostDestinationInfo = new GetHostDestinationInfo();
        getHostDestinationInfo.setPickupcityid("2");
        getHostDestinationInfo.setPickupcountryid("2");
        getHostDestinationInfo.setPickuplocationcode("SHA");
        getHostDestinationInfo.setPickuplocationname("shanghai_hongqiao");
        getHostDestinationInfoMap.put("shanghai_hongqiao", getHostDestinationInfo);
        seoHotDestinationBusiness.buildNewDestination(getHostDestinationInfoMap, countryMap, cityMap);
        Assert.assertEquals("shanghai", cityMap.get(2L).getEnglishName());
    }

    @Test
    public void testBuildHotDestinationMassageDTO() {
        when(SharkUtil.getSharkKeyByLanguageWithAppId(anyInt(),anyString(), anyString())).thenReturn("在{0}的城市");
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        Map<String, String> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("11", "shanghai");
        nameMap.put("en-ID", objectObjectHashMap);
        nameMap.put("en-XX", objectObjectHashMap);
        nameMap.put("en-GB", objectObjectHashMap);
        MessageParams messageParams = new MessageParams();
        messageParams.setSeoHotDestinationEnum(SeoHotDestinationEnum.CITY);
        messageParams.setUrl("xx");
        messageParams.setCityId(11);
        messageParams.setCountryId(1);
        messageParams.setNameMap(nameMap);
        List<HotDestinationMassageDTO> result = seoHotDestinationBusiness.buildHotDestinationMassageDTO(messageParams);
        HotDestinationMassageDTO hotDestinationMassageDTO = new HotDestinationMassageDTO();
        hotDestinationMassageDTO.setPageType("CITY");
        hotDestinationMassageDTO.setIsHot(0);
        hotDestinationMassageDTO.setState(1);
        hotDestinationMassageDTO.setSite("ID");
        hotDestinationMassageDTO.setSource("en-ID");
        hotDestinationMassageDTO.setChannel("en-ID");
        hotDestinationMassageDTO.setUrl("ww?locale=en-ID");
        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductName("cityId");
        productDTO.setProductValue("11");
        ProductDTO productDTO2 = new ProductDTO();
        productDTO2.setProductName("poiId");
        productDTO2.setProductValue("111");
        List<ProductDTO> list = Arrays.asList(productDTO, productDTO2);
        hotDestinationMassageDTO.setProducts(list);
        Assert.assertEquals("ID", result.get(0).getSite());
    }


    @Test
    public void testBuildHotDestinationMassageDTO2() {
        when(SharkUtil.getSharkKeyByLanguageWithAppId(anyInt(),anyString(), anyString())).thenReturn("在{0}的城市");
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        Map<String, String> objectObjectHashMap = new HashMap<>();
        nameMap.put("en-ID", objectObjectHashMap);
        nameMap.put("en-XX", objectObjectHashMap);
        nameMap.put("en-GB", objectObjectHashMap);
        MessageParams messageParams = new MessageParams();
        messageParams.setSeoHotDestinationEnum(SeoHotDestinationEnum.CITY);
        messageParams.setUrl("xx");
        messageParams.setCityId(11);
        messageParams.setCountryId(1);
        messageParams.setNameMap(nameMap);
        List<HotDestinationMassageDTO> result = seoHotDestinationBusiness.buildHotDestinationMassageDTO(messageParams);
        HotDestinationMassageDTO hotDestinationMassageDTO = new HotDestinationMassageDTO();
        hotDestinationMassageDTO.setPageType("CITY");
        hotDestinationMassageDTO.setIsHot(0);
        hotDestinationMassageDTO.setState(1);
        hotDestinationMassageDTO.setSite("ID");
        hotDestinationMassageDTO.setSource("en-ID");
        hotDestinationMassageDTO.setChannel("en-ID");
        hotDestinationMassageDTO.setUrl("ww?locale=en-ID");
        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductName("cityId");
        productDTO.setProductValue("11");
        ProductDTO productDTO2 = new ProductDTO();
        productDTO2.setProductName("poiId");
        productDTO2.setProductValue("111");
        List<ProductDTO> list = Arrays.asList(productDTO, productDTO2);
        hotDestinationMassageDTO.setProducts(list);
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testBuildSeoHotInformation() {
        GetCarSeoRentalInfo getCarSeoRentalInfo = new GetCarSeoRentalInfo();
        getCarSeoRentalInfo.setPickuplocationcode("code");
        getCarSeoRentalInfo.setCommon_period("1");
        getCarSeoRentalInfo.setVendorid("1");
        getCarSeoRentalInfo.setVendorname("11");
        getCarSeoRentalInfo.setPickcountryname("11");
        getCarSeoRentalInfo.setPickcountryid("1");
        getCarSeoRentalInfo.setVehivlegroupname("11");
        getCarSeoRentalInfo.setVehivlegroupid("11");
        getCarSeoRentalInfo.setPickcityid("1");
        getCarSeoRentalInfo.setPickcountryid("1");
        SeoHotInformation seoHotInformation = new SeoHotInformation();
        seoHotDestinationBusiness.buildSeoHotInformation(getCarSeoRentalInfo, seoHotInformation);
        Assert.assertEquals(seoHotInformation.getCountryId().toString(), getCarSeoRentalInfo.getPickcountryid());
    }

    @Test
    public void testGetProductValue() {
        String getProductValue1 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "getProductValue", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.COUNTRY).countryId(1).build());
        String getProductValue2 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "getProductValue", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.AIRPORT).poiCode("sha").build());
        String getProductValue3 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "getProductValue", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.CITY).cityId(1).build());
        String getProductValue4 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "getProductValue", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.SUPPLIER).vendorCode("SD001").build());
        String getProductValue5 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "getProductValue", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.SUPPLIER_CITY).vendorCityId(1).build());

        Assert.assertEquals(getProductValue1, "1");
        Assert.assertEquals(getProductValue2, "sha");
        Assert.assertEquals(getProductValue3, "1");
        Assert.assertEquals(getProductValue4, "SD001");
        Assert.assertEquals(getProductValue5, "1");
    }

    @Test
    public void testBuildSiteMapProductDTO() {
        List<ProductDTO> R1 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "buildSiteMapProductDTO", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.COUNTRY).countryId(1).build(), "hk");
        List<ProductDTO> R2 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "buildSiteMapProductDTO", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.AIRPORT).poiCode("sha").poiId(1L).countryId(1).build(), "hk");
        List<ProductDTO> R3 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "buildSiteMapProductDTO", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.CITY).cityId(1).countryId(1).provinceId("1").build(), "hk");
        List<ProductDTO> R4 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "buildSiteMapProductDTO", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.AIRPORT).poiCode("sha").poiId(1L).countryId(1).build(), "tw");
        List<ProductDTO> R5 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "buildSiteMapProductDTO", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.CITY).cityId(1).countryId(1).provinceId("1").build(), "tw");
        List<ProductDTO> R6 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "buildSiteMapProductDTO", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.AIRPORT).poiCode("sja").countryId(1).provinceId("1").build(), "tw");
        List<ProductDTO> R7 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "buildSiteMapProductDTO", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.AIRPORT).poiCode("sja").countryId(1).provinceId("1").cityId(617).build(), "tw");
        List<ProductDTO> R8 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "buildSiteMapProductDTO", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.CITY).cityId(617).countryId(1).provinceId("1").build(), "tw");
        List<ProductDTO> R9 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "buildSiteMapProductDTO", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.SUPPLIER).cityId(617).vendorCode("SD001").build(), "tw");
        List<ProductDTO> R10 = ReflectionTestUtils.invokeMethod(seoHotDestinationBusiness, "buildSiteMapProductDTO", MessageParams.builder().seoHotDestinationEnum(SeoHotDestinationEnum.SUPPLIER_CITY).cityId(617).vendorCode("SD001").vendorCityId(617).build(), "tw");

        Assert.assertEquals(R1.size(), 1);
        Assert.assertEquals(R2.size(), 3);
        Assert.assertEquals(R3.size(), 2);
        Assert.assertEquals(R4.size(), 3);
        Assert.assertEquals(R5.size(), 2);
        Assert.assertEquals(R6.size(), 2);
        Assert.assertEquals(R7.size(), 3);
        Assert.assertEquals(R8.size(), 3);
        Assert.assertEquals(R9.size(), 1);
        Assert.assertEquals(R10.size(), 2);
    }


    @Test
    public void testBuildSeoHotVendor() {
        // 准备测试数据
        SeoVendorCityPageConfig config = new SeoVendorCityPageConfig();
        config.setVendorCode("V001");
        config.setVendorName("VendorName");

        SeoHotVendor existingVendor = new SeoHotVendor();
        existingVendor.setVendorId("V001");

        // 调用方法
        SeoHotVendor result = seoHotDestinationBusiness.buildSeoHotVendor(config, existingVendor);

        // 验证结果
        Assert.assertEquals("V001", result.getVendorId());
        Assert.assertEquals("VendorName", result.getVendorName());
    }

    @Test
    public void testBuildSeoHotVendorCity() {
        // 准备测试数据
        SeoVendorCityPageConfig config = new SeoVendorCityPageConfig();
        config.setVendorCode("V001");
        config.setVendorName("Buget");
        config.setCityIdList(Collections.singletonList(new VendorCityInfo(){{setCityId(101);}}));

        SeoHotVendorCity existingVendorCity = new SeoHotVendorCity();
        existingVendorCity.setVendorId("V001");
        existingVendorCity.setCityId(101);
        when(cityRepository.findOne(Mockito.any(Long.class))).thenReturn(null);
        // 调用方法
        SeoHotVendorCity result = seoHotDestinationBusiness.buildSeoHotVendorCity(config, existingVendorCity, 101);
        // 验证结果
        Assert.assertEquals("V001", result.getVendorId());
        Assert.assertEquals(Integer.valueOf(101), result.getCityId());

        when(cityRepository.findOne(Mockito.any(Long.class))).thenReturn(new City());
        // 调用方法
        SeoHotVendorCity result2 = seoHotDestinationBusiness.buildSeoHotVendorCity(config, existingVendorCity, 101);
        // 验证结果
        Assert.assertEquals("V001", result2.getVendorId());
        Assert.assertEquals(Integer.valueOf(101), result2.getCityId());

        when(cityRepository.findOne(Mockito.any(Long.class))).thenReturn(City.builder().englishName("shanghai").build());
        // 调用方法
        SeoHotVendorCity result3 = seoHotDestinationBusiness.buildSeoHotVendorCity(config, existingVendorCity, 101);
        // 验证结果
        Assert.assertEquals("V001", result3.getVendorId());
        Assert.assertEquals(Integer.valueOf(101), result3.getCityId());
    }

    @Test
    public void testBuildSeoHotVendorCityException() {
        // 准备测试数据
        SeoVendorCityPageConfig config = new SeoVendorCityPageConfig();
        config.setVendorCode("V001");
        config.setVendorName("Buget");
        config.setCityIdList(Collections.singletonList(new VendorCityInfo(){{setCityId(101);}}));

        SeoHotVendorCity existingVendorCity = new SeoHotVendorCity();
        existingVendorCity.setVendorId("V001");
        existingVendorCity.setCityId(101);
        when(cityRepository.findOne(Mockito.any(Long.class))).thenThrow(new RuntimeException());
        // 调用方法
        SeoHotVendorCity result4 = seoHotDestinationBusiness.buildSeoHotVendorCity(config, existingVendorCity, 101);
        // 验证结果
        Assert.assertEquals("V001", result4.getVendorId());
        Assert.assertEquals(Integer.valueOf(101), result4.getCityId());
    }
}