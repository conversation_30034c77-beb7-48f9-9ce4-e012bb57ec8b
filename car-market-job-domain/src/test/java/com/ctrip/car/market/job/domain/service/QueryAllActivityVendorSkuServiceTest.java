package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.ActVendorSkuInfoDO;
import com.ctrip.car.market.job.domain.mapper.ActVendorSkuInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.VendorSkuinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllActivityVendorSkuServiceTest {

    @Mock
    private ActivityService service;
    @Mock
    private ActVendorSkuInfoMapper mockMapper;

    @InjectMocks
    private QueryAllActivityVendorSkuService queryAllActivityVendorSkuService;

    @Test
    public void testLoad() throws Throwable {
        final VendorSkuinfo vendorSkuinfo = new VendorSkuinfo();
        vendorSkuinfo.setId(0L);
        vendorSkuinfo.setVendorId(1L);
        vendorSkuinfo.setSkuId(1L);
        vendorSkuinfo.setStandardProductId(1L);
        final List<VendorSkuinfo> vendorSkuinfos = Arrays.asList(vendorSkuinfo);
        when(service.queryVendorSku()).thenReturn(vendorSkuinfos);

        final ActVendorSkuInfoDO vendorSkuInfoDO = new ActVendorSkuInfoDO();
        vendorSkuInfoDO.setId(0L);
        vendorSkuInfoDO.setVendorId(1L);
        vendorSkuInfoDO.setSkuId(1L);
        vendorSkuInfoDO.setStandardProductId(1L);
        when(mockMapper.to(any(VendorSkuinfo.class))).thenReturn(vendorSkuInfoDO);

        final List<ActVendorSkuInfoDO> result = queryAllActivityVendorSkuService.load("area", 0L);

        Assert.assertFalse(CollectionUtils.isNotEmpty(result));
    }

    @Test
    public void testPreLoad() throws Exception {
        final VendorSkuinfo vendorSkuinfo = new VendorSkuinfo();
        vendorSkuinfo.setId(0L);
        vendorSkuinfo.setVendorId(1L);
        vendorSkuinfo.setSkuId(1L);
        vendorSkuinfo.setStandardProductId(1L);
        final List<VendorSkuinfo> vendorSkuinfos = Arrays.asList(vendorSkuinfo);
        when(service.queryVendorSku()).thenReturn(vendorSkuinfos);

        final ActVendorSkuInfoDO vendorSkuInfoDO = new ActVendorSkuInfoDO();
        vendorSkuInfoDO.setId(0L);
        vendorSkuInfoDO.setVendorId(1L);
        vendorSkuInfoDO.setSkuId(1L);
        vendorSkuInfoDO.setStandardProductId(1L);
        when(mockMapper.to(any(VendorSkuinfo.class))).thenReturn(vendorSkuInfoDO);

        final Map<Long, List<ActVendorSkuInfoDO>> result = queryAllActivityVendorSkuService.preLoad("area", new Date());

        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void testLoad_MarketDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(service.queryVendorSku()).thenReturn(Collections.emptyList());

        final List<ActVendorSkuInfoDO> result = queryAllActivityVendorSkuService.load("area", 0L);

        Assert.assertFalse(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_MarketDBServiceThrowsSQLException() throws Exception {
        when(service.queryVendorSku()).thenThrow(SQLException.class);

        assertThatThrownBy(() -> queryAllActivityVendorSkuService.load("area", 0L)).isInstanceOf(SQLException.class);
    }
}
