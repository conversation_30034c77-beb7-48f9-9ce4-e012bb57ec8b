package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.crossrecommend.service.contract.dto.RecommendProductDTO;
import com.ctrip.car.market.crossrecommend.service.contract.servicetype.CarRecommendProductRequestType;
import com.ctrip.car.market.job.domain.config.CommonCityQueryConfig;
import com.ctrip.car.market.job.domain.proxy.CarCrossRecommendedServiceClientProxy;
import com.ctrip.car.market.job.domain.proxy.DestProxy;
import com.ctrip.car.market.job.repository.dao.CarKalabCityDao;
import com.ctrip.car.market.job.repository.dao.PkgRealtimedataDao;
import com.ctrip.car.market.job.repository.entity.CarKalabCity;
import com.ctrip.car.market.job.repository.entity.PkgRealtimedata;
import com.ctrip.framework.clogging.agent.log.ILog;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductManagerServiceJavaTest {
    @Mock
    ILog log;
    @Mock
    CarCrossRecommendedServiceClientProxy carCrossRecommendedServiceClientProxy;
    @Mock
    PkgRealtimedataDao pkgRealtimedataDao;
    @Mock
    CarKalabCityDao carKalabCityDao;
    @Mock
    DestProxy destProxy;
    @InjectMocks
    ProductManagerService productManagerService;

    @Test
    public void testBuildCarRecommendProductRequestType(){
        CommonCityQueryConfig commonCityQueryConfig1 = new CommonCityQueryConfig();
        commonCityQueryConfig1.setCacheMinutes("1");
        commonCityQueryConfig1.setPickupDate("2024-09-10 10:00:00");
        commonCityQueryConfig1.setRentDays(1);
        commonCityQueryConfig1.setBusinessType(35);
        commonCityQueryConfig1.setVehicleGroupIds("88");
        Assert.assertNotNull(ReflectionTestUtils.invokeMethod(productManagerService, "buildCarRecommendProductRequestType", commonCityQueryConfig1, 2L, 2L));
    }

    @Test
    public void testBuildCarRecommendProductRequestTypeNull(){
        CommonCityQueryConfig commonCityQueryConfig1 = new CommonCityQueryConfig();
        Assert.assertNotNull(ReflectionTestUtils.invokeMethod(productManagerService, "buildCarRecommendProductRequestType", commonCityQueryConfig1, 2L, 2L));
    }

    @Test
    public void testUpdateCityPrice() throws SQLException {
        List<CarKalabCity> list = new ArrayList<CarKalabCity>();
        Map<Long, String> cityImage = new HashMap<>();
        cityImage.put(1L, "https://youimg1.tripcdn.com/target/0103o12000agse4tu95C3.jpg");
        Map<Long, String> cityImage2 = new HashMap<>();
        cityImage2.put(2L, "https://youimg1.tripcdn.com/target/0103o12000agse4tu95C3.jpg");
        int[] temp = new int[1];
        CarKalabCity carKalabCity = new CarKalabCity();
        carKalabCity.setId(1L);
        list.add(carKalabCity);
        PowerMockito.when(carKalabCityDao.queryBy(Mockito.any(CarKalabCity.class))).thenReturn(list);
        PowerMockito.when(carKalabCityDao.batchUpdate(Mockito.anyListOf(CarKalabCity.class))).thenReturn(temp);
        int i = productManagerService.UpdateCityPrice(1L, new RecommendProductDTO() {{
            setOriginPrice(new BigDecimal("11"));
        }}, cityImage);
        int i2 = productManagerService.UpdateCityPrice(1L, new RecommendProductDTO(), cityImage2);
        Assert.assertEquals(1, i);
        Assert.assertEquals(1, i2);
    }

    @Test
    public void teststart() throws SQLException {
        Map<Long, String> cityImage = new HashMap<>();
        List<RecommendProductDTO>  list = new ArrayList<>();
        list.add(new RecommendProductDTO() {{setOriginPrice(new BigDecimal("11"));}});
        cityImage.put(1L, "https://youimg1.tripcdn.com/target/0103o12000agse4tu95C3.jpg");
        List<CarKalabCity> carKalabCities = new ArrayList<CarKalabCity>();
        CarKalabCity carKalabCity = new CarKalabCity();
        carKalabCity.setId(1L);
        carKalabCities.add(carKalabCity);
        int[] temp = new int[1];
        List<CommonCityQueryConfig> cityQueryConfigList = new ArrayList<>();
        CommonCityQueryConfig commonCityQueryConfig = new CommonCityQueryConfig();
        commonCityQueryConfig.setPickUpCityIds("1,2,3");
        cityQueryConfigList.add(commonCityQueryConfig);

        PowerMockito.when(carKalabCityDao.queryBy(Mockito.any(CarKalabCity.class))).thenReturn(carKalabCities);
        PowerMockito.when(carKalabCityDao.batchUpdate(Mockito.anyListOf(CarKalabCity.class))).thenReturn(temp);
        PowerMockito.when(pkgRealtimedataDao.batchInsert(Mockito.anyListOf(PkgRealtimedata.class))).thenReturn(temp);
        PowerMockito.when(destProxy.getCityImage(Mockito.anyListOf(Long.class))).thenReturn(cityImage);
        PowerMockito.when(carCrossRecommendedServiceClientProxy.queryProductByRequest(Mockito.any(CarRecommendProductRequestType.class))).thenReturn(list);
        boolean start = productManagerService.start(cityQueryConfigList);
        Assert.assertTrue(start);
    }
}
