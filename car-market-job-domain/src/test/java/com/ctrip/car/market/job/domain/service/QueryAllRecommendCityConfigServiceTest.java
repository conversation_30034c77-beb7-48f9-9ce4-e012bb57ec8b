package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.RecommendCityconfigDO;
import com.ctrip.car.market.job.domain.mapper.RecommendCityConfigMapper;
import com.ctrip.car.market.job.repository.entity.RecommendCityconfig;
import com.ctrip.car.market.job.repository.service.LabelService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllRecommendCityConfigServiceTest {

    @Mock
    private LabelService mockService;
    @Mock
    private RecommendCityConfigMapper mockMapper;

    @InjectMocks
    private QueryAllRecommendCityConfigService queryAllRecommendCityConfigServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure LabelService.queryAllRecommendCityConfig(...).
        final RecommendCityconfig recommendCityconfig = new RecommendCityconfig();
        recommendCityconfig.setId(0L);
        recommendCityconfig.setCityName("cityName");
        recommendCityconfig.setCityId(0);
        recommendCityconfig.setProductCategoryName("productCategoryName");
        recommendCityconfig.setProductCategoryId(0);
        recommendCityconfig.setPageType(0);
        recommendCityconfig.setPageShowName("pageShowName");
        recommendCityconfig.setPageShowIndex(0);
        recommendCityconfig.setCountryId(0);
        recommendCityconfig.setCountryName("countryName");
        recommendCityconfig.setContinentId(0);
        recommendCityconfig.setContinentName("continentName");
        recommendCityconfig.setShowLabels("showLabels");
        recommendCityconfig.setH5JumpUrl("h5JumpUrl");
        recommendCityconfig.setAppJumpUrl("appJumpUrl");
        final List<RecommendCityconfig> recommendCityconfigs = Arrays.asList(recommendCityconfig);
        when(mockService.queryAllRecommendCityConfig()).thenReturn(recommendCityconfigs);

        // Configure RecommendCityConfigMapper.to(...).
        final RecommendCityconfigDO recommendCityconfigDO = new RecommendCityconfigDO();
        recommendCityconfigDO.setId(0L);
        recommendCityconfigDO.setCityName("cityName");
        recommendCityconfigDO.setCityId(0);
        recommendCityconfigDO.setProductCategoryName("productCategoryName");
        recommendCityconfigDO.setProductCategoryId(0);
        recommendCityconfigDO.setPageType(0);
        recommendCityconfigDO.setPageShowName("pageShowName");
        recommendCityconfigDO.setPageShowIndex(0);
        recommendCityconfigDO.setCountryId(0);
        recommendCityconfigDO.setCountryName("countryName");
        recommendCityconfigDO.setContinentId(0);
        recommendCityconfigDO.setContinentName("continentName");
        recommendCityconfigDO.setShowLabels("showLabels");
        recommendCityconfigDO.setH5JumpUrl("h5JumpUrl");
        recommendCityconfigDO.setAppJumpUrl("appJumpUrl");
        when(mockMapper.to(any(RecommendCityconfig.class))).thenReturn(recommendCityconfigDO);

        // Run the test
        final List<RecommendCityconfigDO> result = queryAllRecommendCityConfigServiceUnderTest.load("area", 0);
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_LabelServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllRecommendCityConfig()).thenReturn(Collections.emptyList());

        // Configure RecommendCityConfigMapper.to(...).
        final RecommendCityconfigDO recommendCityconfigDO = new RecommendCityconfigDO();
        recommendCityconfigDO.setId(0L);
        recommendCityconfigDO.setCityName("cityName");
        recommendCityconfigDO.setCityId(0);
        recommendCityconfigDO.setProductCategoryName("productCategoryName");
        recommendCityconfigDO.setProductCategoryId(0);
        recommendCityconfigDO.setPageType(0);
        recommendCityconfigDO.setPageShowName("pageShowName");
        recommendCityconfigDO.setPageShowIndex(0);
        recommendCityconfigDO.setCountryId(0);
        recommendCityconfigDO.setCountryName("countryName");
        recommendCityconfigDO.setContinentId(0);
        recommendCityconfigDO.setContinentName("continentName");
        recommendCityconfigDO.setShowLabels("showLabels");
        recommendCityconfigDO.setH5JumpUrl("h5JumpUrl");
        recommendCityconfigDO.setAppJumpUrl("appJumpUrl");
        when(mockMapper.to(any(RecommendCityconfig.class))).thenReturn(recommendCityconfigDO);

        // Run the test
        final List<RecommendCityconfigDO> result = queryAllRecommendCityConfigServiceUnderTest.load("area", 0);

        // Verify the results
        Assert.isNull(result);
    }

    @Test
    public void testLoad_LabelServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllRecommendCityConfig()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllRecommendCityConfigServiceUnderTest.load("area", 0))
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure LabelService.queryAllRecommendCityConfig(...).
        final RecommendCityconfig recommendCityconfig = new RecommendCityconfig();
        recommendCityconfig.setId(0L);
        recommendCityconfig.setCityName("cityName");
        recommendCityconfig.setCityId(0);
        recommendCityconfig.setProductCategoryName("productCategoryName");
        recommendCityconfig.setProductCategoryId(0);
        recommendCityconfig.setPageType(0);
        recommendCityconfig.setPageShowName("pageShowName");
        recommendCityconfig.setPageShowIndex(0);
        recommendCityconfig.setCountryId(0);
        recommendCityconfig.setCountryName("countryName");
        recommendCityconfig.setContinentId(0);
        recommendCityconfig.setContinentName("continentName");
        recommendCityconfig.setShowLabels("showLabels");
        recommendCityconfig.setH5JumpUrl("h5JumpUrl");
        recommendCityconfig.setAppJumpUrl("appJumpUrl");
        final List<RecommendCityconfig> recommendCityconfigs = Arrays.asList(recommendCityconfig);
        when(mockService.queryAllRecommendCityConfig()).thenReturn(recommendCityconfigs);

        // Configure RecommendCityConfigMapper.to(...).
        final RecommendCityconfigDO recommendCityconfigDO = new RecommendCityconfigDO();
        recommendCityconfigDO.setId(0L);
        recommendCityconfigDO.setCityName("cityName");
        recommendCityconfigDO.setCityId(0);
        recommendCityconfigDO.setProductCategoryName("productCategoryName");
        recommendCityconfigDO.setProductCategoryId(0);
        recommendCityconfigDO.setPageType(0);
        recommendCityconfigDO.setPageShowName("pageShowName");
        recommendCityconfigDO.setPageShowIndex(0);
        recommendCityconfigDO.setCountryId(0);
        recommendCityconfigDO.setCountryName("countryName");
        recommendCityconfigDO.setContinentId(0);
        recommendCityconfigDO.setContinentName("continentName");
        recommendCityconfigDO.setShowLabels("showLabels");
        recommendCityconfigDO.setH5JumpUrl("h5JumpUrl");
        recommendCityconfigDO.setAppJumpUrl("appJumpUrl");
        when(mockMapper.to(any(RecommendCityconfig.class))).thenReturn(recommendCityconfigDO);

        // Run the test
        final Map<Integer, List<RecommendCityconfigDO>> result = queryAllRecommendCityConfigServiceUnderTest.loadAll(
                "area", new HashSet<>(Arrays.asList(0)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_LabelServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllRecommendCityConfig()).thenReturn(Collections.emptyList());

        // Configure RecommendCityConfigMapper.to(...).
        final RecommendCityconfigDO recommendCityconfigDO = new RecommendCityconfigDO();
        recommendCityconfigDO.setId(0L);
        recommendCityconfigDO.setCityName("cityName");
        recommendCityconfigDO.setCityId(0);
        recommendCityconfigDO.setProductCategoryName("productCategoryName");
        recommendCityconfigDO.setProductCategoryId(0);
        recommendCityconfigDO.setPageType(0);
        recommendCityconfigDO.setPageShowName("pageShowName");
        recommendCityconfigDO.setPageShowIndex(0);
        recommendCityconfigDO.setCountryId(0);
        recommendCityconfigDO.setCountryName("countryName");
        recommendCityconfigDO.setContinentId(0);
        recommendCityconfigDO.setContinentName("continentName");
        recommendCityconfigDO.setShowLabels("showLabels");
        recommendCityconfigDO.setH5JumpUrl("h5JumpUrl");
        recommendCityconfigDO.setAppJumpUrl("appJumpUrl");
        when(mockMapper.to(any(RecommendCityconfig.class))).thenReturn(recommendCityconfigDO);

        // Run the test
        final Map<Integer, List<RecommendCityconfigDO>> result = queryAllRecommendCityConfigServiceUnderTest.loadAll(
                "area", new HashSet<>(Arrays.asList(0)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_LabelServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllRecommendCityConfig()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllRecommendCityConfigServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0)))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure LabelService.queryAllRecommendCityConfig(...).
        final RecommendCityconfig recommendCityconfig = new RecommendCityconfig();
        recommendCityconfig.setId(0L);
        recommendCityconfig.setCityName("cityName");
        recommendCityconfig.setCityId(0);
        recommendCityconfig.setProductCategoryName("productCategoryName");
        recommendCityconfig.setProductCategoryId(0);
        recommendCityconfig.setPageType(0);
        recommendCityconfig.setPageShowName("pageShowName");
        recommendCityconfig.setPageShowIndex(0);
        recommendCityconfig.setCountryId(0);
        recommendCityconfig.setCountryName("countryName");
        recommendCityconfig.setContinentId(0);
        recommendCityconfig.setContinentName("continentName");
        recommendCityconfig.setShowLabels("showLabels");
        recommendCityconfig.setH5JumpUrl("h5JumpUrl");
        recommendCityconfig.setAppJumpUrl("appJumpUrl");
        final List<RecommendCityconfig> recommendCityconfigs = Arrays.asList(recommendCityconfig);
        when(mockService.queryAllRecommendCityConfig()).thenReturn(recommendCityconfigs);

        // Configure RecommendCityConfigMapper.to(...).
        final RecommendCityconfigDO recommendCityconfigDO = new RecommendCityconfigDO();
        recommendCityconfigDO.setId(0L);
        recommendCityconfigDO.setCityName("cityName");
        recommendCityconfigDO.setCityId(0);
        recommendCityconfigDO.setProductCategoryName("productCategoryName");
        recommendCityconfigDO.setProductCategoryId(0);
        recommendCityconfigDO.setPageType(0);
        recommendCityconfigDO.setPageShowName("pageShowName");
        recommendCityconfigDO.setPageShowIndex(0);
        recommendCityconfigDO.setCountryId(0);
        recommendCityconfigDO.setCountryName("countryName");
        recommendCityconfigDO.setContinentId(0);
        recommendCityconfigDO.setContinentName("continentName");
        recommendCityconfigDO.setShowLabels("showLabels");
        recommendCityconfigDO.setH5JumpUrl("h5JumpUrl");
        recommendCityconfigDO.setAppJumpUrl("appJumpUrl");
        when(mockMapper.to(any(RecommendCityconfig.class))).thenReturn(recommendCityconfigDO);

        // Run the test
        final Map<Integer, List<RecommendCityconfigDO>> result = queryAllRecommendCityConfigServiceUnderTest.preLoad(
                "area", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_LabelServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllRecommendCityConfig()).thenReturn(Collections.emptyList());

        // Configure RecommendCityConfigMapper.to(...).
        final RecommendCityconfigDO recommendCityconfigDO = new RecommendCityconfigDO();
        recommendCityconfigDO.setId(0L);
        recommendCityconfigDO.setCityName("cityName");
        recommendCityconfigDO.setCityId(0);
        recommendCityconfigDO.setProductCategoryName("productCategoryName");
        recommendCityconfigDO.setProductCategoryId(0);
        recommendCityconfigDO.setPageType(0);
        recommendCityconfigDO.setPageShowName("pageShowName");
        recommendCityconfigDO.setPageShowIndex(0);
        recommendCityconfigDO.setCountryId(0);
        recommendCityconfigDO.setCountryName("countryName");
        recommendCityconfigDO.setContinentId(0);
        recommendCityconfigDO.setContinentName("continentName");
        recommendCityconfigDO.setShowLabels("showLabels");
        recommendCityconfigDO.setH5JumpUrl("h5JumpUrl");
        recommendCityconfigDO.setAppJumpUrl("appJumpUrl");
        when(mockMapper.to(any(RecommendCityconfig.class))).thenReturn(recommendCityconfigDO);

        // Run the test
        final Map<Integer, List<RecommendCityconfigDO>> result = queryAllRecommendCityConfigServiceUnderTest.preLoad(
                "area", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_LabelServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllRecommendCityConfig()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllRecommendCityConfigServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
