package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.CpnPromotionidredirecturlDO;
import com.ctrip.car.market.job.domain.mapper.PromotionidredirecturlMapper;
import com.ctrip.car.market.job.repository.entity.CpnPromotionidredirecturl;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllPromotionRedirectUrlServiceTest {

    @Mock
    private RestrictedService mockService;
    @Mock
    private PromotionidredirecturlMapper mockMapper;

    @InjectMocks
    private QueryAllPromotionRedirectUrlService queryAllPromotionRedirectUrlServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllPromotionRedirectUrl(...).
        final CpnPromotionidredirecturl cpnPromotionidredirecturl = new CpnPromotionidredirecturl();
        cpnPromotionidredirecturl.setID(0L);
        cpnPromotionidredirecturl.setPromotionId(0L);
        cpnPromotionidredirecturl.setShowName("showName");
        cpnPromotionidredirecturl.setShortDesc("shortDesc");
        cpnPromotionidredirecturl.setPcJumpUrl("pcJumpUrl");
        cpnPromotionidredirecturl.setH5JumpUrl("h5JumpUrl");
        cpnPromotionidredirecturl.setAppJumpUrl("appJumpUrl");
        cpnPromotionidredirecturl.setRnJumpUrl("rnJumpUrl");
        cpnPromotionidredirecturl.setOtherJumpContent("otherJumpContent");
        cpnPromotionidredirecturl.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturl.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturl.setCreateor("createor");
        cpnPromotionidredirecturl.setModifer("modifer");
        cpnPromotionidredirecturl.setOfflineRoleCode("offlineRoleCode");
        cpnPromotionidredirecturl.setIsValid(0);
        final List<CpnPromotionidredirecturl> cpnPromotionidredirecturls = Arrays.asList(cpnPromotionidredirecturl);
        when(mockService.queryAllPromotionRedirectUrl()).thenReturn(cpnPromotionidredirecturls);

        // Configure PromotionidredirecturlMapper.to(...).
        final CpnPromotionidredirecturlDO cpnPromotionidredirecturlDO = new CpnPromotionidredirecturlDO();
        cpnPromotionidredirecturlDO.setID(0L);
        cpnPromotionidredirecturlDO.setPromotionId(0L);
        cpnPromotionidredirecturlDO.setShowName("showName");
        cpnPromotionidredirecturlDO.setShortDesc("shortDesc");
        cpnPromotionidredirecturlDO.setPcJumpUrl("pcJumpUrl");
        cpnPromotionidredirecturlDO.setH5JumpUrl("h5JumpUrl");
        cpnPromotionidredirecturlDO.setAppJumpUrl("appJumpUrl");
        cpnPromotionidredirecturlDO.setRnJumpUrl("rnJumpUrl");
        cpnPromotionidredirecturlDO.setOtherJumpContent("otherJumpContent");
        cpnPromotionidredirecturlDO.setDatachangeCreatetime(
                Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturlDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturlDO.setCreateor("createor");
        cpnPromotionidredirecturlDO.setModifer("modifer");
        cpnPromotionidredirecturlDO.setOfflineRoleCode("offlineRoleCode");
        cpnPromotionidredirecturlDO.setIsValid(0);
        when(mockMapper.to(any(CpnPromotionidredirecturl.class))).thenReturn(cpnPromotionidredirecturlDO);

        // Run the test
        final CpnPromotionidredirecturlDO result = queryAllPromotionRedirectUrlServiceUnderTest.load("area", 0L);
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllPromotionRedirectUrl()).thenReturn(Collections.emptyList());

        // Configure PromotionidredirecturlMapper.to(...).
        final CpnPromotionidredirecturlDO cpnPromotionidredirecturlDO = new CpnPromotionidredirecturlDO();
        cpnPromotionidredirecturlDO.setID(0L);
        cpnPromotionidredirecturlDO.setPromotionId(0L);
        cpnPromotionidredirecturlDO.setShowName("showName");
        cpnPromotionidredirecturlDO.setShortDesc("shortDesc");
        cpnPromotionidredirecturlDO.setPcJumpUrl("pcJumpUrl");
        cpnPromotionidredirecturlDO.setH5JumpUrl("h5JumpUrl");
        cpnPromotionidredirecturlDO.setAppJumpUrl("appJumpUrl");
        cpnPromotionidredirecturlDO.setRnJumpUrl("rnJumpUrl");
        cpnPromotionidredirecturlDO.setOtherJumpContent("otherJumpContent");
        cpnPromotionidredirecturlDO.setDatachangeCreatetime(
                Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturlDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturlDO.setCreateor("createor");
        cpnPromotionidredirecturlDO.setModifer("modifer");
        cpnPromotionidredirecturlDO.setOfflineRoleCode("offlineRoleCode");
        cpnPromotionidredirecturlDO.setIsValid(0);
        when(mockMapper.to(any(CpnPromotionidredirecturl.class))).thenReturn(cpnPromotionidredirecturlDO);

        // Run the test
        final CpnPromotionidredirecturlDO result = queryAllPromotionRedirectUrlServiceUnderTest.load("area", 0L);
        Assert.isNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllPromotionRedirectUrl()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllPromotionRedirectUrlServiceUnderTest.load("area", 0L))
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllPromotionRedirectUrl(...).
        final CpnPromotionidredirecturl cpnPromotionidredirecturl = new CpnPromotionidredirecturl();
        cpnPromotionidredirecturl.setID(0L);
        cpnPromotionidredirecturl.setPromotionId(0L);
        cpnPromotionidredirecturl.setShowName("showName");
        cpnPromotionidredirecturl.setShortDesc("shortDesc");
        cpnPromotionidredirecturl.setPcJumpUrl("pcJumpUrl");
        cpnPromotionidredirecturl.setH5JumpUrl("h5JumpUrl");
        cpnPromotionidredirecturl.setAppJumpUrl("appJumpUrl");
        cpnPromotionidredirecturl.setRnJumpUrl("rnJumpUrl");
        cpnPromotionidredirecturl.setOtherJumpContent("otherJumpContent");
        cpnPromotionidredirecturl.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturl.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturl.setCreateor("createor");
        cpnPromotionidredirecturl.setModifer("modifer");
        cpnPromotionidredirecturl.setOfflineRoleCode("offlineRoleCode");
        cpnPromotionidredirecturl.setIsValid(0);
        final List<CpnPromotionidredirecturl> cpnPromotionidredirecturls = Arrays.asList(cpnPromotionidredirecturl);
        when(mockService.queryAllPromotionRedirectUrl()).thenReturn(cpnPromotionidredirecturls);

        // Configure PromotionidredirecturlMapper.to(...).
        final CpnPromotionidredirecturlDO cpnPromotionidredirecturlDO = new CpnPromotionidredirecturlDO();
        cpnPromotionidredirecturlDO.setID(0L);
        cpnPromotionidredirecturlDO.setPromotionId(0L);
        cpnPromotionidredirecturlDO.setShowName("showName");
        cpnPromotionidredirecturlDO.setShortDesc("shortDesc");
        cpnPromotionidredirecturlDO.setPcJumpUrl("pcJumpUrl");
        cpnPromotionidredirecturlDO.setH5JumpUrl("h5JumpUrl");
        cpnPromotionidredirecturlDO.setAppJumpUrl("appJumpUrl");
        cpnPromotionidredirecturlDO.setRnJumpUrl("rnJumpUrl");
        cpnPromotionidredirecturlDO.setOtherJumpContent("otherJumpContent");
        cpnPromotionidredirecturlDO.setDatachangeCreatetime(
                Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturlDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturlDO.setCreateor("createor");
        cpnPromotionidredirecturlDO.setModifer("modifer");
        cpnPromotionidredirecturlDO.setOfflineRoleCode("offlineRoleCode");
        cpnPromotionidredirecturlDO.setIsValid(0);
        when(mockMapper.to(any(CpnPromotionidredirecturl.class))).thenReturn(cpnPromotionidredirecturlDO);

        // Run the test
        final Map<Long, CpnPromotionidredirecturlDO> result = queryAllPromotionRedirectUrlServiceUnderTest.loadAll(
                "area", new HashSet<>(Arrays.asList(0L)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllPromotionRedirectUrl()).thenReturn(Collections.emptyList());

        // Configure PromotionidredirecturlMapper.to(...).
        final CpnPromotionidredirecturlDO cpnPromotionidredirecturlDO = new CpnPromotionidredirecturlDO();
        cpnPromotionidredirecturlDO.setID(0L);
        cpnPromotionidredirecturlDO.setPromotionId(0L);
        cpnPromotionidredirecturlDO.setShowName("showName");
        cpnPromotionidredirecturlDO.setShortDesc("shortDesc");
        cpnPromotionidredirecturlDO.setPcJumpUrl("pcJumpUrl");
        cpnPromotionidredirecturlDO.setH5JumpUrl("h5JumpUrl");
        cpnPromotionidredirecturlDO.setAppJumpUrl("appJumpUrl");
        cpnPromotionidredirecturlDO.setRnJumpUrl("rnJumpUrl");
        cpnPromotionidredirecturlDO.setOtherJumpContent("otherJumpContent");
        cpnPromotionidredirecturlDO.setDatachangeCreatetime(
                Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturlDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturlDO.setCreateor("createor");
        cpnPromotionidredirecturlDO.setModifer("modifer");
        cpnPromotionidredirecturlDO.setOfflineRoleCode("offlineRoleCode");
        cpnPromotionidredirecturlDO.setIsValid(0);
        when(mockMapper.to(any(CpnPromotionidredirecturl.class))).thenReturn(cpnPromotionidredirecturlDO);

        // Run the test
        final Map<Long, CpnPromotionidredirecturlDO> result = queryAllPromotionRedirectUrlServiceUnderTest.loadAll(
                "area", new HashSet<>(Arrays.asList(0L)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllPromotionRedirectUrl()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllPromotionRedirectUrlServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure RestrictedService.queryAllPromotionRedirectUrl(...).
        final CpnPromotionidredirecturl cpnPromotionidredirecturl = new CpnPromotionidredirecturl();
        cpnPromotionidredirecturl.setID(0L);
        cpnPromotionidredirecturl.setPromotionId(0L);
        cpnPromotionidredirecturl.setShowName("showName");
        cpnPromotionidredirecturl.setShortDesc("shortDesc");
        cpnPromotionidredirecturl.setPcJumpUrl("pcJumpUrl");
        cpnPromotionidredirecturl.setH5JumpUrl("h5JumpUrl");
        cpnPromotionidredirecturl.setAppJumpUrl("appJumpUrl");
        cpnPromotionidredirecturl.setRnJumpUrl("rnJumpUrl");
        cpnPromotionidredirecturl.setOtherJumpContent("otherJumpContent");
        cpnPromotionidredirecturl.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturl.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturl.setCreateor("createor");
        cpnPromotionidredirecturl.setModifer("modifer");
        cpnPromotionidredirecturl.setOfflineRoleCode("offlineRoleCode");
        cpnPromotionidredirecturl.setIsValid(0);
        final List<CpnPromotionidredirecturl> cpnPromotionidredirecturls = Arrays.asList(cpnPromotionidredirecturl);
        when(mockService.queryAllPromotionRedirectUrl()).thenReturn(cpnPromotionidredirecturls);

        // Configure PromotionidredirecturlMapper.to(...).
        final CpnPromotionidredirecturlDO cpnPromotionidredirecturlDO = new CpnPromotionidredirecturlDO();
        cpnPromotionidredirecturlDO.setID(0L);
        cpnPromotionidredirecturlDO.setPromotionId(0L);
        cpnPromotionidredirecturlDO.setShowName("showName");
        cpnPromotionidredirecturlDO.setShortDesc("shortDesc");
        cpnPromotionidredirecturlDO.setPcJumpUrl("pcJumpUrl");
        cpnPromotionidredirecturlDO.setH5JumpUrl("h5JumpUrl");
        cpnPromotionidredirecturlDO.setAppJumpUrl("appJumpUrl");
        cpnPromotionidredirecturlDO.setRnJumpUrl("rnJumpUrl");
        cpnPromotionidredirecturlDO.setOtherJumpContent("otherJumpContent");
        cpnPromotionidredirecturlDO.setDatachangeCreatetime(
                Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturlDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturlDO.setCreateor("createor");
        cpnPromotionidredirecturlDO.setModifer("modifer");
        cpnPromotionidredirecturlDO.setOfflineRoleCode("offlineRoleCode");
        cpnPromotionidredirecturlDO.setIsValid(0);
        when(mockMapper.to(any(CpnPromotionidredirecturl.class))).thenReturn(cpnPromotionidredirecturlDO);

        // Run the test
        final Map<Long, CpnPromotionidredirecturlDO> result = queryAllPromotionRedirectUrlServiceUnderTest.preLoad(
                "area", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllPromotionRedirectUrl()).thenReturn(Collections.emptyList());

        // Configure PromotionidredirecturlMapper.to(...).
        final CpnPromotionidredirecturlDO cpnPromotionidredirecturlDO = new CpnPromotionidredirecturlDO();
        cpnPromotionidredirecturlDO.setID(0L);
        cpnPromotionidredirecturlDO.setPromotionId(0L);
        cpnPromotionidredirecturlDO.setShowName("showName");
        cpnPromotionidredirecturlDO.setShortDesc("shortDesc");
        cpnPromotionidredirecturlDO.setPcJumpUrl("pcJumpUrl");
        cpnPromotionidredirecturlDO.setH5JumpUrl("h5JumpUrl");
        cpnPromotionidredirecturlDO.setAppJumpUrl("appJumpUrl");
        cpnPromotionidredirecturlDO.setRnJumpUrl("rnJumpUrl");
        cpnPromotionidredirecturlDO.setOtherJumpContent("otherJumpContent");
        cpnPromotionidredirecturlDO.setDatachangeCreatetime(
                Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturlDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionidredirecturlDO.setCreateor("createor");
        cpnPromotionidredirecturlDO.setModifer("modifer");
        cpnPromotionidredirecturlDO.setOfflineRoleCode("offlineRoleCode");
        cpnPromotionidredirecturlDO.setIsValid(0);
        when(mockMapper.to(any(CpnPromotionidredirecturl.class))).thenReturn(cpnPromotionidredirecturlDO);

        // Run the test
        final Map<Long, CpnPromotionidredirecturlDO> result = queryAllPromotionRedirectUrlServiceUnderTest.preLoad(
                "area", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllPromotionRedirectUrl()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllPromotionRedirectUrlServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
