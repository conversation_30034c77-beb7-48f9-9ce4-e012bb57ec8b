package com.ctrip.car.market.job.domain.service.seo;

import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorCityDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotVendorMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotVendor;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllHotVendorServiceTest {

    @Mock
    private MarketDBService mockService;
    @Mock
    private SeoHotVendorMapper mockMapper;

    @InjectMocks
    private QueryAllHotVendorService queryAllSeoHotCityCountryServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure MarketDBService.queryAllHotCity(...).
        final SeoHotVendor seoHotCityinfo = new SeoHotVendor();
        seoHotCityinfo.setId(0L);
        seoHotCityinfo.setVendorId("sd");
        seoHotCityinfo.setVendorId("sd");
        seoHotCityinfo.setUrl("url");
        final List<SeoHotVendor> seoHotCityinfos = Arrays.asList(seoHotCityinfo);
        when(mockService.queryAllHotVendor()).thenReturn(seoHotCityinfos);

        // Configure SeoHotCityinfoMapper.to(...).
        final SeoHotVendorDO seoHotCityinfoDO = new SeoHotVendorDO();
        seoHotCityinfoDO.setId(0L);
        seoHotCityinfoDO.setVendorId("SD");
        seoHotCityinfoDO.setVendorId("sd");
        seoHotCityinfoDO.setUrl("url");
        when(mockMapper.to(any(SeoHotVendor.class))).thenReturn(seoHotCityinfoDO);

        // Run the test
        Assert.assertTrue(CollectionUtils.isNotEmpty(queryAllSeoHotCityCountryServiceUnderTest.load("area", "sd")));
    }

    @Test
    public void testLoad_MarketDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllHotVendor()).thenReturn(Collections.emptyList());

        // Run the test
        final List<SeoHotVendorDO> result = queryAllSeoHotCityCountryServiceUnderTest.load("area", "sd");

        // Verify the results
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_MarketDBServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllHotVendor()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllSeoHotCityCountryServiceUnderTest.load("area", "sd"))
                .isInstanceOf(SQLException.class);
    }

    @Test
    public void test_preLoad() throws Exception {
        when(mockService.queryAllHotVendor()).thenReturn(Collections.emptyList());

        final Map<String, List<SeoHotVendorDO>> result = queryAllSeoHotCityCountryServiceUnderTest.preLoad("area", null);

        // Verify the results
        Assert.assertTrue(result.isEmpty());
    }
}
