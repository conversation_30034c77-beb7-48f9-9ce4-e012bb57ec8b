package com.ctrip.car.market.job.domain.service.seo;

import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO;
import com.ctrip.car.market.job.common.entity.seo.SeoVendorCommentScoreDO;
import com.ctrip.car.market.job.domain.mapper.SeoVendorCommentScoreMapper;
import com.ctrip.car.market.job.repository.entity.SeoVendorCommentScore;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllSeoVendorCommentScoreServiceTest {

    @Mock
    private MarketDBService mockService;
    @Mock
    private SeoVendorCommentScoreMapper mockMapper;

    @InjectMocks
    private QueryAllSeoVendorCommentScoreService queryAllSeoHotCityCountryServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure MarketDBService.queryAllHotCity(...).
        final SeoVendorCommentScore seoHotCityinfo = new SeoVendorCommentScore();
        seoHotCityinfo.setId(0L);
        seoHotCityinfo.setVendorCode("sd");
        final List<SeoVendorCommentScore> seoHotCityinfos = Arrays.asList(seoHotCityinfo);
        when(mockService.queryAllVendorScore()).thenReturn(seoHotCityinfos);

        // Configure SeoHotCityinfoMapper.to(...).
        final SeoVendorCommentScoreDO seoHotCityinfoDO = new SeoVendorCommentScoreDO();
        seoHotCityinfoDO.setId(0L);
        seoHotCityinfoDO.setVendorCode("sd");
        when(mockMapper.to(any(SeoVendorCommentScore.class))).thenReturn(seoHotCityinfoDO);

        // Run the test
        Assert.assertTrue(CollectionUtils.isNotEmpty(queryAllSeoHotCityCountryServiceUnderTest.load("area", "sd")));
    }

    @Test
    public void testLoad_MarketDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllVendorScore()).thenReturn(Collections.emptyList());

        // Run the test
        final List<SeoVendorCommentScoreDO> result = queryAllSeoHotCityCountryServiceUnderTest.load("area", "sd");

        // Verify the results
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_MarketDBServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllVendorScore()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllSeoHotCityCountryServiceUnderTest.load("area", "sd"))
                .isInstanceOf(SQLException.class);
    }

    @Test
    public void test_preLoad() throws Exception {
        when(mockService.queryAllVendorScore()).thenReturn(Collections.emptyList());

        final Map<String, List<SeoVendorCommentScoreDO>> result = queryAllSeoHotCityCountryServiceUnderTest.preLoad("area", null);

        // Verify the results
        Assert.assertTrue(result.isEmpty());
    }
}
