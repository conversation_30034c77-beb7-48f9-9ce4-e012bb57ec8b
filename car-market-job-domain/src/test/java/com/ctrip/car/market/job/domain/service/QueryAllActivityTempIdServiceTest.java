package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.repository.entity.activity.ActCtriptempinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllActivityTempIdServiceTest {

    @Mock
    private ActivityService mockService;

    @InjectMocks
    private QueryAllActivityTempIdService queryAllActivityTempIdServiceUnderTest;

    @Test
    public void testLoad_ActivityServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllTemp()).thenReturn(Collections.emptyList());

        // Run the test
        final List<Long> result = queryAllActivityTempIdServiceUnderTest.load("area", "key");

        // Verify the results
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_ActivityServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllTemp()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityTempIdServiceUnderTest.load("area", "key"))
                .isInstanceOf(SQLException.class);
    }

    @Test
    public void testLoadAll_ActivityServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllTemp()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityTempIdServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")))).isInstanceOf(SQLException.class);
    }

    @Test
    public void testPreLoad_ActivityServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllTemp()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityTempIdServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
