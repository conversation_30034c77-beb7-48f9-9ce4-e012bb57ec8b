package com.ctrip.car.market.job.domain.service.seo;

import com.ctrip.car.market.job.common.entity.seo.SeoHotCountryinfoDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotCountryinfoMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotCountryinfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Sets;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllSeoHotCountryServiceTest {

    @Mock
    private MarketDBService mockService;
    @Mock
    private SeoHotCountryinfoMapper mockMapper;

    @InjectMocks
    private QueryAllSeoHotCountryService queryAllSeoHotCountryService;

    @Test
    public void testLoad() throws Throwable {
        final SeoHotCountryinfo seoHotCountryinfo = new SeoHotCountryinfo();
        seoHotCountryinfo.setId(0L);
        seoHotCountryinfo.setCountryId(0);
        seoHotCountryinfo.setCountryName("cityName");
        seoHotCountryinfo.setCountryId(0);
        seoHotCountryinfo.setUrl("url");
        final List<SeoHotCountryinfo> seoHotCountryinfos = Arrays.asList(seoHotCountryinfo);
        when(mockService.queryAllHotCountry()).thenReturn(seoHotCountryinfos);

        final SeoHotCountryinfoDO seoHotCountryinfoDO = new SeoHotCountryinfoDO();
        seoHotCountryinfoDO.setId(0L);
        seoHotCountryinfoDO.setCountryId(0);
        seoHotCountryinfoDO.setCountryName("cityName");
        seoHotCountryinfoDO.setCountryId(0);
        seoHotCountryinfoDO.setUrl("url");
        when(mockMapper.to(any(SeoHotCountryinfo.class))).thenReturn(seoHotCountryinfoDO);

        final List<SeoHotCountryinfoDO> result = queryAllSeoHotCountryService.load("area", 0);

        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }

    @Test
    public void testPreLoad() throws Exception {
        final SeoHotCountryinfo seoHotCountryinfo = new SeoHotCountryinfo();
        seoHotCountryinfo.setId(0L);
        seoHotCountryinfo.setCountryId(0);
        seoHotCountryinfo.setCountryName("cityName");
        seoHotCountryinfo.setCountryId(0);
        seoHotCountryinfo.setUrl("url");
        final List<SeoHotCountryinfo> seoHotCountryinfos = Arrays.asList(seoHotCountryinfo);
        when(mockService.queryAllHotCountry()).thenReturn(seoHotCountryinfos);

        final SeoHotCountryinfoDO seoHotCountryinfoDO = new SeoHotCountryinfoDO();
        seoHotCountryinfoDO.setId(0L);
        seoHotCountryinfoDO.setCountryId(0);
        seoHotCountryinfoDO.setCountryName("cityName");
        seoHotCountryinfoDO.setCountryId(0);
        seoHotCountryinfoDO.setUrl("url");
        when(mockMapper.to(any(SeoHotCountryinfo.class))).thenReturn(seoHotCountryinfoDO);

        final Map<Integer, List<SeoHotCountryinfoDO>> result = queryAllSeoHotCountryService.preLoad("area", new Date());

        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void testLoad_MarketDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllHotCountry()).thenReturn(Collections.emptyList());

        final List<SeoHotCountryinfoDO> result = queryAllSeoHotCountryService.load("area", 0);

        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_MarketDBServiceThrowsSQLException() throws Exception {
        when(mockService.queryAllHotCountry()).thenThrow(SQLException.class);

        assertThatThrownBy(() -> queryAllSeoHotCountryService.load("area", 0)).isInstanceOf(SQLException.class);
    }
}
