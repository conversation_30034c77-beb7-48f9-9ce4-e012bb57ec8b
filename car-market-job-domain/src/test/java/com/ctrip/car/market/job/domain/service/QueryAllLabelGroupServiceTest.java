package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.CpnLabelGroupDO;
import com.ctrip.car.market.job.domain.mapper.LabelGroupMapper;
import com.ctrip.car.market.job.repository.entity.CpnLabelGroup;
import com.ctrip.car.market.job.repository.service.LabelService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllLabelGroupServiceTest {

    @Mock
    private LabelService mockService;
    @Mock
    private LabelGroupMapper mockMapper;

    @InjectMocks
    private QueryAllLabelGroupService queryAllLabelGroupServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure LabelService.queryAllLabelGroup(...).
        final CpnLabelGroup cpnLabelGroup = new CpnLabelGroup();
        cpnLabelGroup.setId(0L);
        cpnLabelGroup.setCategory(0);
        cpnLabelGroup.setSubCategory(0);
        cpnLabelGroup.setSubCategoryDesc("subCategoryDesc");
        cpnLabelGroup.setMarketGroup(0);
        cpnLabelGroup.setMarketGroupDesc("marketGroupDesc");
        cpnLabelGroup.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelGroup.setShowQuickPickInList(false);
        cpnLabelGroup.setShowQuickNoticeInList(false);
        cpnLabelGroup.setQuickNoticeIcon("quickNoticeIcon");
        cpnLabelGroup.setShowPriority(0);
        cpnLabelGroup.setNoticeContent("noticeContent");
        final List<CpnLabelGroup> cpnLabelGroups = Arrays.asList(cpnLabelGroup);
        when(mockService.queryAllLabelGroup()).thenReturn(cpnLabelGroups);

        // Configure LabelGroupMapper.to(...).
        final CpnLabelGroupDO cpnLabelGroupDO = new CpnLabelGroupDO();
        cpnLabelGroupDO.setId(0L);
        cpnLabelGroupDO.setCategory(0);
        cpnLabelGroupDO.setSubCategory(0);
        cpnLabelGroupDO.setSubCategoryDesc("subCategoryDesc");
        cpnLabelGroupDO.setMarketGroup(0);
        cpnLabelGroupDO.setMarketGroupDesc("marketGroupDesc");
        cpnLabelGroupDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelGroupDO.setShowQuickPickInList(false);
        cpnLabelGroupDO.setShowQuickNoticeInList(false);
        cpnLabelGroupDO.setQuickNoticeIcon("quickNoticeIcon");
        cpnLabelGroupDO.setShowPriority(0);
        cpnLabelGroupDO.setNoticeContent("noticeContent");
        when(mockMapper.to(any(CpnLabelGroup.class))).thenReturn(cpnLabelGroupDO);

        // Run the test
        final CpnLabelGroupDO result = queryAllLabelGroupServiceUnderTest.load("area", 0L);
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_LabelServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllLabelGroup()).thenReturn(Collections.emptyList());

        // Configure LabelGroupMapper.to(...).
        final CpnLabelGroupDO cpnLabelGroupDO = new CpnLabelGroupDO();
        cpnLabelGroupDO.setId(0L);
        cpnLabelGroupDO.setCategory(0);
        cpnLabelGroupDO.setSubCategory(0);
        cpnLabelGroupDO.setSubCategoryDesc("subCategoryDesc");
        cpnLabelGroupDO.setMarketGroup(0);
        cpnLabelGroupDO.setMarketGroupDesc("marketGroupDesc");
        cpnLabelGroupDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelGroupDO.setShowQuickPickInList(false);
        cpnLabelGroupDO.setShowQuickNoticeInList(false);
        cpnLabelGroupDO.setQuickNoticeIcon("quickNoticeIcon");
        cpnLabelGroupDO.setShowPriority(0);
        cpnLabelGroupDO.setNoticeContent("noticeContent");
        when(mockMapper.to(any(CpnLabelGroup.class))).thenReturn(cpnLabelGroupDO);

        // Run the test
        final CpnLabelGroupDO result = queryAllLabelGroupServiceUnderTest.load("area", 0L);
        Assert.isNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_LabelServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllLabelGroup()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllLabelGroupServiceUnderTest.load("area", 0L)).isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure LabelService.queryAllLabelGroup(...).
        final CpnLabelGroup cpnLabelGroup = new CpnLabelGroup();
        cpnLabelGroup.setId(0L);
        cpnLabelGroup.setCategory(0);
        cpnLabelGroup.setSubCategory(0);
        cpnLabelGroup.setSubCategoryDesc("subCategoryDesc");
        cpnLabelGroup.setMarketGroup(0);
        cpnLabelGroup.setMarketGroupDesc("marketGroupDesc");
        cpnLabelGroup.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelGroup.setShowQuickPickInList(false);
        cpnLabelGroup.setShowQuickNoticeInList(false);
        cpnLabelGroup.setQuickNoticeIcon("quickNoticeIcon");
        cpnLabelGroup.setShowPriority(0);
        cpnLabelGroup.setNoticeContent("noticeContent");
        final List<CpnLabelGroup> cpnLabelGroups = Arrays.asList(cpnLabelGroup);
        when(mockService.queryAllLabelGroup()).thenReturn(cpnLabelGroups);

        // Configure LabelGroupMapper.to(...).
        final CpnLabelGroupDO cpnLabelGroupDO = new CpnLabelGroupDO();
        cpnLabelGroupDO.setId(0L);
        cpnLabelGroupDO.setCategory(0);
        cpnLabelGroupDO.setSubCategory(0);
        cpnLabelGroupDO.setSubCategoryDesc("subCategoryDesc");
        cpnLabelGroupDO.setMarketGroup(0);
        cpnLabelGroupDO.setMarketGroupDesc("marketGroupDesc");
        cpnLabelGroupDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelGroupDO.setShowQuickPickInList(false);
        cpnLabelGroupDO.setShowQuickNoticeInList(false);
        cpnLabelGroupDO.setQuickNoticeIcon("quickNoticeIcon");
        cpnLabelGroupDO.setShowPriority(0);
        cpnLabelGroupDO.setNoticeContent("noticeContent");
        when(mockMapper.to(any(CpnLabelGroup.class))).thenReturn(cpnLabelGroupDO);

        // Run the test
        final Map<Long, CpnLabelGroupDO> result = queryAllLabelGroupServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_LabelServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllLabelGroup()).thenReturn(Collections.emptyList());

        // Configure LabelGroupMapper.to(...).
        final CpnLabelGroupDO cpnLabelGroupDO = new CpnLabelGroupDO();
        cpnLabelGroupDO.setId(0L);
        cpnLabelGroupDO.setCategory(0);
        cpnLabelGroupDO.setSubCategory(0);
        cpnLabelGroupDO.setSubCategoryDesc("subCategoryDesc");
        cpnLabelGroupDO.setMarketGroup(0);
        cpnLabelGroupDO.setMarketGroupDesc("marketGroupDesc");
        cpnLabelGroupDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelGroupDO.setShowQuickPickInList(false);
        cpnLabelGroupDO.setShowQuickNoticeInList(false);
        cpnLabelGroupDO.setQuickNoticeIcon("quickNoticeIcon");
        cpnLabelGroupDO.setShowPriority(0);
        cpnLabelGroupDO.setNoticeContent("noticeContent");
        when(mockMapper.to(any(CpnLabelGroup.class))).thenReturn(cpnLabelGroupDO);

        // Run the test
        final Map<Long, CpnLabelGroupDO> result = queryAllLabelGroupServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_LabelServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllLabelGroup()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllLabelGroupServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure LabelService.queryAllLabelGroup(...).
        final CpnLabelGroup cpnLabelGroup = new CpnLabelGroup();
        cpnLabelGroup.setId(0L);
        cpnLabelGroup.setCategory(0);
        cpnLabelGroup.setSubCategory(0);
        cpnLabelGroup.setSubCategoryDesc("subCategoryDesc");
        cpnLabelGroup.setMarketGroup(0);
        cpnLabelGroup.setMarketGroupDesc("marketGroupDesc");
        cpnLabelGroup.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelGroup.setShowQuickPickInList(false);
        cpnLabelGroup.setShowQuickNoticeInList(false);
        cpnLabelGroup.setQuickNoticeIcon("quickNoticeIcon");
        cpnLabelGroup.setShowPriority(0);
        cpnLabelGroup.setNoticeContent("noticeContent");
        final List<CpnLabelGroup> cpnLabelGroups = Arrays.asList(cpnLabelGroup);
        when(mockService.queryAllLabelGroup()).thenReturn(cpnLabelGroups);

        // Configure LabelGroupMapper.to(...).
        final CpnLabelGroupDO cpnLabelGroupDO = new CpnLabelGroupDO();
        cpnLabelGroupDO.setId(0L);
        cpnLabelGroupDO.setCategory(0);
        cpnLabelGroupDO.setSubCategory(0);
        cpnLabelGroupDO.setSubCategoryDesc("subCategoryDesc");
        cpnLabelGroupDO.setMarketGroup(0);
        cpnLabelGroupDO.setMarketGroupDesc("marketGroupDesc");
        cpnLabelGroupDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelGroupDO.setShowQuickPickInList(false);
        cpnLabelGroupDO.setShowQuickNoticeInList(false);
        cpnLabelGroupDO.setQuickNoticeIcon("quickNoticeIcon");
        cpnLabelGroupDO.setShowPriority(0);
        cpnLabelGroupDO.setNoticeContent("noticeContent");
        when(mockMapper.to(any(CpnLabelGroup.class))).thenReturn(cpnLabelGroupDO);

        // Run the test
        final Map<Long, CpnLabelGroupDO> result = queryAllLabelGroupServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_LabelServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllLabelGroup()).thenReturn(Collections.emptyList());

        // Configure LabelGroupMapper.to(...).
        final CpnLabelGroupDO cpnLabelGroupDO = new CpnLabelGroupDO();
        cpnLabelGroupDO.setId(0L);
        cpnLabelGroupDO.setCategory(0);
        cpnLabelGroupDO.setSubCategory(0);
        cpnLabelGroupDO.setSubCategoryDesc("subCategoryDesc");
        cpnLabelGroupDO.setMarketGroup(0);
        cpnLabelGroupDO.setMarketGroupDesc("marketGroupDesc");
        cpnLabelGroupDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelGroupDO.setShowQuickPickInList(false);
        cpnLabelGroupDO.setShowQuickNoticeInList(false);
        cpnLabelGroupDO.setQuickNoticeIcon("quickNoticeIcon");
        cpnLabelGroupDO.setShowPriority(0);
        cpnLabelGroupDO.setNoticeContent("noticeContent");
        when(mockMapper.to(any(CpnLabelGroup.class))).thenReturn(cpnLabelGroupDO);

        // Run the test
        final Map<Long, CpnLabelGroupDO> result = queryAllLabelGroupServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_LabelServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllLabelGroup()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllLabelGroupServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
