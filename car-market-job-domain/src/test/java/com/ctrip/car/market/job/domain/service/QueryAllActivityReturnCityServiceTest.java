package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.ActReturnCityInfoDO;
import com.ctrip.car.market.job.domain.mapper.ActReturnCityInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.ActReturnCityinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllActivityReturnCityServiceTest {

    @Mock
    private ActivityService mockService;
    @Mock
    private ActReturnCityInfoMapper mockMapper;

    @InjectMocks
    private QueryAllActivityReturnCityService queryAllActivityReturnCityServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure ActivityService.queryActivityReturnCity(...).
        final ActReturnCityinfo actReturnCityinfo = new ActReturnCityinfo();
        actReturnCityinfo.setId(0L);
        actReturnCityinfo.setActivityId(0L);
        actReturnCityinfo.setCityId(0);
        actReturnCityinfo.setIsActive(false);
        actReturnCityinfo.setDataChangeCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<ActReturnCityinfo> actReturnCityinfos = Arrays.asList(actReturnCityinfo);
        when(mockService.queryActivityReturnCity()).thenReturn(actReturnCityinfos);

        // Configure ActReturnCityInfoMapper.to(...).
        final ActReturnCityInfoDO actReturnCityInfoDO = new ActReturnCityInfoDO();
        actReturnCityInfoDO.setId(0L);
        actReturnCityInfoDO.setActivityId(0L);
        actReturnCityInfoDO.setCityId(0);
        actReturnCityInfoDO.setIsActive(false);
        actReturnCityInfoDO.setDataChangeCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ActReturnCityinfo.class))).thenReturn(actReturnCityInfoDO);

        // Run the test
        final List<ActReturnCityInfoDO> result = queryAllActivityReturnCityServiceUnderTest.load("area", 0L);

        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }

    @Test
    public void testLoad_ActivityServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryActivityReturnCity()).thenReturn(Collections.emptyList());

        // Run the test
        final List<ActReturnCityInfoDO> result = queryAllActivityReturnCityServiceUnderTest.load("area", 0L);

        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_ActivityServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryActivityReturnCity()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityReturnCityServiceUnderTest.load("area", 0L))
                .isInstanceOf(SQLException.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure ActivityService.queryActivityReturnCity(...).
        final ActReturnCityinfo actReturnCityinfo = new ActReturnCityinfo();
        actReturnCityinfo.setId(0L);
        actReturnCityinfo.setActivityId(0L);
        actReturnCityinfo.setCityId(0);
        actReturnCityinfo.setIsActive(false);
        actReturnCityinfo.setDataChangeCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<ActReturnCityinfo> actReturnCityinfos = Arrays.asList(actReturnCityinfo);
        when(mockService.queryActivityReturnCity()).thenReturn(actReturnCityinfos);

        // Configure ActReturnCityInfoMapper.to(...).
        final ActReturnCityInfoDO actReturnCityInfoDO = new ActReturnCityInfoDO();
        actReturnCityInfoDO.setId(0L);
        actReturnCityInfoDO.setActivityId(0L);
        actReturnCityInfoDO.setCityId(0);
        actReturnCityInfoDO.setIsActive(false);
        actReturnCityInfoDO.setDataChangeCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ActReturnCityinfo.class))).thenReturn(actReturnCityInfoDO);

        // Run the test
        final Map<Long, List<ActReturnCityInfoDO>> result = queryAllActivityReturnCityServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));

        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void testLoadAll_ActivityServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryActivityReturnCity()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, List<ActReturnCityInfoDO>> result = queryAllActivityReturnCityServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));

        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testLoadAll_ActivityServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryActivityReturnCity()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityReturnCityServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)))).isInstanceOf(SQLException.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure ActivityService.queryActivityReturnCity(...).
        final ActReturnCityinfo actReturnCityinfo = new ActReturnCityinfo();
        actReturnCityinfo.setId(0L);
        actReturnCityinfo.setActivityId(0L);
        actReturnCityinfo.setCityId(0);
        actReturnCityinfo.setIsActive(false);
        actReturnCityinfo.setDataChangeCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<ActReturnCityinfo> actReturnCityinfos = Arrays.asList(actReturnCityinfo);
        when(mockService.queryActivityReturnCity()).thenReturn(actReturnCityinfos);

        // Configure ActReturnCityInfoMapper.to(...).
        final ActReturnCityInfoDO actReturnCityInfoDO = new ActReturnCityInfoDO();
        actReturnCityInfoDO.setId(0L);
        actReturnCityInfoDO.setActivityId(0L);
        actReturnCityInfoDO.setCityId(0);
        actReturnCityInfoDO.setIsActive(false);
        actReturnCityInfoDO.setDataChangeCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ActReturnCityinfo.class))).thenReturn(actReturnCityInfoDO);

        // Run the test
        final Map<Long, List<ActReturnCityInfoDO>> result = queryAllActivityReturnCityServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void testPreLoad_ActivityServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryActivityReturnCity()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, List<ActReturnCityInfoDO>> result = queryAllActivityReturnCityServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

       Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testPreLoad_ActivityServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryActivityReturnCity()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityReturnCityServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
