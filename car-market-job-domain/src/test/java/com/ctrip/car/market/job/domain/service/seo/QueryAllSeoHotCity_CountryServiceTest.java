package com.ctrip.car.market.job.domain.service.seo;

import com.ctrip.car.market.job.common.entity.seo.SeoHotCityinfoDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotCityinfoMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotCityinfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllSeoHotCity_CountryServiceTest {

    @Mock
    private MarketDBService mockService;
    @Mock
    private SeoHotCityinfoMapper mockMapper;

    @InjectMocks
    private QueryAllSeoHotCity_CountryService queryAllSeoHotCityCountryServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure MarketDBService.queryAllHotCity(...).
        final SeoHotCityinfo seoHotCityinfo = new SeoHotCityinfo();
        seoHotCityinfo.setId(0L);
        seoHotCityinfo.setCityId(0);
        seoHotCityinfo.setCityName("cityName");
        seoHotCityinfo.setCountryId(0);
        seoHotCityinfo.setUrl("url");
        final List<SeoHotCityinfo> seoHotCityinfos = Arrays.asList(seoHotCityinfo);
        when(mockService.queryAllHotCity()).thenReturn(seoHotCityinfos);

        // Configure SeoHotCityinfoMapper.to(...).
        final SeoHotCityinfoDO seoHotCityinfoDO = new SeoHotCityinfoDO();
        seoHotCityinfoDO.setId(0L);
        seoHotCityinfoDO.setCityId(0);
        seoHotCityinfoDO.setCityName("cityName");
        seoHotCityinfoDO.setCountryId(0);
        seoHotCityinfoDO.setUrl("url");
        when(mockMapper.to(any(SeoHotCityinfo.class))).thenReturn(seoHotCityinfoDO);

        // Run the test
        Assert.assertTrue(CollectionUtils.isNotEmpty(queryAllSeoHotCityCountryServiceUnderTest.load("area", 0)));
    }

    @Test
    public void testLoad_MarketDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllHotCity()).thenReturn(Collections.emptyList());

        // Run the test
        final List<SeoHotCityinfoDO> result = queryAllSeoHotCityCountryServiceUnderTest.load("area", 0);

        // Verify the results
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_MarketDBServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllHotCity()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllSeoHotCityCountryServiceUnderTest.load("area", 0))
                .isInstanceOf(SQLException.class);
    }
}
