package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.ActCityInfoDO;
import com.ctrip.car.market.job.domain.mapper.ActCityInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.ActCityinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllActivityCityServiceTest {

    @Mock
    private ActivityService mockService;
    @Mock
    private ActCityInfoMapper mockMapper;

    @InjectMocks
    private QueryAllActivityCityService queryAllActivityCityServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure ActivityService.queryActivityCity(...).
        final ActCityinfo actCityinfo = new ActCityinfo();
        actCityinfo.setId(0L);
        actCityinfo.setActivityId(0L);
        actCityinfo.setCityId(0);
        actCityinfo.setIsActive(false);
        actCityinfo.setDataChangeCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<ActCityinfo> actCityinfos = Arrays.asList(actCityinfo);
        when(mockService.queryActivityCity()).thenReturn(actCityinfos);

        // Configure ActCityInfoMapper.to(...).
        final ActCityInfoDO actCityInfoDO = new ActCityInfoDO();
        actCityInfoDO.setId(0L);
        actCityInfoDO.setActivityId(0L);
        actCityInfoDO.setCityId(0);
        actCityInfoDO.setIsActive(false);
        actCityInfoDO.setDataChangeCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ActCityinfo.class))).thenReturn(actCityInfoDO);

        // Run the test
        final List<ActCityInfoDO> result = queryAllActivityCityServiceUnderTest.load("area", 0L);

        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }

    @Test
    public void testLoad_ActivityServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryActivityCity()).thenReturn(Collections.emptyList());

        // Run the test
        final List<ActCityInfoDO> result = queryAllActivityCityServiceUnderTest.load("area", 0L);

        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_ActivityServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryActivityCity()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityCityServiceUnderTest.load("area", 0L))
                .isInstanceOf(SQLException.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure ActivityService.queryActivityCity(...).
        final ActCityinfo actCityinfo = new ActCityinfo();
        actCityinfo.setId(0L);
        actCityinfo.setActivityId(0L);
        actCityinfo.setCityId(0);
        actCityinfo.setIsActive(false);
        actCityinfo.setDataChangeCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<ActCityinfo> actCityinfos = Arrays.asList(actCityinfo);
        when(mockService.queryActivityCity()).thenReturn(actCityinfos);

        // Configure ActCityInfoMapper.to(...).
        final ActCityInfoDO actCityInfoDO = new ActCityInfoDO();
        actCityInfoDO.setId(0L);
        actCityInfoDO.setActivityId(0L);
        actCityInfoDO.setCityId(0);
        actCityInfoDO.setIsActive(false);
        actCityInfoDO.setDataChangeCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ActCityinfo.class))).thenReturn(actCityInfoDO);

        // Run the test
        final Map<Long, List<ActCityInfoDO>> result = queryAllActivityCityServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));

        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void testLoadAll_ActivityServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryActivityCity()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, List<ActCityInfoDO>> result = queryAllActivityCityServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));

        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testLoadAll_ActivityServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryActivityCity()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityCityServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)))).isInstanceOf(SQLException.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure ActivityService.queryActivityCity(...).
        final ActCityinfo actCityinfo = new ActCityinfo();
        actCityinfo.setId(0L);
        actCityinfo.setActivityId(0L);
        actCityinfo.setCityId(0);
        actCityinfo.setIsActive(false);
        actCityinfo.setDataChangeCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<ActCityinfo> actCityinfos = Arrays.asList(actCityinfo);
        when(mockService.queryActivityCity()).thenReturn(actCityinfos);

        // Configure ActCityInfoMapper.to(...).
        final ActCityInfoDO actCityInfoDO = new ActCityInfoDO();
        actCityInfoDO.setId(0L);
        actCityInfoDO.setActivityId(0L);
        actCityInfoDO.setCityId(0);
        actCityInfoDO.setIsActive(false);
        actCityInfoDO.setDataChangeCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ActCityinfo.class))).thenReturn(actCityInfoDO);

        // Run the test
        final Map<Long, List<ActCityInfoDO>> result = queryAllActivityCityServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void testPreLoad_ActivityServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryActivityCity()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, List<ActCityInfoDO>> result = queryAllActivityCityServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testPreLoad_ActivityServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryActivityCity()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityCityServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
