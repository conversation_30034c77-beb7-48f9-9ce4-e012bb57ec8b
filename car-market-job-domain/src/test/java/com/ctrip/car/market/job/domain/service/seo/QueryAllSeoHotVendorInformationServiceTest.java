package com.ctrip.car.market.job.domain.service.seo;

import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotVendorInformationMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotVendorInformation;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllSeoHotVendorInformationServiceTest {

    @Mock
    private MarketDBService mockService;
    @Mock
    private SeoHotVendorInformationMapper mockMapper;

    @InjectMocks
    private QueryAllSeoHotVendorInformationService queryAllSeoHotCityCountryServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure MarketDBService.queryAllHotCity(...).
        final SeoHotVendorInformation seoHotCityinfo = new SeoHotVendorInformation();
        seoHotCityinfo.setId(0L);
        seoHotCityinfo.setCityId(0);
        seoHotCityinfo.setCityName("cityName");
        final List<SeoHotVendorInformation> seoHotCityinfos = Arrays.asList(seoHotCityinfo);
        when(mockService.queryALlVendorInformation()).thenReturn(seoHotCityinfos);

        // Configure SeoHotCityinfoMapper.to(...).
        final SeoHotVendorInformationDO seoHotCityinfoDO = new SeoHotVendorInformationDO();
        seoHotCityinfoDO.setId(0L);
        seoHotCityinfoDO.setCityId(0);
        seoHotCityinfoDO.setCityName("cityName");
        when(mockMapper.to(any(SeoHotVendorInformation.class))).thenReturn(seoHotCityinfoDO);

        // Run the test
        Assert.assertFalse(CollectionUtils.isNotEmpty(queryAllSeoHotCityCountryServiceUnderTest.load("area", "sd")));
    }

    @Test
    public void testLoad_MarketDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryALlVendorInformation()).thenReturn(Collections.emptyList());

        // Run the test
        final List<SeoHotVendorInformationDO> result = queryAllSeoHotCityCountryServiceUnderTest.load("area", "sd");

        // Verify the results
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_MarketDBServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryALlVendorInformation()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllSeoHotCityCountryServiceUnderTest.load("area", "sd"))
                .isInstanceOf(SQLException.class);
    }

    @Test
    public void test_preLoad() throws Exception {
        when(mockService.queryALlVendorInformation()).thenReturn(Collections.emptyList());

        final Map<String, List<SeoHotVendorInformationDO>> result = queryAllSeoHotCityCountryServiceUnderTest.preLoad("area", null);

        // Verify the results
        Assert.assertTrue(result.isEmpty());
    }
}
