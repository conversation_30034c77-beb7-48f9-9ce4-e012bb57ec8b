package com.ctrip.car.market.job.domain.service.seo;

import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotDestinatioinfoMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotDestinatioinfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllSeoHotPoi_CountryServiceTest {

    @Mock
    private MarketDBService mockService;
    @Mock
    private SeoHotDestinatioinfoMapper mockMapper;

    @InjectMocks
    private QueryAllSeoHotPoi_CountryService queryAllSeoHotPoiCountryServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure MarketDBService.queryAllHotPoi(...).
        final SeoHotDestinatioinfo seoHotDestinatioinfo = new SeoHotDestinatioinfo();
        seoHotDestinatioinfo.setId(0L);
        seoHotDestinatioinfo.setPoiType(0);
        seoHotDestinatioinfo.setPoiCode("poiCode");
        seoHotDestinatioinfo.setPoiName("poiName");
        seoHotDestinatioinfo.setCountryId(0);
        final List<SeoHotDestinatioinfo> seoHotDestinatioinfos = Arrays.asList(seoHotDestinatioinfo);
        when(mockService.queryAllHotPoi()).thenReturn(seoHotDestinatioinfos);

        // Configure SeoHotDestinatioinfoMapper.to(...).
        final SeoHotDestinatioinfoDO seoHotDestinatioinfoDO = new SeoHotDestinatioinfoDO();
        seoHotDestinatioinfoDO.setId(0L);
        seoHotDestinatioinfoDO.setPoiType(0);
        seoHotDestinatioinfoDO.setPoiId(0L);
        seoHotDestinatioinfoDO.setPoiCode("poiCode");
        seoHotDestinatioinfoDO.setCountryId(0);
        when(mockMapper.to(any(SeoHotDestinatioinfo.class))).thenReturn(seoHotDestinatioinfoDO);

        // Run the test
        final List<SeoHotDestinatioinfoDO> result = queryAllSeoHotPoiCountryServiceUnderTest.load("area", 0);

        // Verify the results
        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }

    @Test
    public void testLoad_MarketDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllHotPoi()).thenReturn(Collections.emptyList());

        // Run the test
        final List<SeoHotDestinatioinfoDO> result = queryAllSeoHotPoiCountryServiceUnderTest.load("area", 0);

        // Verify the results
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_MarketDBServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllHotPoi()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllSeoHotPoiCountryServiceUnderTest.load("area", 0))
                .isInstanceOf(SQLException.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure MarketDBService.queryAllHotPoi(...).
        final SeoHotDestinatioinfo seoHotDestinatioinfo = new SeoHotDestinatioinfo();
        seoHotDestinatioinfo.setId(0L);
        seoHotDestinatioinfo.setPoiType(0);
        seoHotDestinatioinfo.setPoiCode("poiCode");
        seoHotDestinatioinfo.setPoiName("poiName");
        seoHotDestinatioinfo.setCountryId(0);
        final List<SeoHotDestinatioinfo> seoHotDestinatioinfos = Arrays.asList(seoHotDestinatioinfo);
        when(mockService.queryAllHotPoi()).thenReturn(seoHotDestinatioinfos);

        // Configure SeoHotDestinatioinfoMapper.to(...).
        final SeoHotDestinatioinfoDO seoHotDestinatioinfoDO = new SeoHotDestinatioinfoDO();
        seoHotDestinatioinfoDO.setId(0L);
        seoHotDestinatioinfoDO.setPoiType(0);
        seoHotDestinatioinfoDO.setPoiId(0L);
        seoHotDestinatioinfoDO.setPoiCode("poiCode");
        seoHotDestinatioinfoDO.setCountryId(0);
        when(mockMapper.to(any(SeoHotDestinatioinfo.class))).thenReturn(seoHotDestinatioinfoDO);

        // Run the test
        final Map<Integer, List<SeoHotDestinatioinfoDO>> result = queryAllSeoHotPoiCountryServiceUnderTest.loadAll(
                "area", new HashSet<>(Arrays.asList(0)));

        // Verify the results
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void testLoadAll_MarketDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllHotPoi()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Integer, List<SeoHotDestinatioinfoDO>> result = queryAllSeoHotPoiCountryServiceUnderTest.loadAll(
                "area", new HashSet<>(Arrays.asList(0)));

        // Verify the results
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void testLoadAll_MarketDBServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllHotPoi()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllSeoHotPoiCountryServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0)))).isInstanceOf(SQLException.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure MarketDBService.queryAllHotPoi(...).
        final SeoHotDestinatioinfo seoHotDestinatioinfo = new SeoHotDestinatioinfo();
        seoHotDestinatioinfo.setId(0L);
        seoHotDestinatioinfo.setPoiType(0);
        seoHotDestinatioinfo.setPoiCode("poiCode");
        seoHotDestinatioinfo.setPoiName("poiName");
        seoHotDestinatioinfo.setCountryId(0);
        final List<SeoHotDestinatioinfo> seoHotDestinatioinfos = Arrays.asList(seoHotDestinatioinfo);
        when(mockService.queryAllHotPoi()).thenReturn(seoHotDestinatioinfos);

        // Configure SeoHotDestinatioinfoMapper.to(...).
        final SeoHotDestinatioinfoDO seoHotDestinatioinfoDO = new SeoHotDestinatioinfoDO();
        seoHotDestinatioinfoDO.setId(0L);
        seoHotDestinatioinfoDO.setPoiType(0);
        seoHotDestinatioinfoDO.setPoiId(0L);
        seoHotDestinatioinfoDO.setPoiCode("poiCode");
        seoHotDestinatioinfoDO.setCountryId(0);
        when(mockMapper.to(any(SeoHotDestinatioinfo.class))).thenReturn(seoHotDestinatioinfoDO);

        // Run the test
        final Map<Integer, List<SeoHotDestinatioinfoDO>> result = queryAllSeoHotPoiCountryServiceUnderTest.preLoad(
                "area", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void testPreLoad_MarketDBServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllHotPoi()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Integer, List<SeoHotDestinatioinfoDO>> result = queryAllSeoHotPoiCountryServiceUnderTest.preLoad(
                "area", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        Assert.assertTrue(Objects.nonNull(result));
    }

    @Test
    public void testPreLoad_MarketDBServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllHotPoi()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllSeoHotPoiCountryServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
