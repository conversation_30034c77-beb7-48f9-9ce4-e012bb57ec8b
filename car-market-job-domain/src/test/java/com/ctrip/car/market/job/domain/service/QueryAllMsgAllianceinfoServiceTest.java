package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.MsgAllianceinfoDO;
import com.ctrip.car.market.job.domain.mapper.MsgAllianceinfoMapper;
import com.ctrip.car.market.job.repository.entity.MsgAllianceinfo;
import com.ctrip.car.market.job.repository.service.LogDBService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllMsgAllianceinfoServiceTest {

    @Mock
    private LogDBService mockService;
    @Mock
    private MsgAllianceinfoMapper mockMapper;

    @InjectMocks
    private QueryAllMsgAllianceinfoService queryAllMsgAllianceinfoServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure LogDBService.queryAllMsgAllianceinfo(...).
        final MsgAllianceinfo msgAllianceinfo = new MsgAllianceinfo();
        msgAllianceinfo.setID(0);
        msgAllianceinfo.setAllianceID(0L);
        msgAllianceinfo.setAllianceName("allianceName");
        msgAllianceinfo.setScene(0);
        msgAllianceinfo.setAnonymous(false);
        msgAllianceinfo.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        msgAllianceinfo.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<MsgAllianceinfo> msgAllianceinfos = Arrays.asList(msgAllianceinfo);
        when(mockService.queryAllMsgAllianceinfo()).thenReturn(msgAllianceinfos);

        // Configure MsgAllianceinfoMapper.to(...).
        final MsgAllianceinfoDO msgAllianceinfoDO = new MsgAllianceinfoDO();
        msgAllianceinfoDO.setID(0);
        msgAllianceinfoDO.setAllianceID(0L);
        msgAllianceinfoDO.setAllianceName("allianceName");
        msgAllianceinfoDO.setScene(0);
        msgAllianceinfoDO.setAnonymous(false);
        msgAllianceinfoDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        msgAllianceinfoDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(MsgAllianceinfo.class))).thenReturn(msgAllianceinfoDO);

        // Run the test
        final List<MsgAllianceinfoDO> result = queryAllMsgAllianceinfoServiceUnderTest.load("area", "key");
        Assert.isNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_LogDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllMsgAllianceinfo()).thenReturn(Collections.emptyList());

        // Configure MsgAllianceinfoMapper.to(...).
        final MsgAllianceinfoDO msgAllianceinfoDO = new MsgAllianceinfoDO();
        msgAllianceinfoDO.setID(0);
        msgAllianceinfoDO.setAllianceID(0L);
        msgAllianceinfoDO.setAllianceName("allianceName");
        msgAllianceinfoDO.setScene(0);
        msgAllianceinfoDO.setAnonymous(false);
        msgAllianceinfoDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        msgAllianceinfoDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(MsgAllianceinfo.class))).thenReturn(msgAllianceinfoDO);

        // Run the test
        final List<MsgAllianceinfoDO> result = queryAllMsgAllianceinfoServiceUnderTest.load("area", "key");

        // Verify the results
        Assert.isNull(result);
    }

    @Test
    public void testLoad_LogDBServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllMsgAllianceinfo()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllMsgAllianceinfoServiceUnderTest.load("area", "key"))
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure LogDBService.queryAllMsgAllianceinfo(...).
        final MsgAllianceinfo msgAllianceinfo = new MsgAllianceinfo();
        msgAllianceinfo.setID(0);
        msgAllianceinfo.setAllianceID(0L);
        msgAllianceinfo.setAllianceName("allianceName");
        msgAllianceinfo.setScene(0);
        msgAllianceinfo.setAnonymous(false);
        msgAllianceinfo.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        msgAllianceinfo.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<MsgAllianceinfo> msgAllianceinfos = Arrays.asList(msgAllianceinfo);
        when(mockService.queryAllMsgAllianceinfo()).thenReturn(msgAllianceinfos);

        // Configure MsgAllianceinfoMapper.to(...).
        final MsgAllianceinfoDO msgAllianceinfoDO = new MsgAllianceinfoDO();
        msgAllianceinfoDO.setID(0);
        msgAllianceinfoDO.setAllianceID(0L);
        msgAllianceinfoDO.setAllianceName("allianceName");
        msgAllianceinfoDO.setScene(0);
        msgAllianceinfoDO.setAnonymous(false);
        msgAllianceinfoDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        msgAllianceinfoDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(MsgAllianceinfo.class))).thenReturn(msgAllianceinfoDO);

        // Run the test
        final Map<String, List<MsgAllianceinfoDO>> result = queryAllMsgAllianceinfoServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_LogDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllMsgAllianceinfo()).thenReturn(Collections.emptyList());

        // Configure MsgAllianceinfoMapper.to(...).
        final MsgAllianceinfoDO msgAllianceinfoDO = new MsgAllianceinfoDO();
        msgAllianceinfoDO.setID(0);
        msgAllianceinfoDO.setAllianceID(0L);
        msgAllianceinfoDO.setAllianceName("allianceName");
        msgAllianceinfoDO.setScene(0);
        msgAllianceinfoDO.setAnonymous(false);
        msgAllianceinfoDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        msgAllianceinfoDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(MsgAllianceinfo.class))).thenReturn(msgAllianceinfoDO);

        // Run the test
        final Map<String, List<MsgAllianceinfoDO>> result = queryAllMsgAllianceinfoServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_LogDBServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllMsgAllianceinfo()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllMsgAllianceinfoServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure LogDBService.queryAllMsgAllianceinfo(...).
        final MsgAllianceinfo msgAllianceinfo = new MsgAllianceinfo();
        msgAllianceinfo.setID(0);
        msgAllianceinfo.setAllianceID(0L);
        msgAllianceinfo.setAllianceName("allianceName");
        msgAllianceinfo.setScene(0);
        msgAllianceinfo.setAnonymous(false);
        msgAllianceinfo.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        msgAllianceinfo.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<MsgAllianceinfo> msgAllianceinfos = Arrays.asList(msgAllianceinfo);
        when(mockService.queryAllMsgAllianceinfo()).thenReturn(msgAllianceinfos);

        // Configure MsgAllianceinfoMapper.to(...).
        final MsgAllianceinfoDO msgAllianceinfoDO = new MsgAllianceinfoDO();
        msgAllianceinfoDO.setID(0);
        msgAllianceinfoDO.setAllianceID(0L);
        msgAllianceinfoDO.setAllianceName("allianceName");
        msgAllianceinfoDO.setScene(0);
        msgAllianceinfoDO.setAnonymous(false);
        msgAllianceinfoDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        msgAllianceinfoDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(MsgAllianceinfo.class))).thenReturn(msgAllianceinfoDO);

        // Run the test
        final Map<String, List<MsgAllianceinfoDO>> result = queryAllMsgAllianceinfoServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_LogDBServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllMsgAllianceinfo()).thenReturn(Collections.emptyList());

        // Configure MsgAllianceinfoMapper.to(...).
        final MsgAllianceinfoDO msgAllianceinfoDO = new MsgAllianceinfoDO();
        msgAllianceinfoDO.setID(0);
        msgAllianceinfoDO.setAllianceID(0L);
        msgAllianceinfoDO.setAllianceName("allianceName");
        msgAllianceinfoDO.setScene(0);
        msgAllianceinfoDO.setAnonymous(false);
        msgAllianceinfoDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        msgAllianceinfoDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(MsgAllianceinfo.class))).thenReturn(msgAllianceinfoDO);

        // Run the test
        final Map<String, List<MsgAllianceinfoDO>> result = queryAllMsgAllianceinfoServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_LogDBServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllMsgAllianceinfo()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllMsgAllianceinfoServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
