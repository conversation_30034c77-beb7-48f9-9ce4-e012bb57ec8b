package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.proxy.IgtGeoServiceProxy;
import com.ctrip.car.market.job.repository.entity.CarPoiMapping;
import com.ctrip.car.market.job.repository.service.PoiService;
import com.ctrip.igt.geo.interfaces.dto.AddressExtensionDTO;
import com.ctrip.igt.geo.interfaces.dto.FuzzyAddressDTO;
import com.ctrip.igt.geo.interfaces.message.QueryFuzzyAddressResponseType;
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.*;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PoiMappingScheduleTest {

    @Mock
    private PoiService mockPoiService;
    @Mock
    private IgtGeoServiceProxy mockIgtGeoServiceProxy;

    @InjectMocks
    private PoiMappingSchedule poiMappingScheduleUnderTest;

    @Test
    public void testTask() throws Exception {
        // Setup
        // Configure PoiService.query(...).
        final CarPoiMapping carPoiMapping = new CarPoiMapping();
        carPoiMapping.setPoiId(0L);
        carPoiMapping.setPoiName("poiName");
        carPoiMapping.setCityId(0L);
        carPoiMapping.setCarPlaceId("carPlaceId");
        carPoiMapping.setIsActive(false);
        final List<CarPoiMapping> carPoiMappings = Arrays.asList(carPoiMapping);
        when(mockPoiService.query(0L)).thenReturn(carPoiMappings);

        // Configure IgtGeoServiceProxy.queryFuzzyAddress(...).
        final QueryFuzzyAddressResponseType queryFuzzyAddressResponseType = new QueryFuzzyAddressResponseType();
        final ResponseStatusType responseStatus = new ResponseStatusType();
        responseStatus.setTimestamp(Calendar.getInstance(Locale.US));
        queryFuzzyAddressResponseType.setResponseStatus(responseStatus);
        final FuzzyAddressDTO addressDTO = new FuzzyAddressDTO();
        final AddressExtensionDTO addressExtInfo = new AddressExtensionDTO();
        addressExtInfo.setCarPlaceId("carPlaceId");
        addressDTO.setAddressExtInfo(addressExtInfo);
        queryFuzzyAddressResponseType.setAddressList(Arrays.asList(addressDTO));
        when(mockIgtGeoServiceProxy.queryFuzzyAddress(0L, "poiName")).thenReturn(queryFuzzyAddressResponseType);

        when(mockPoiService.update(any(CarPoiMapping.class))).thenReturn(false);

        // Run the test
        poiMappingScheduleUnderTest.task();

        // Verify the results
    }

    @Test
    public void testTask_PoiServiceQueryReturnsNoItems() throws Exception {
        // Setup
        when(mockPoiService.query(0L)).thenReturn(Collections.emptyList());

        // Run the test
        poiMappingScheduleUnderTest.task();

        // Verify the results
    }

    @Test
    public void testTask_PoiServiceQueryThrowsException() throws Exception {
        // Setup
        when(mockPoiService.query(0L)).thenThrow(Exception.class);

        // Run the test
        poiMappingScheduleUnderTest.task();

        // Verify the results
    }

    @Test
    public void testTask_IgtGeoServiceProxyReturnsNull() throws Exception {
        // Setup
        // Configure PoiService.query(...).
        final CarPoiMapping carPoiMapping = new CarPoiMapping();
        carPoiMapping.setPoiId(0L);
        carPoiMapping.setPoiName("poiName");
        carPoiMapping.setCityId(0L);
        carPoiMapping.setCarPlaceId("carPlaceId");
        carPoiMapping.setIsActive(false);
        final List<CarPoiMapping> carPoiMappings = Arrays.asList(carPoiMapping);
        when(mockPoiService.query(0L)).thenReturn(carPoiMappings);

        when(mockIgtGeoServiceProxy.queryFuzzyAddress(0L, "poiName")).thenReturn(null);
        when(mockPoiService.update(any(CarPoiMapping.class))).thenReturn(false);

        // Run the test
        poiMappingScheduleUnderTest.task();

        // Verify the results
    }

    @Test
    public void testTask_PoiServiceUpdateThrowsException() throws Exception {
        // Setup
        // Configure PoiService.query(...).
        final CarPoiMapping carPoiMapping = new CarPoiMapping();
        carPoiMapping.setPoiId(0L);
        carPoiMapping.setPoiName("poiName");
        carPoiMapping.setCityId(0L);
        carPoiMapping.setCarPlaceId("carPlaceId");
        carPoiMapping.setIsActive(false);
        final List<CarPoiMapping> carPoiMappings = Arrays.asList(carPoiMapping);
        when(mockPoiService.query(0L)).thenReturn(carPoiMappings);

        // Configure IgtGeoServiceProxy.queryFuzzyAddress(...).
        final QueryFuzzyAddressResponseType queryFuzzyAddressResponseType = new QueryFuzzyAddressResponseType();
        final ResponseStatusType responseStatus = new ResponseStatusType();
        responseStatus.setTimestamp(Calendar.getInstance(Locale.US));
        queryFuzzyAddressResponseType.setResponseStatus(responseStatus);
        final FuzzyAddressDTO addressDTO = new FuzzyAddressDTO();
        final AddressExtensionDTO addressExtInfo = new AddressExtensionDTO();
        addressExtInfo.setCarPlaceId("carPlaceId");
        addressDTO.setAddressExtInfo(addressExtInfo);
        queryFuzzyAddressResponseType.setAddressList(Arrays.asList(addressDTO));
        when(mockIgtGeoServiceProxy.queryFuzzyAddress(0L, "poiName")).thenReturn(queryFuzzyAddressResponseType);

        when(mockPoiService.update(any(CarPoiMapping.class))).thenThrow(Exception.class);

        // Run the test
        poiMappingScheduleUnderTest.task();

        // Verify the results
    }

    @Test
    public void testMatching() throws Exception {
        // Setup
        final CarPoiMapping carPoiMapping = new CarPoiMapping();
        carPoiMapping.setPoiId(0L);
        carPoiMapping.setPoiName("poiName");
        carPoiMapping.setCityId(0L);
        carPoiMapping.setCarPlaceId("carPlaceId");
        carPoiMapping.setIsActive(false);
        final List<CarPoiMapping> list = Arrays.asList(carPoiMapping);

        // Configure IgtGeoServiceProxy.queryFuzzyAddress(...).
        final QueryFuzzyAddressResponseType queryFuzzyAddressResponseType = new QueryFuzzyAddressResponseType();
        final ResponseStatusType responseStatus = new ResponseStatusType();
        responseStatus.setTimestamp(Calendar.getInstance(Locale.US));
        queryFuzzyAddressResponseType.setResponseStatus(responseStatus);
        final FuzzyAddressDTO addressDTO = new FuzzyAddressDTO();
        final AddressExtensionDTO addressExtInfo = new AddressExtensionDTO();
        addressExtInfo.setCarPlaceId("carPlaceId");
        addressDTO.setAddressExtInfo(addressExtInfo);
        queryFuzzyAddressResponseType.setAddressList(Arrays.asList(addressDTO));
        when(mockIgtGeoServiceProxy.queryFuzzyAddress(0L, "poiName")).thenReturn(queryFuzzyAddressResponseType);

        when(mockPoiService.update(any(CarPoiMapping.class))).thenReturn(false);

        // Run the test
        poiMappingScheduleUnderTest.matching(list);

        // Verify the results
    }

    @Test
    public void testMatching_IgtGeoServiceProxyReturnsNull() throws Exception {
        // Setup
        final CarPoiMapping carPoiMapping = new CarPoiMapping();
        carPoiMapping.setPoiId(0L);
        carPoiMapping.setPoiName("poiName");
        carPoiMapping.setCityId(0L);
        carPoiMapping.setCarPlaceId("carPlaceId");
        carPoiMapping.setIsActive(false);
        final List<CarPoiMapping> list = Arrays.asList(carPoiMapping);
        when(mockIgtGeoServiceProxy.queryFuzzyAddress(0L, "poiName")).thenReturn(null);
        when(mockPoiService.update(any(CarPoiMapping.class))).thenReturn(false);

        // Run the test
        poiMappingScheduleUnderTest.matching(list);

        // Verify the results
    }

    @Test
    public void testMatching_PoiServiceThrowsException() throws Exception {
        // Setup
        final CarPoiMapping carPoiMapping = new CarPoiMapping();
        carPoiMapping.setPoiId(0L);
        carPoiMapping.setPoiName("poiName");
        carPoiMapping.setCityId(0L);
        carPoiMapping.setCarPlaceId("carPlaceId");
        carPoiMapping.setIsActive(false);
        final List<CarPoiMapping> list = Arrays.asList(carPoiMapping);

        // Configure IgtGeoServiceProxy.queryFuzzyAddress(...).
        final QueryFuzzyAddressResponseType queryFuzzyAddressResponseType = new QueryFuzzyAddressResponseType();
        final ResponseStatusType responseStatus = new ResponseStatusType();
        responseStatus.setTimestamp(Calendar.getInstance(Locale.US));
        queryFuzzyAddressResponseType.setResponseStatus(responseStatus);
        final FuzzyAddressDTO addressDTO = new FuzzyAddressDTO();
        final AddressExtensionDTO addressExtInfo = new AddressExtensionDTO();
        addressExtInfo.setCarPlaceId("carPlaceId");
        addressDTO.setAddressExtInfo(addressExtInfo);
        queryFuzzyAddressResponseType.setAddressList(Arrays.asList(addressDTO));
        when(mockIgtGeoServiceProxy.queryFuzzyAddress(0L, "poiName")).thenReturn(queryFuzzyAddressResponseType);

        when(mockPoiService.update(any(CarPoiMapping.class))).thenThrow(Exception.class);

        // Run the test
        poiMappingScheduleUnderTest.matching(list);

        // Verify the results
    }
}
