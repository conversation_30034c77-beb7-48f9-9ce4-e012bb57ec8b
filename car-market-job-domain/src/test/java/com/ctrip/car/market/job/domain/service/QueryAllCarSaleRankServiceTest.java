package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.CarSaleRankDO;
import com.ctrip.car.market.job.domain.mapper.CarSaleRankMapper;
import com.ctrip.car.market.job.repository.entity.CarSaleRank;
import com.ctrip.car.market.job.repository.service.BiDBService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllCarSaleRankServiceTest {

    @Mock
    private BiDBService mockService;
    @Mock
    private CarSaleRankMapper mockMapper;

    @InjectMocks
    private QueryAllCarSaleRankService queryAllCarSaleRankServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure BiDBService.queryAllSaleRank(...).
        final CarSaleRank carSaleRank = new CarSaleRank();
        carSaleRank.setId(0L);
        carSaleRank.setVehicleName("vehicleName");
        carSaleRank.setVehicleGroupName("vehicleGroupName");
        carSaleRank.setVehicleId(0);
        carSaleRank.setTrans(0);
        carSaleRank.setDisplacement("displacement");
        carSaleRank.setSeat(0);
        carSaleRank.setDoorNo(0);
        carSaleRank.setCarriageDesc("carriageDesc");
        carSaleRank.setVehicleRank(0);
        carSaleRank.setCityId(0);
        carSaleRank.setVehicleImageUrl("vehicleImageUrl");
        carSaleRank.setActive(false);
        carSaleRank.setDatachageCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        carSaleRank.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<CarSaleRank> carSaleRanks = Arrays.asList(carSaleRank);
        when(mockService.queryAllSaleRank()).thenReturn(carSaleRanks);

        // Configure CarSaleRankMapper.to(...).
        final CarSaleRankDO carSaleRankDO = new CarSaleRankDO();
        carSaleRankDO.setId(0L);
        carSaleRankDO.setVehicleName("vehicleName");
        carSaleRankDO.setVehicleGroupName("vehicleGroupName");
        carSaleRankDO.setVehicleId(0);
        carSaleRankDO.setTrans(0);
        carSaleRankDO.setDisplacement("displacement");
        carSaleRankDO.setSeat(0);
        carSaleRankDO.setDoorNo(0);
        carSaleRankDO.setCarriageDesc("carriageDesc");
        carSaleRankDO.setVehicleRank(0);
        carSaleRankDO.setCityId(0);
        carSaleRankDO.setVehicleImageUrl("vehicleImageUrl");
        carSaleRankDO.setActive(false);
        carSaleRankDO.setDatachageCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        carSaleRankDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(CarSaleRank.class))).thenReturn(carSaleRankDO);

        // Run the test
        try {
            final List<CarSaleRankDO> result = queryAllCarSaleRankServiceUnderTest.load("area", 0);
            Assert.notNull(result);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }

        // Verify the results
    }

    @Test
    public void testLoad_BiDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllSaleRank()).thenReturn(Collections.emptyList());

        // Configure CarSaleRankMapper.to(...).
        final CarSaleRankDO carSaleRankDO = new CarSaleRankDO();
        carSaleRankDO.setId(0L);
        carSaleRankDO.setVehicleName("vehicleName");
        carSaleRankDO.setVehicleGroupName("vehicleGroupName");
        carSaleRankDO.setVehicleId(0);
        carSaleRankDO.setTrans(0);
        carSaleRankDO.setDisplacement("displacement");
        carSaleRankDO.setSeat(0);
        carSaleRankDO.setDoorNo(0);
        carSaleRankDO.setCarriageDesc("carriageDesc");
        carSaleRankDO.setVehicleRank(0);
        carSaleRankDO.setCityId(0);
        carSaleRankDO.setVehicleImageUrl("vehicleImageUrl");
        carSaleRankDO.setActive(false);
        carSaleRankDO.setDatachageCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        carSaleRankDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(CarSaleRank.class))).thenReturn(carSaleRankDO);

        // Run the test
        final List<CarSaleRankDO> result = queryAllCarSaleRankServiceUnderTest.load("area", 0);

        // Verify the results
        Assert.isNull(result);
    }

    @Test
    public void testLoad_BiDBServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllSaleRank()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllCarSaleRankServiceUnderTest.load("area", 0)).isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure BiDBService.queryAllSaleRank(...).
        final CarSaleRank carSaleRank = new CarSaleRank();
        carSaleRank.setId(0L);
        carSaleRank.setVehicleName("vehicleName");
        carSaleRank.setVehicleGroupName("vehicleGroupName");
        carSaleRank.setVehicleId(0);
        carSaleRank.setTrans(0);
        carSaleRank.setDisplacement("displacement");
        carSaleRank.setSeat(0);
        carSaleRank.setDoorNo(0);
        carSaleRank.setCarriageDesc("carriageDesc");
        carSaleRank.setVehicleRank(0);
        carSaleRank.setCityId(0);
        carSaleRank.setVehicleImageUrl("vehicleImageUrl");
        carSaleRank.setActive(false);
        carSaleRank.setDatachageCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        carSaleRank.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<CarSaleRank> carSaleRanks = Arrays.asList(carSaleRank);
        when(mockService.queryAllSaleRank()).thenReturn(carSaleRanks);

        // Configure CarSaleRankMapper.to(...).
        final CarSaleRankDO carSaleRankDO = new CarSaleRankDO();
        carSaleRankDO.setId(0L);
        carSaleRankDO.setVehicleName("vehicleName");
        carSaleRankDO.setVehicleGroupName("vehicleGroupName");
        carSaleRankDO.setVehicleId(0);
        carSaleRankDO.setTrans(0);
        carSaleRankDO.setDisplacement("displacement");
        carSaleRankDO.setSeat(0);
        carSaleRankDO.setDoorNo(0);
        carSaleRankDO.setCarriageDesc("carriageDesc");
        carSaleRankDO.setVehicleRank(0);
        carSaleRankDO.setCityId(0);
        carSaleRankDO.setVehicleImageUrl("vehicleImageUrl");
        carSaleRankDO.setActive(false);
        carSaleRankDO.setDatachageCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        carSaleRankDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(CarSaleRank.class))).thenReturn(carSaleRankDO);

        // Run the test
        final Map<Integer, List<CarSaleRankDO>> result = queryAllCarSaleRankServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_BiDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllSaleRank()).thenReturn(Collections.emptyList());

        // Configure CarSaleRankMapper.to(...).
        final CarSaleRankDO carSaleRankDO = new CarSaleRankDO();
        carSaleRankDO.setId(0L);
        carSaleRankDO.setVehicleName("vehicleName");
        carSaleRankDO.setVehicleGroupName("vehicleGroupName");
        carSaleRankDO.setVehicleId(0);
        carSaleRankDO.setTrans(0);
        carSaleRankDO.setDisplacement("displacement");
        carSaleRankDO.setSeat(0);
        carSaleRankDO.setDoorNo(0);
        carSaleRankDO.setCarriageDesc("carriageDesc");
        carSaleRankDO.setVehicleRank(0);
        carSaleRankDO.setCityId(0);
        carSaleRankDO.setVehicleImageUrl("vehicleImageUrl");
        carSaleRankDO.setActive(false);
        carSaleRankDO.setDatachageCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        carSaleRankDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(CarSaleRank.class))).thenReturn(carSaleRankDO);

        // Run the test
        try {
            final Map<Integer, List<CarSaleRankDO>> result = queryAllCarSaleRankServiceUnderTest.loadAll("area",
                    new HashSet<>(Arrays.asList(0)));
            Assert.notNull(result);
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }

        // Verify the results
    }

    @Test
    public void testLoadAll_BiDBServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllSaleRank()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllCarSaleRankServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0)))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure BiDBService.queryAllSaleRank(...).
        final CarSaleRank carSaleRank = new CarSaleRank();
        carSaleRank.setId(0L);
        carSaleRank.setVehicleName("vehicleName");
        carSaleRank.setVehicleGroupName("vehicleGroupName");
        carSaleRank.setVehicleId(0);
        carSaleRank.setTrans(0);
        carSaleRank.setDisplacement("displacement");
        carSaleRank.setSeat(0);
        carSaleRank.setDoorNo(0);
        carSaleRank.setCarriageDesc("carriageDesc");
        carSaleRank.setVehicleRank(0);
        carSaleRank.setCityId(0);
        carSaleRank.setVehicleImageUrl("vehicleImageUrl");
        carSaleRank.setActive(false);
        carSaleRank.setDatachageCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        carSaleRank.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<CarSaleRank> carSaleRanks = Arrays.asList(carSaleRank);
        when(mockService.queryAllSaleRank()).thenReturn(carSaleRanks);

        // Configure CarSaleRankMapper.to(...).
        final CarSaleRankDO carSaleRankDO = new CarSaleRankDO();
        carSaleRankDO.setId(0L);
        carSaleRankDO.setVehicleName("vehicleName");
        carSaleRankDO.setVehicleGroupName("vehicleGroupName");
        carSaleRankDO.setVehicleId(0);
        carSaleRankDO.setTrans(0);
        carSaleRankDO.setDisplacement("displacement");
        carSaleRankDO.setSeat(0);
        carSaleRankDO.setDoorNo(0);
        carSaleRankDO.setCarriageDesc("carriageDesc");
        carSaleRankDO.setVehicleRank(0);
        carSaleRankDO.setCityId(0);
        carSaleRankDO.setVehicleImageUrl("vehicleImageUrl");
        carSaleRankDO.setActive(false);
        carSaleRankDO.setDatachageCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        carSaleRankDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(CarSaleRank.class))).thenReturn(carSaleRankDO);

        // Run the test
        final Map<Integer, List<CarSaleRankDO>> result = queryAllCarSaleRankServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_BiDBServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllSaleRank()).thenReturn(Collections.emptyList());

        // Configure CarSaleRankMapper.to(...).
        final CarSaleRankDO carSaleRankDO = new CarSaleRankDO();
        carSaleRankDO.setId(0L);
        carSaleRankDO.setVehicleName("vehicleName");
        carSaleRankDO.setVehicleGroupName("vehicleGroupName");
        carSaleRankDO.setVehicleId(0);
        carSaleRankDO.setTrans(0);
        carSaleRankDO.setDisplacement("displacement");
        carSaleRankDO.setSeat(0);
        carSaleRankDO.setDoorNo(0);
        carSaleRankDO.setCarriageDesc("carriageDesc");
        carSaleRankDO.setVehicleRank(0);
        carSaleRankDO.setCityId(0);
        carSaleRankDO.setVehicleImageUrl("vehicleImageUrl");
        carSaleRankDO.setActive(false);
        carSaleRankDO.setDatachageCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        carSaleRankDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(CarSaleRank.class))).thenReturn(carSaleRankDO);

        // Run the test
        final Map<Integer, List<CarSaleRankDO>> result = queryAllCarSaleRankServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_BiDBServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllSaleRank()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllCarSaleRankServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
