package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.CpnProductRedirectUrlDO;
import com.ctrip.car.market.job.domain.mapper.ProductRedirectUrlMapper;
import com.ctrip.car.market.job.repository.entity.CpnProductRedirectUrl;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllProductRedirectUrlServiceTest {

    @Mock
    private RestrictedService mockService;
    @Mock
    private ProductRedirectUrlMapper mockMapper;

    @InjectMocks
    private QueryAllProductRedirectUrlService queryAllProductRedirectUrlServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllProductRedirectUrl(...).
        final CpnProductRedirectUrl cpnProductRedirectUrl = new CpnProductRedirectUrl();
        cpnProductRedirectUrl.setUrlID(0L);
        cpnProductRedirectUrl.setBusinessType(0);
        cpnProductRedirectUrl.setBusinessSubType(0);
        cpnProductRedirectUrl.setRedirectUrl("redirectUrl");
        cpnProductRedirectUrl.setIsValid(0);
        cpnProductRedirectUrl.setUrlType(0);
        cpnProductRedirectUrl.setUrlName("urlName");
        cpnProductRedirectUrl.setRemark("remark");
        cpnProductRedirectUrl.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrl.setDiscribe("discribe");
        cpnProductRedirectUrl.setUnionType("unionType");
        cpnProductRedirectUrl.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrl.setJumpMiniAppId("jumpMiniAppId");
        final List<CpnProductRedirectUrl> cpnProductRedirectUrls = Arrays.asList(cpnProductRedirectUrl);
        when(mockService.queryAllProductRedirectUrl()).thenReturn(cpnProductRedirectUrls);

        // Configure ProductRedirectUrlMapper.to(...).
        final CpnProductRedirectUrlDO cpnProductRedirectUrlDO = new CpnProductRedirectUrlDO();
        cpnProductRedirectUrlDO.setUrlID(0L);
        cpnProductRedirectUrlDO.setBusinessType(0);
        cpnProductRedirectUrlDO.setBusinessSubType(0);
        cpnProductRedirectUrlDO.setRedirectUrl("redirectUrl");
        cpnProductRedirectUrlDO.setIsValid(0);
        cpnProductRedirectUrlDO.setUrlType(0);
        cpnProductRedirectUrlDO.setUrlName("urlName");
        cpnProductRedirectUrlDO.setRemark("remark");
        cpnProductRedirectUrlDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrlDO.setDiscribe("discribe");
        cpnProductRedirectUrlDO.setUnionType("unionType");
        cpnProductRedirectUrlDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrlDO.setJumpMiniAppId("jumpMiniAppId");
        when(mockMapper.to(any(CpnProductRedirectUrl.class))).thenReturn(cpnProductRedirectUrlDO);

        // Run the test
        final List<CpnProductRedirectUrlDO> result = queryAllProductRedirectUrlServiceUnderTest.load("area", "key");
        Assert.isNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllProductRedirectUrl()).thenReturn(Collections.emptyList());

        // Configure ProductRedirectUrlMapper.to(...).
        final CpnProductRedirectUrlDO cpnProductRedirectUrlDO = new CpnProductRedirectUrlDO();
        cpnProductRedirectUrlDO.setUrlID(0L);
        cpnProductRedirectUrlDO.setBusinessType(0);
        cpnProductRedirectUrlDO.setBusinessSubType(0);
        cpnProductRedirectUrlDO.setRedirectUrl("redirectUrl");
        cpnProductRedirectUrlDO.setIsValid(0);
        cpnProductRedirectUrlDO.setUrlType(0);
        cpnProductRedirectUrlDO.setUrlName("urlName");
        cpnProductRedirectUrlDO.setRemark("remark");
        cpnProductRedirectUrlDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrlDO.setDiscribe("discribe");
        cpnProductRedirectUrlDO.setUnionType("unionType");
        cpnProductRedirectUrlDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrlDO.setJumpMiniAppId("jumpMiniAppId");
        when(mockMapper.to(any(CpnProductRedirectUrl.class))).thenReturn(cpnProductRedirectUrlDO);

        // Run the test
        final List<CpnProductRedirectUrlDO> result = queryAllProductRedirectUrlServiceUnderTest.load("area", "key");

        // Verify the results
        Assert.isNull(result);
    }

    @Test
    public void testLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllProductRedirectUrl()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllProductRedirectUrlServiceUnderTest.load("area", "key"))
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllProductRedirectUrl(...).
        final CpnProductRedirectUrl cpnProductRedirectUrl = new CpnProductRedirectUrl();
        cpnProductRedirectUrl.setUrlID(0L);
        cpnProductRedirectUrl.setBusinessType(0);
        cpnProductRedirectUrl.setBusinessSubType(0);
        cpnProductRedirectUrl.setRedirectUrl("redirectUrl");
        cpnProductRedirectUrl.setIsValid(0);
        cpnProductRedirectUrl.setUrlType(0);
        cpnProductRedirectUrl.setUrlName("urlName");
        cpnProductRedirectUrl.setRemark("remark");
        cpnProductRedirectUrl.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrl.setDiscribe("discribe");
        cpnProductRedirectUrl.setUnionType("unionType");
        cpnProductRedirectUrl.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrl.setJumpMiniAppId("jumpMiniAppId");
        final List<CpnProductRedirectUrl> cpnProductRedirectUrls = Arrays.asList(cpnProductRedirectUrl);
        when(mockService.queryAllProductRedirectUrl()).thenReturn(cpnProductRedirectUrls);

        // Configure ProductRedirectUrlMapper.to(...).
        final CpnProductRedirectUrlDO cpnProductRedirectUrlDO = new CpnProductRedirectUrlDO();
        cpnProductRedirectUrlDO.setUrlID(0L);
        cpnProductRedirectUrlDO.setBusinessType(0);
        cpnProductRedirectUrlDO.setBusinessSubType(0);
        cpnProductRedirectUrlDO.setRedirectUrl("redirectUrl");
        cpnProductRedirectUrlDO.setIsValid(0);
        cpnProductRedirectUrlDO.setUrlType(0);
        cpnProductRedirectUrlDO.setUrlName("urlName");
        cpnProductRedirectUrlDO.setRemark("remark");
        cpnProductRedirectUrlDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrlDO.setDiscribe("discribe");
        cpnProductRedirectUrlDO.setUnionType("unionType");
        cpnProductRedirectUrlDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrlDO.setJumpMiniAppId("jumpMiniAppId");
        when(mockMapper.to(any(CpnProductRedirectUrl.class))).thenReturn(cpnProductRedirectUrlDO);

        // Run the test
        final Map<String, List<CpnProductRedirectUrlDO>> result = queryAllProductRedirectUrlServiceUnderTest.loadAll(
                "area", new HashSet<>(Arrays.asList("value")));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllProductRedirectUrl()).thenReturn(Collections.emptyList());

        // Configure ProductRedirectUrlMapper.to(...).
        final CpnProductRedirectUrlDO cpnProductRedirectUrlDO = new CpnProductRedirectUrlDO();
        cpnProductRedirectUrlDO.setUrlID(0L);
        cpnProductRedirectUrlDO.setBusinessType(0);
        cpnProductRedirectUrlDO.setBusinessSubType(0);
        cpnProductRedirectUrlDO.setRedirectUrl("redirectUrl");
        cpnProductRedirectUrlDO.setIsValid(0);
        cpnProductRedirectUrlDO.setUrlType(0);
        cpnProductRedirectUrlDO.setUrlName("urlName");
        cpnProductRedirectUrlDO.setRemark("remark");
        cpnProductRedirectUrlDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrlDO.setDiscribe("discribe");
        cpnProductRedirectUrlDO.setUnionType("unionType");
        cpnProductRedirectUrlDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrlDO.setJumpMiniAppId("jumpMiniAppId");
        when(mockMapper.to(any(CpnProductRedirectUrl.class))).thenReturn(cpnProductRedirectUrlDO);

        // Run the test
        final Map<String, List<CpnProductRedirectUrlDO>> result = queryAllProductRedirectUrlServiceUnderTest.loadAll(
                "area", new HashSet<>(Arrays.asList("value")));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllProductRedirectUrl()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllProductRedirectUrlServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure RestrictedService.queryAllProductRedirectUrl(...).
        final CpnProductRedirectUrl cpnProductRedirectUrl = new CpnProductRedirectUrl();
        cpnProductRedirectUrl.setUrlID(0L);
        cpnProductRedirectUrl.setBusinessType(0);
        cpnProductRedirectUrl.setBusinessSubType(0);
        cpnProductRedirectUrl.setRedirectUrl("redirectUrl");
        cpnProductRedirectUrl.setIsValid(0);
        cpnProductRedirectUrl.setUrlType(0);
        cpnProductRedirectUrl.setUrlName("urlName");
        cpnProductRedirectUrl.setRemark("remark");
        cpnProductRedirectUrl.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrl.setDiscribe("discribe");
        cpnProductRedirectUrl.setUnionType("unionType");
        cpnProductRedirectUrl.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrl.setJumpMiniAppId("jumpMiniAppId");
        final List<CpnProductRedirectUrl> cpnProductRedirectUrls = Arrays.asList(cpnProductRedirectUrl);
        when(mockService.queryAllProductRedirectUrl()).thenReturn(cpnProductRedirectUrls);

        // Configure ProductRedirectUrlMapper.to(...).
        final CpnProductRedirectUrlDO cpnProductRedirectUrlDO = new CpnProductRedirectUrlDO();
        cpnProductRedirectUrlDO.setUrlID(0L);
        cpnProductRedirectUrlDO.setBusinessType(0);
        cpnProductRedirectUrlDO.setBusinessSubType(0);
        cpnProductRedirectUrlDO.setRedirectUrl("redirectUrl");
        cpnProductRedirectUrlDO.setIsValid(0);
        cpnProductRedirectUrlDO.setUrlType(0);
        cpnProductRedirectUrlDO.setUrlName("urlName");
        cpnProductRedirectUrlDO.setRemark("remark");
        cpnProductRedirectUrlDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrlDO.setDiscribe("discribe");
        cpnProductRedirectUrlDO.setUnionType("unionType");
        cpnProductRedirectUrlDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrlDO.setJumpMiniAppId("jumpMiniAppId");
        when(mockMapper.to(any(CpnProductRedirectUrl.class))).thenReturn(cpnProductRedirectUrlDO);

        // Run the test
        final Map<String, List<CpnProductRedirectUrlDO>> result = queryAllProductRedirectUrlServiceUnderTest.preLoad(
                "area", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllProductRedirectUrl()).thenReturn(Collections.emptyList());

        // Configure ProductRedirectUrlMapper.to(...).
        final CpnProductRedirectUrlDO cpnProductRedirectUrlDO = new CpnProductRedirectUrlDO();
        cpnProductRedirectUrlDO.setUrlID(0L);
        cpnProductRedirectUrlDO.setBusinessType(0);
        cpnProductRedirectUrlDO.setBusinessSubType(0);
        cpnProductRedirectUrlDO.setRedirectUrl("redirectUrl");
        cpnProductRedirectUrlDO.setIsValid(0);
        cpnProductRedirectUrlDO.setUrlType(0);
        cpnProductRedirectUrlDO.setUrlName("urlName");
        cpnProductRedirectUrlDO.setRemark("remark");
        cpnProductRedirectUrlDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrlDO.setDiscribe("discribe");
        cpnProductRedirectUrlDO.setUnionType("unionType");
        cpnProductRedirectUrlDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnProductRedirectUrlDO.setJumpMiniAppId("jumpMiniAppId");
        when(mockMapper.to(any(CpnProductRedirectUrl.class))).thenReturn(cpnProductRedirectUrlDO);

        // Run the test
        final Map<String, List<CpnProductRedirectUrlDO>> result = queryAllProductRedirectUrlServiceUnderTest.preLoad(
                "area", new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllProductRedirectUrl()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllProductRedirectUrlServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
