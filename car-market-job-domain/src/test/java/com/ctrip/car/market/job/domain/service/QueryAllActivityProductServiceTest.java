package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.ActProductDO;
import com.ctrip.car.market.job.domain.mapper.ActProductInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.ActProductids;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllActivityProductServiceTest {

    @Mock
    private ActivityService mockService;
    @Mock
    private ActProductInfoMapper mockMapper;

    @InjectMocks
    private QueryAllActivityProductService queryAllActivityProductServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure ActivityService.queryActivityProduct(...).
        final ActProductids actProductids1 = new ActProductids();
        actProductids1.setId(0L);
        actProductids1.setActivityId(0L);
        actProductids1.setStandardPId(0L);
        actProductids1.setIsActive(false);
        actProductids1.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<ActProductids> actProductids = Arrays.asList(actProductids1);
        when(mockService.queryActivityProduct()).thenReturn(actProductids);

        // Configure ActProductInfoMapper.to(...).
        final ActProductDO actProductDO = new ActProductDO();
        actProductDO.setId(0L);
        actProductDO.setActivityId(0L);
        actProductDO.setStandardPId(0L);
        actProductDO.setIsActive(false);
        actProductDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ActProductids.class))).thenReturn(actProductDO);

        // Run the test
        final List<ActProductDO> result = queryAllActivityProductServiceUnderTest.load("area", 0L);

        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }

    @Test
    public void testLoad_ActivityServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryActivityProduct()).thenReturn(Collections.emptyList());

        // Run the test
        final List<ActProductDO> result = queryAllActivityProductServiceUnderTest.load("area", 0L);

        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_ActivityServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryActivityProduct()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityProductServiceUnderTest.load("area", 0L))
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure ActivityService.queryActivityProduct(...).
        final ActProductids actProductids1 = new ActProductids();
        actProductids1.setId(0L);
        actProductids1.setActivityId(0L);
        actProductids1.setStandardPId(0L);
        actProductids1.setIsActive(false);
        actProductids1.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<ActProductids> actProductids = Arrays.asList(actProductids1);
        when(mockService.queryActivityProduct()).thenReturn(actProductids);

        // Configure ActProductInfoMapper.to(...).
        final ActProductDO actProductDO = new ActProductDO();
        actProductDO.setId(0L);
        actProductDO.setActivityId(0L);
        actProductDO.setStandardPId(0L);
        actProductDO.setIsActive(false);
        actProductDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ActProductids.class))).thenReturn(actProductDO);

        // Run the test
        final Map<Long, List<ActProductDO>> result = queryAllActivityProductServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));

        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void testLoadAll_ActivityServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryActivityProduct()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, List<ActProductDO>> result = queryAllActivityProductServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));

        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testLoadAll_ActivityServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryActivityProduct()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityProductServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure ActivityService.queryActivityProduct(...).
        final ActProductids actProductids1 = new ActProductids();
        actProductids1.setId(0L);
        actProductids1.setActivityId(0L);
        actProductids1.setStandardPId(0L);
        actProductids1.setIsActive(false);
        actProductids1.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<ActProductids> actProductids = Arrays.asList(actProductids1);
        when(mockService.queryActivityProduct()).thenReturn(actProductids);

        // Configure ActProductInfoMapper.to(...).
        final ActProductDO actProductDO = new ActProductDO();
        actProductDO.setId(0L);
        actProductDO.setActivityId(0L);
        actProductDO.setStandardPId(0L);
        actProductDO.setIsActive(false);
        actProductDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ActProductids.class))).thenReturn(actProductDO);

        // Run the test
        final Map<Long, List<ActProductDO>> result = queryAllActivityProductServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void testPreLoad_ActivityServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryActivityProduct()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, List<ActProductDO>> result = queryAllActivityProductServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testPreLoad_ActivityServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryActivityProduct()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityProductServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
