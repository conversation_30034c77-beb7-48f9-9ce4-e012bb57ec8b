package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.common.entity.act.CustomContent;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.domain.mapper.ActInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.ActCtripactinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllActivity_VendorServiceTest {

    @Mock
    private ActivityService mockService;
    @Mock
    private ActInfoMapper mockMapper;

    @InjectMocks
    private QueryAllActivity_VendorService queryAllActivityVendorServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure ActivityService.queryAllActivity(...).
        final ActCtripactinfo actCtripactinfo = new ActCtripactinfo();
        actCtripactinfo.setId(0L);
        actCtripactinfo.setTempId(0L);
        actCtripactinfo.setVendorType(0);
        actCtripactinfo.setCustomContent("customContent");
        final CustomContent content = new CustomContent();
        actCtripactinfo.setContent(content);
        final List<ActCtripactinfo> actCtripactinfos = Arrays.asList(actCtripactinfo);
        when(mockService.queryAllActivity()).thenReturn(actCtripactinfos);

        // Configure ActInfoMapper.to(...).
        final ActInfoDO actInfoDO = new ActInfoDO();
        actInfoDO.setId(0L);
        actInfoDO.setTempId(0L);
        actInfoDO.setVendorType(0);
        actInfoDO.setVendorId(0L);
        actInfoDO.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ActCtripactinfo.class))).thenReturn(actInfoDO);

        // Run the test
        final List<ActInfoDO> result = queryAllActivityVendorServiceUnderTest.load("area", 0L);

        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }

    @Test
    public void testLoad_ActivityServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllActivity()).thenReturn(Collections.emptyList());

        // Run the test
        final List<ActInfoDO> result = queryAllActivityVendorServiceUnderTest.load("area", 0L);

        // Verify the results
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_ActivityServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllActivity()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityVendorServiceUnderTest.load("area", 0L)).isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {

        final ActCtripactinfo actCtripactinfo = new ActCtripactinfo();
        actCtripactinfo.setId(0L);
        actCtripactinfo.setTempId(0L);
        actCtripactinfo.setVendorType(0);
        actCtripactinfo.setCustomContent("customContent");
        final CustomContent content = new CustomContent();
        actCtripactinfo.setContent(content);
        final List<ActCtripactinfo> actCtripactinfos = Arrays.asList(actCtripactinfo);
        when(mockService.queryAllActivity()).thenReturn(actCtripactinfos);


        final ActInfoDO actInfoDO = new ActInfoDO();
        actInfoDO.setId(0L);
        actInfoDO.setTempId(0L);
        actInfoDO.setVendorType(0);
        actInfoDO.setVendorId(0L);
        actInfoDO.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ActCtripactinfo.class))).thenReturn(actInfoDO);

        // Run the test
        final Map<Long, List<ActInfoDO>> result = queryAllActivityVendorServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));

        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void testLoadAll_ActivityServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllActivity()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, List<ActInfoDO>> result = queryAllActivityVendorServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));

        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testLoadAll_ActivityServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllActivity()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityVendorServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure ActivityService.queryAllActivity(...).
        final ActCtripactinfo actCtripactinfo = new ActCtripactinfo();
        actCtripactinfo.setId(0L);
        actCtripactinfo.setTempId(0L);
        actCtripactinfo.setVendorType(0);
        actCtripactinfo.setCustomContent("customContent");
        final CustomContent content = new CustomContent();
        actCtripactinfo.setContent(content);
        final List<ActCtripactinfo> actCtripactinfos = Arrays.asList(actCtripactinfo);
        when(mockService.queryAllActivity()).thenReturn(actCtripactinfos);

        // Configure ActInfoMapper.to(...).
        final ActInfoDO actInfoDO = new ActInfoDO();
        actInfoDO.setId(0L);
        actInfoDO.setTempId(0L);
        actInfoDO.setVendorType(0);
        actInfoDO.setVendorId(0L);
        actInfoDO.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(ActCtripactinfo.class))).thenReturn(actInfoDO);

        // Run the test
        final Map<Long, List<ActInfoDO>> result = queryAllActivityVendorServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void testPreLoad_ActivityServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllActivity()).thenReturn(Collections.emptyList());

        // Run the test
        final Map<Long, List<ActInfoDO>> result = queryAllActivityVendorServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testPreLoad_ActivityServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllActivity()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllActivityVendorServiceUnderTest.preLoad("s",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
