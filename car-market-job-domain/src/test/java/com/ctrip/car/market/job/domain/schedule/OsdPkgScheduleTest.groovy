package com.ctrip.car.market.job.domain.schedule

import com.ctrip.car.market.job.domain.config.AllCityQueryConfig
import com.ctrip.car.market.job.domain.config.CommonCityQueryConfig
import com.ctrip.car.market.job.domain.service.ProductManagerService
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class OsdPkgScheduleTest extends Specification {
    @Mock
    AllCityQueryConfig allCityQueryConfig
    @Mock
    ProductManagerService productManagerService
    @InjectMocks
    OsdPkgSchedule osdPkgSchedule

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test Osd Other City Query"() {
        given:
        when(allCityQueryConfig.getOsdOtherCityQueryConfig()).thenReturn([new CommonCityQueryConfig()])
        when(productManagerService.start(anyListOf(CommonCityQueryConfig.class))).thenReturn(true)

        when:
        osdPkgSchedule.OsdOtherCityQuery(null)

        then:
        true//todo - validate something
    }

    def "test Osd Core City Query"() {
        given:
        when(allCityQueryConfig.getOsdCoreCityQueryConfig()).thenReturn([new CommonCityQueryConfig()])
        when(productManagerService.start(anyListOf(CommonCityQueryConfig.class))).thenReturn(true)

        when:
        osdPkgSchedule.OsdCoreCityQuery(null)

        then:
        true//todo - validate something
    }

    def "test Osd No Order City Query"() {
        given:
        when(allCityQueryConfig.getOsdNoOrderCityQueryConfig()).thenReturn([new CommonCityQueryConfig()])
        when(productManagerService.start(anyListOf(CommonCityQueryConfig.class))).thenReturn(true)

        when:
        osdPkgSchedule.OsdNoOrderCityQuery(null)

        then:
        true//todo - validate something
    }

    def "test Osd Hot Market City Query Config"() {
        given:
        when(allCityQueryConfig.getOsdHotMarketCityQueryConfig()).thenReturn([new CommonCityQueryConfig()])
        when(productManagerService.start(anyListOf(CommonCityQueryConfig.class))).thenReturn(true)

        when:
        osdPkgSchedule.OsdHotMarketCityQueryConfig(null)

        then:
        true//todo - validate something
    }

    def "test Osd Qunar Core City Query Config"() {
        given:
        when(allCityQueryConfig.getOsdQunarCoreCityQueryConfig()).thenReturn([new CommonCityQueryConfig()])
        when(productManagerService.start(anyListOf(CommonCityQueryConfig.class))).thenReturn(true)

        when:
        osdPkgSchedule.OsdQunarCoreCityQueryConfig(null)

        then:
        true//todo - validate something
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme