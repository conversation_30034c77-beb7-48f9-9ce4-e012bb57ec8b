package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.CpnPromotionOrderDO;
import com.ctrip.car.market.job.domain.mapper.PromotionOrderMapper;
import com.ctrip.car.market.job.repository.entity.CpnPromotionOrder;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllPromotionOrderServiceTest {

    @Mock
    private RestrictedService mockService;
    @Mock
    private PromotionOrderMapper mockMapper;

    @InjectMocks
    private QueryAllPromotionOrderService queryAllPromotionOrderServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllPromotionOrder(...).
        final CpnPromotionOrder cpnPromotionOrder = new CpnPromotionOrder();
        cpnPromotionOrder.setId(0L);
        cpnPromotionOrder.setPromotionId(0);
        cpnPromotionOrder.setPromotionName("promotionName");
        cpnPromotionOrder.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrder.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrder.setUseStation(0);
        cpnPromotionOrder.setStatus(0);
        cpnPromotionOrder.setCreateUser("createUser");
        cpnPromotionOrder.setUpdateUser("updateUser");
        cpnPromotionOrder.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrder.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrder.setCustomerType(0);
        cpnPromotionOrder.setContent("content");
        final List<CpnPromotionOrder> cpnPromotionOrders = Arrays.asList(cpnPromotionOrder);
        when(mockService.queryAllPromotionOrder()).thenReturn(cpnPromotionOrders);

        // Configure PromotionOrderMapper.to(...).
        final CpnPromotionOrderDO cpnPromotionOrderDO = new CpnPromotionOrderDO();
        cpnPromotionOrderDO.setId(0L);
        cpnPromotionOrderDO.setPromotionId(0);
        cpnPromotionOrderDO.setPromotionName("promotionName");
        cpnPromotionOrderDO.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setUseStation(0);
        cpnPromotionOrderDO.setStatus(0);
        cpnPromotionOrderDO.setCustomerType(0);
        cpnPromotionOrderDO.setContent("content");
        cpnPromotionOrderDO.setCreateUser("createUser");
        cpnPromotionOrderDO.setUpdateUser("updateUser");
        cpnPromotionOrderDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(CpnPromotionOrder.class))).thenReturn(cpnPromotionOrderDO);

        // Run the test
        final CpnPromotionOrderDO result = queryAllPromotionOrderServiceUnderTest.load("area", 0L);
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllPromotionOrder()).thenReturn(Collections.emptyList());

        // Configure PromotionOrderMapper.to(...).
        final CpnPromotionOrderDO cpnPromotionOrderDO = new CpnPromotionOrderDO();
        cpnPromotionOrderDO.setId(0L);
        cpnPromotionOrderDO.setPromotionId(0);
        cpnPromotionOrderDO.setPromotionName("promotionName");
        cpnPromotionOrderDO.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setUseStation(0);
        cpnPromotionOrderDO.setStatus(0);
        cpnPromotionOrderDO.setCustomerType(0);
        cpnPromotionOrderDO.setContent("content");
        cpnPromotionOrderDO.setCreateUser("createUser");
        cpnPromotionOrderDO.setUpdateUser("updateUser");
        cpnPromotionOrderDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(CpnPromotionOrder.class))).thenReturn(cpnPromotionOrderDO);

        // Run the test
        final CpnPromotionOrderDO result = queryAllPromotionOrderServiceUnderTest.load("area", 0L);
        Assert.isNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllPromotionOrder()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllPromotionOrderServiceUnderTest.load("area", 0L)).isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllPromotionOrder(...).
        final CpnPromotionOrder cpnPromotionOrder = new CpnPromotionOrder();
        cpnPromotionOrder.setId(0L);
        cpnPromotionOrder.setPromotionId(0);
        cpnPromotionOrder.setPromotionName("promotionName");
        cpnPromotionOrder.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrder.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrder.setUseStation(0);
        cpnPromotionOrder.setStatus(0);
        cpnPromotionOrder.setCreateUser("createUser");
        cpnPromotionOrder.setUpdateUser("updateUser");
        cpnPromotionOrder.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrder.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrder.setCustomerType(0);
        cpnPromotionOrder.setContent("content");
        final List<CpnPromotionOrder> cpnPromotionOrders = Arrays.asList(cpnPromotionOrder);
        when(mockService.queryAllPromotionOrder()).thenReturn(cpnPromotionOrders);

        // Configure PromotionOrderMapper.to(...).
        final CpnPromotionOrderDO cpnPromotionOrderDO = new CpnPromotionOrderDO();
        cpnPromotionOrderDO.setId(0L);
        cpnPromotionOrderDO.setPromotionId(0);
        cpnPromotionOrderDO.setPromotionName("promotionName");
        cpnPromotionOrderDO.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setUseStation(0);
        cpnPromotionOrderDO.setStatus(0);
        cpnPromotionOrderDO.setCustomerType(0);
        cpnPromotionOrderDO.setContent("content");
        cpnPromotionOrderDO.setCreateUser("createUser");
        cpnPromotionOrderDO.setUpdateUser("updateUser");
        cpnPromotionOrderDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(CpnPromotionOrder.class))).thenReturn(cpnPromotionOrderDO);

        // Run the test
        final Map<Long, CpnPromotionOrderDO> result = queryAllPromotionOrderServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllPromotionOrder()).thenReturn(Collections.emptyList());

        // Configure PromotionOrderMapper.to(...).
        final CpnPromotionOrderDO cpnPromotionOrderDO = new CpnPromotionOrderDO();
        cpnPromotionOrderDO.setId(0L);
        cpnPromotionOrderDO.setPromotionId(0);
        cpnPromotionOrderDO.setPromotionName("promotionName");
        cpnPromotionOrderDO.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setUseStation(0);
        cpnPromotionOrderDO.setStatus(0);
        cpnPromotionOrderDO.setCustomerType(0);
        cpnPromotionOrderDO.setContent("content");
        cpnPromotionOrderDO.setCreateUser("createUser");
        cpnPromotionOrderDO.setUpdateUser("updateUser");
        cpnPromotionOrderDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(CpnPromotionOrder.class))).thenReturn(cpnPromotionOrderDO);

        // Run the test
        final Map<Long, CpnPromotionOrderDO> result = queryAllPromotionOrderServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllPromotionOrder()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllPromotionOrderServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure RestrictedService.queryAllPromotionOrder(...).
        final CpnPromotionOrder cpnPromotionOrder = new CpnPromotionOrder();
        cpnPromotionOrder.setId(0L);
        cpnPromotionOrder.setPromotionId(0);
        cpnPromotionOrder.setPromotionName("promotionName");
        cpnPromotionOrder.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrder.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrder.setUseStation(0);
        cpnPromotionOrder.setStatus(0);
        cpnPromotionOrder.setCreateUser("createUser");
        cpnPromotionOrder.setUpdateUser("updateUser");
        cpnPromotionOrder.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrder.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrder.setCustomerType(0);
        cpnPromotionOrder.setContent("content");
        final List<CpnPromotionOrder> cpnPromotionOrders = Arrays.asList(cpnPromotionOrder);
        when(mockService.queryAllPromotionOrder()).thenReturn(cpnPromotionOrders);

        // Configure PromotionOrderMapper.to(...).
        final CpnPromotionOrderDO cpnPromotionOrderDO = new CpnPromotionOrderDO();
        cpnPromotionOrderDO.setId(0L);
        cpnPromotionOrderDO.setPromotionId(0);
        cpnPromotionOrderDO.setPromotionName("promotionName");
        cpnPromotionOrderDO.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setUseStation(0);
        cpnPromotionOrderDO.setStatus(0);
        cpnPromotionOrderDO.setCustomerType(0);
        cpnPromotionOrderDO.setContent("content");
        cpnPromotionOrderDO.setCreateUser("createUser");
        cpnPromotionOrderDO.setUpdateUser("updateUser");
        cpnPromotionOrderDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(CpnPromotionOrder.class))).thenReturn(cpnPromotionOrderDO);

        // Run the test
        final Map<Long, CpnPromotionOrderDO> result = queryAllPromotionOrderServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllPromotionOrder()).thenReturn(Collections.emptyList());

        // Configure PromotionOrderMapper.to(...).
        final CpnPromotionOrderDO cpnPromotionOrderDO = new CpnPromotionOrderDO();
        cpnPromotionOrderDO.setId(0L);
        cpnPromotionOrderDO.setPromotionId(0);
        cpnPromotionOrderDO.setPromotionName("promotionName");
        cpnPromotionOrderDO.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setEndTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setUseStation(0);
        cpnPromotionOrderDO.setStatus(0);
        cpnPromotionOrderDO.setCustomerType(0);
        cpnPromotionOrderDO.setContent("content");
        cpnPromotionOrderDO.setCreateUser("createUser");
        cpnPromotionOrderDO.setUpdateUser("updateUser");
        cpnPromotionOrderDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnPromotionOrderDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        when(mockMapper.to(any(CpnPromotionOrder.class))).thenReturn(cpnPromotionOrderDO);

        // Run the test
        final Map<Long, CpnPromotionOrderDO> result = queryAllPromotionOrderServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllPromotionOrder()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllPromotionOrderServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
