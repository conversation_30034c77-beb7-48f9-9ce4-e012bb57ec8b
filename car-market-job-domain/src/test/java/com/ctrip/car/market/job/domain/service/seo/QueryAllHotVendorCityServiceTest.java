package com.ctrip.car.market.job.domain.service.seo;

import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorCityDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotVendorCityMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotVendorCity;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllHotVendorCityServiceTest {

    @Mock
    private MarketDBService mockService;
    @Mock
    private SeoHotVendorCityMapper mockMapper;

    @InjectMocks
    private QueryAllHotVendorCityService queryAllSeoHotCityCountryServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure MarketDBService.queryAllHotCity(...).
        final SeoHotVendorCity seoHotCityinfo = new SeoHotVendorCity();
        seoHotCityinfo.setId(0L);
        seoHotCityinfo.setCityId(0);
        seoHotCityinfo.setCityName("cityName");
        seoHotCityinfo.setVendorId("sd");
        seoHotCityinfo.setUrl("url");
        final List<SeoHotVendorCity> seoHotCityinfos = Arrays.asList(seoHotCityinfo);
        when(mockService.queryAllHotVendorCity()).thenReturn(seoHotCityinfos);

        // Configure SeoHotCityinfoMapper.to(...).
        final SeoHotVendorCityDO seoHotCityinfoDO = new SeoHotVendorCityDO();
        seoHotCityinfoDO.setId(0L);
        seoHotCityinfoDO.setCityId(0);
        seoHotCityinfoDO.setCityName("cityName");
        seoHotCityinfoDO.setVendorId("sd");
        seoHotCityinfoDO.setUrl("url");
        when(mockMapper.to(any(SeoHotVendorCity.class))).thenReturn(seoHotCityinfoDO);

        // Run the test
        Assert.assertTrue(CollectionUtils.isNotEmpty(queryAllSeoHotCityCountryServiceUnderTest.load("area", "sd")));
    }

    @Test
    public void testLoad_MarketDBServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllHotVendorCity()).thenReturn(Collections.emptyList());

        // Run the test
        final List<SeoHotVendorCityDO> result = queryAllSeoHotCityCountryServiceUnderTest.load("area", "sd");

        // Verify the results
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testLoad_MarketDBServiceThrowsSQLException() throws Exception {
        // Setup
        when(mockService.queryAllHotVendorCity()).thenThrow(SQLException.class);

        // Run the test
        assertThatThrownBy(() -> queryAllSeoHotCityCountryServiceUnderTest.load("area", "sd"))
                .isInstanceOf(SQLException.class);
    }

    @Test
    public void test_preLoad() throws Exception {
        when(mockService.queryAllHotVendorCity()).thenReturn(Collections.emptyList());

        final Map<String, List<SeoHotVendorCityDO>> result = queryAllSeoHotCityCountryServiceUnderTest.preLoad("area", null);

        // Verify the results
        Assert.assertTrue(result.isEmpty());
    }
}
