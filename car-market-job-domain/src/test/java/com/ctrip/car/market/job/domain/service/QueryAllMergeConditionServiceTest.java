package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.CpnMergeConditionDO;
import com.ctrip.car.market.job.domain.mapper.MergeConditionMapper;
import com.ctrip.car.market.job.repository.entity.CpnMergeCondition;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllMergeConditionServiceTest {

    @Mock
    private RestrictedService mockService;
    @Mock
    private MergeConditionMapper mockMapper;

    @InjectMocks
    private QueryAllMergeConditionService queryAllMergeConditionServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllMergeCondition(...).
        final CpnMergeCondition cpnMergeCondition = new CpnMergeCondition();
        cpnMergeCondition.setID(0L);
        cpnMergeCondition.setDisplayName("displayName");
        cpnMergeCondition.setChannelIds("channelIds");
        cpnMergeCondition.setRemark("remark");
        cpnMergeCondition.setStartDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeCondition.setEndDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeCondition.setActive(0);
        cpnMergeCondition.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeCondition.setOperator("operator");
        cpnMergeCondition.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeCondition.setConent("conent");
        cpnMergeCondition.setCategorycodes("categorycodes");
        cpnMergeCondition.setChannelGroups("channelGroups");
        cpnMergeCondition.setExcludeChannelIds("excludeChannelIds");
        final List<CpnMergeCondition> cpnMergeConditions = Arrays.asList(cpnMergeCondition);
        when(mockService.queryAllMergeCondition()).thenReturn(cpnMergeConditions);

        // Configure MergeConditionMapper.to(...).
        final CpnMergeConditionDO cpnMergeConditionDO = new CpnMergeConditionDO();
        cpnMergeConditionDO.setID(0L);
        cpnMergeConditionDO.setDisplayName("displayName");
        cpnMergeConditionDO.setChannelIds("channelIds");
        cpnMergeConditionDO.setRemark("remark");
        cpnMergeConditionDO.setStartDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setEndDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setActive(0);
        cpnMergeConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setOperator("operator");
        cpnMergeConditionDO.setCategorycodes("categorycodes");
        cpnMergeConditionDO.setChannelGroups("channelGroups");
        cpnMergeConditionDO.setExcludeChannelIds("excludeChannelIds");
        cpnMergeConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setConent("conent");
        when(mockMapper.to(any(CpnMergeCondition.class))).thenReturn(cpnMergeConditionDO);

        // Run the test
        final List<CpnMergeConditionDO> result = queryAllMergeConditionServiceUnderTest.load("area", "key");
        Assert.isNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllMergeCondition()).thenReturn(Collections.emptyList());

        // Configure MergeConditionMapper.to(...).
        final CpnMergeConditionDO cpnMergeConditionDO = new CpnMergeConditionDO();
        cpnMergeConditionDO.setID(0L);
        cpnMergeConditionDO.setDisplayName("displayName");
        cpnMergeConditionDO.setChannelIds("channelIds");
        cpnMergeConditionDO.setRemark("remark");
        cpnMergeConditionDO.setStartDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setEndDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setActive(0);
        cpnMergeConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setOperator("operator");
        cpnMergeConditionDO.setCategorycodes("categorycodes");
        cpnMergeConditionDO.setChannelGroups("channelGroups");
        cpnMergeConditionDO.setExcludeChannelIds("excludeChannelIds");
        cpnMergeConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setConent("conent");
        when(mockMapper.to(any(CpnMergeCondition.class))).thenReturn(cpnMergeConditionDO);

        // Run the test
        final List<CpnMergeConditionDO> result = queryAllMergeConditionServiceUnderTest.load("area", "key");
        Assert.isNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllMergeCondition()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllMergeConditionServiceUnderTest.load("area", "key"))
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllMergeCondition(...).
        final CpnMergeCondition cpnMergeCondition = new CpnMergeCondition();
        cpnMergeCondition.setID(0L);
        cpnMergeCondition.setDisplayName("displayName");
        cpnMergeCondition.setChannelIds("channelIds");
        cpnMergeCondition.setRemark("remark");
        cpnMergeCondition.setStartDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeCondition.setEndDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeCondition.setActive(0);
        cpnMergeCondition.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeCondition.setOperator("operator");
        cpnMergeCondition.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeCondition.setConent("conent");
        cpnMergeCondition.setCategorycodes("categorycodes");
        cpnMergeCondition.setChannelGroups("channelGroups");
        cpnMergeCondition.setExcludeChannelIds("excludeChannelIds");
        final List<CpnMergeCondition> cpnMergeConditions = Arrays.asList(cpnMergeCondition);
        when(mockService.queryAllMergeCondition()).thenReturn(cpnMergeConditions);

        // Configure MergeConditionMapper.to(...).
        final CpnMergeConditionDO cpnMergeConditionDO = new CpnMergeConditionDO();
        cpnMergeConditionDO.setID(0L);
        cpnMergeConditionDO.setDisplayName("displayName");
        cpnMergeConditionDO.setChannelIds("channelIds");
        cpnMergeConditionDO.setRemark("remark");
        cpnMergeConditionDO.setStartDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setEndDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setActive(0);
        cpnMergeConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setOperator("operator");
        cpnMergeConditionDO.setCategorycodes("categorycodes");
        cpnMergeConditionDO.setChannelGroups("channelGroups");
        cpnMergeConditionDO.setExcludeChannelIds("excludeChannelIds");
        cpnMergeConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setConent("conent");
        when(mockMapper.to(any(CpnMergeCondition.class))).thenReturn(cpnMergeConditionDO);

        // Run the test
        final Map<String, List<CpnMergeConditionDO>> result = queryAllMergeConditionServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllMergeCondition()).thenReturn(Collections.emptyList());

        // Configure MergeConditionMapper.to(...).
        final CpnMergeConditionDO cpnMergeConditionDO = new CpnMergeConditionDO();
        cpnMergeConditionDO.setID(0L);
        cpnMergeConditionDO.setDisplayName("displayName");
        cpnMergeConditionDO.setChannelIds("channelIds");
        cpnMergeConditionDO.setRemark("remark");
        cpnMergeConditionDO.setStartDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setEndDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setActive(0);
        cpnMergeConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setOperator("operator");
        cpnMergeConditionDO.setCategorycodes("categorycodes");
        cpnMergeConditionDO.setChannelGroups("channelGroups");
        cpnMergeConditionDO.setExcludeChannelIds("excludeChannelIds");
        cpnMergeConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setConent("conent");
        when(mockMapper.to(any(CpnMergeCondition.class))).thenReturn(cpnMergeConditionDO);

        // Run the test
        final Map<String, List<CpnMergeConditionDO>> result = queryAllMergeConditionServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllMergeCondition()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllMergeConditionServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList("value")))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure RestrictedService.queryAllMergeCondition(...).
        final CpnMergeCondition cpnMergeCondition = new CpnMergeCondition();
        cpnMergeCondition.setID(0L);
        cpnMergeCondition.setDisplayName("displayName");
        cpnMergeCondition.setChannelIds("channelIds");
        cpnMergeCondition.setRemark("remark");
        cpnMergeCondition.setStartDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeCondition.setEndDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeCondition.setActive(0);
        cpnMergeCondition.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeCondition.setOperator("operator");
        cpnMergeCondition.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeCondition.setConent("conent");
        cpnMergeCondition.setCategorycodes("categorycodes");
        cpnMergeCondition.setChannelGroups("channelGroups");
        cpnMergeCondition.setExcludeChannelIds("excludeChannelIds");
        final List<CpnMergeCondition> cpnMergeConditions = Arrays.asList(cpnMergeCondition);
        when(mockService.queryAllMergeCondition()).thenReturn(cpnMergeConditions);

        // Configure MergeConditionMapper.to(...).
        final CpnMergeConditionDO cpnMergeConditionDO = new CpnMergeConditionDO();
        cpnMergeConditionDO.setID(0L);
        cpnMergeConditionDO.setDisplayName("displayName");
        cpnMergeConditionDO.setChannelIds("channelIds");
        cpnMergeConditionDO.setRemark("remark");
        cpnMergeConditionDO.setStartDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setEndDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setActive(0);
        cpnMergeConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setOperator("operator");
        cpnMergeConditionDO.setCategorycodes("categorycodes");
        cpnMergeConditionDO.setChannelGroups("channelGroups");
        cpnMergeConditionDO.setExcludeChannelIds("excludeChannelIds");
        cpnMergeConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setConent("conent");
        when(mockMapper.to(any(CpnMergeCondition.class))).thenReturn(cpnMergeConditionDO);

        // Run the test
        final Map<String, List<CpnMergeConditionDO>> result = queryAllMergeConditionServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllMergeCondition()).thenReturn(Collections.emptyList());

        // Configure MergeConditionMapper.to(...).
        final CpnMergeConditionDO cpnMergeConditionDO = new CpnMergeConditionDO();
        cpnMergeConditionDO.setID(0L);
        cpnMergeConditionDO.setDisplayName("displayName");
        cpnMergeConditionDO.setChannelIds("channelIds");
        cpnMergeConditionDO.setRemark("remark");
        cpnMergeConditionDO.setStartDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setEndDate(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setActive(0);
        cpnMergeConditionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setOperator("operator");
        cpnMergeConditionDO.setCategorycodes("categorycodes");
        cpnMergeConditionDO.setChannelGroups("channelGroups");
        cpnMergeConditionDO.setExcludeChannelIds("excludeChannelIds");
        cpnMergeConditionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnMergeConditionDO.setConent("conent");
        when(mockMapper.to(any(CpnMergeCondition.class))).thenReturn(cpnMergeConditionDO);

        // Run the test
        final Map<String, List<CpnMergeConditionDO>> result = queryAllMergeConditionServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllMergeCondition()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllMergeConditionServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
