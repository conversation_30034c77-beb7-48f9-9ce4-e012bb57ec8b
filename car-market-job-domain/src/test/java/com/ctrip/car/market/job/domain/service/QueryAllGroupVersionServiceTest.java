package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.CpnGroupVersionDO;
import com.ctrip.car.market.job.domain.mapper.GroupVersionMapper;
import com.ctrip.car.market.job.repository.entity.CpnGroupVersion;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllGroupVersionServiceTest {

    @Mock
    private RestrictedService mockService;
    @Mock
    private GroupVersionMapper mockMapper;

    @InjectMocks
    private QueryAllGroupVersionService queryAllGroupVersionServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllGroupVersion(...).
        final CpnGroupVersion cpnGroupVersion = new CpnGroupVersion();
        cpnGroupVersion.setGvid(0);
        cpnGroupVersion.setGroupId(0);
        cpnGroupVersion.setGroupConditionId(0L);
        cpnGroupVersion.setModifyUser("modifyUser");
        cpnGroupVersion.setIsValid(false);
        cpnGroupVersion.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersion.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersion.setUrlTempId(0);
        cpnGroupVersion.setVersion(0);
        cpnGroupVersion.setFeConfigId(0);
        cpnGroupVersion.setUnionType(0);
        final List<CpnGroupVersion> cpnGroupVersions = Arrays.asList(cpnGroupVersion);
        when(mockService.queryAllGroupVersion()).thenReturn(cpnGroupVersions);

        // Configure GroupVersionMapper.to(...).
        final CpnGroupVersionDO cpnGroupVersionDO = new CpnGroupVersionDO();
        cpnGroupVersionDO.setGvid(0);
        cpnGroupVersionDO.setGroupId(0);
        cpnGroupVersionDO.setGroupConditionId(0L);
        cpnGroupVersionDO.setModifyUser("modifyUser");
        cpnGroupVersionDO.setIsValid(false);
        cpnGroupVersionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersionDO.setUrlTempId(0);
        cpnGroupVersionDO.setVersion(0);
        cpnGroupVersionDO.setFeConfigId(0);
        cpnGroupVersionDO.setUnionType(0);
        when(mockMapper.to(any(CpnGroupVersion.class))).thenReturn(cpnGroupVersionDO);

        // Run the test
        final List<CpnGroupVersionDO> result = queryAllGroupVersionServiceUnderTest.load("area", 0);
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllGroupVersion()).thenReturn(Collections.emptyList());

        // Configure GroupVersionMapper.to(...).
        final CpnGroupVersionDO cpnGroupVersionDO = new CpnGroupVersionDO();
        cpnGroupVersionDO.setGvid(0);
        cpnGroupVersionDO.setGroupId(0);
        cpnGroupVersionDO.setGroupConditionId(0L);
        cpnGroupVersionDO.setModifyUser("modifyUser");
        cpnGroupVersionDO.setIsValid(false);
        cpnGroupVersionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersionDO.setUrlTempId(0);
        cpnGroupVersionDO.setVersion(0);
        cpnGroupVersionDO.setFeConfigId(0);
        cpnGroupVersionDO.setUnionType(0);
        when(mockMapper.to(any(CpnGroupVersion.class))).thenReturn(cpnGroupVersionDO);

        // Run the test
        final List<CpnGroupVersionDO> result = queryAllGroupVersionServiceUnderTest.load("area", 0);

        // Verify the results
        Assert.isNull(result);
    }

    @Test
    public void testLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllGroupVersion()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllGroupVersionServiceUnderTest.load("area", 0)).isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure RestrictedService.queryAllGroupVersion(...).
        final CpnGroupVersion cpnGroupVersion = new CpnGroupVersion();
        cpnGroupVersion.setGvid(0);
        cpnGroupVersion.setGroupId(0);
        cpnGroupVersion.setGroupConditionId(0L);
        cpnGroupVersion.setModifyUser("modifyUser");
        cpnGroupVersion.setIsValid(false);
        cpnGroupVersion.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersion.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersion.setUrlTempId(0);
        cpnGroupVersion.setVersion(0);
        cpnGroupVersion.setFeConfigId(0);
        cpnGroupVersion.setUnionType(0);
        final List<CpnGroupVersion> cpnGroupVersions = Arrays.asList(cpnGroupVersion);
        when(mockService.queryAllGroupVersion()).thenReturn(cpnGroupVersions);

        // Configure GroupVersionMapper.to(...).
        final CpnGroupVersionDO cpnGroupVersionDO = new CpnGroupVersionDO();
        cpnGroupVersionDO.setGvid(0);
        cpnGroupVersionDO.setGroupId(0);
        cpnGroupVersionDO.setGroupConditionId(0L);
        cpnGroupVersionDO.setModifyUser("modifyUser");
        cpnGroupVersionDO.setIsValid(false);
        cpnGroupVersionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersionDO.setUrlTempId(0);
        cpnGroupVersionDO.setVersion(0);
        cpnGroupVersionDO.setFeConfigId(0);
        cpnGroupVersionDO.setUnionType(0);
        when(mockMapper.to(any(CpnGroupVersion.class))).thenReturn(cpnGroupVersionDO);

        // Run the test
        final Map<Integer, List<CpnGroupVersionDO>> result = queryAllGroupVersionServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllGroupVersion()).thenReturn(Collections.emptyList());

        // Configure GroupVersionMapper.to(...).
        final CpnGroupVersionDO cpnGroupVersionDO = new CpnGroupVersionDO();
        cpnGroupVersionDO.setGvid(0);
        cpnGroupVersionDO.setGroupId(0);
        cpnGroupVersionDO.setGroupConditionId(0L);
        cpnGroupVersionDO.setModifyUser("modifyUser");
        cpnGroupVersionDO.setIsValid(false);
        cpnGroupVersionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersionDO.setUrlTempId(0);
        cpnGroupVersionDO.setVersion(0);
        cpnGroupVersionDO.setFeConfigId(0);
        cpnGroupVersionDO.setUnionType(0);
        when(mockMapper.to(any(CpnGroupVersion.class))).thenReturn(cpnGroupVersionDO);

        // Run the test
        final Map<Integer, List<CpnGroupVersionDO>> result = queryAllGroupVersionServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllGroupVersion()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllGroupVersionServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0)))).isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure RestrictedService.queryAllGroupVersion(...).
        final CpnGroupVersion cpnGroupVersion = new CpnGroupVersion();
        cpnGroupVersion.setGvid(0);
        cpnGroupVersion.setGroupId(0);
        cpnGroupVersion.setGroupConditionId(0L);
        cpnGroupVersion.setModifyUser("modifyUser");
        cpnGroupVersion.setIsValid(false);
        cpnGroupVersion.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersion.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersion.setUrlTempId(0);
        cpnGroupVersion.setVersion(0);
        cpnGroupVersion.setFeConfigId(0);
        cpnGroupVersion.setUnionType(0);
        final List<CpnGroupVersion> cpnGroupVersions = Arrays.asList(cpnGroupVersion);
        when(mockService.queryAllGroupVersion()).thenReturn(cpnGroupVersions);

        // Configure GroupVersionMapper.to(...).
        final CpnGroupVersionDO cpnGroupVersionDO = new CpnGroupVersionDO();
        cpnGroupVersionDO.setGvid(0);
        cpnGroupVersionDO.setGroupId(0);
        cpnGroupVersionDO.setGroupConditionId(0L);
        cpnGroupVersionDO.setModifyUser("modifyUser");
        cpnGroupVersionDO.setIsValid(false);
        cpnGroupVersionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersionDO.setUrlTempId(0);
        cpnGroupVersionDO.setVersion(0);
        cpnGroupVersionDO.setFeConfigId(0);
        cpnGroupVersionDO.setUnionType(0);
        when(mockMapper.to(any(CpnGroupVersion.class))).thenReturn(cpnGroupVersionDO);

        // Run the test
        final Map<Integer, List<CpnGroupVersionDO>> result = queryAllGroupVersionServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllGroupVersion()).thenReturn(Collections.emptyList());

        // Configure GroupVersionMapper.to(...).
        final CpnGroupVersionDO cpnGroupVersionDO = new CpnGroupVersionDO();
        cpnGroupVersionDO.setGvid(0);
        cpnGroupVersionDO.setGroupId(0);
        cpnGroupVersionDO.setGroupConditionId(0L);
        cpnGroupVersionDO.setModifyUser("modifyUser");
        cpnGroupVersionDO.setIsValid(false);
        cpnGroupVersionDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersionDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnGroupVersionDO.setUrlTempId(0);
        cpnGroupVersionDO.setVersion(0);
        cpnGroupVersionDO.setFeConfigId(0);
        cpnGroupVersionDO.setUnionType(0);
        when(mockMapper.to(any(CpnGroupVersion.class))).thenReturn(cpnGroupVersionDO);

        // Run the test
        final Map<Integer, List<CpnGroupVersionDO>> result = queryAllGroupVersionServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_RestrictedServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllGroupVersion()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllGroupVersionServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
