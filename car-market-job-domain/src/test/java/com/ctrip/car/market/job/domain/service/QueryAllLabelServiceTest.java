package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.common.entity.CpnLabelDO;
import com.ctrip.car.market.job.domain.mapper.LabelMapper;
import com.ctrip.car.market.job.repository.entity.CpnLabel;
import com.ctrip.car.market.job.repository.service.LabelService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QueryAllLabelServiceTest {

    @Mock
    private LabelService mockService;
    @Mock
    private LabelMapper mockMapper;

    @InjectMocks
    private QueryAllLabelService queryAllLabelServiceUnderTest;

    @Test
    public void testLoad() throws Throwable {
        // Setup
        // Configure LabelService.queryAllLabel(...).
        final CpnLabel cpnLabel = new CpnLabel();
        cpnLabel.setCode(0L);
        cpnLabel.setCType(0);
        cpnLabel.setName("name");
        cpnLabel.setDescription("description");
        cpnLabel.setSort(0);
        cpnLabel.setPos(0);
        cpnLabel.setGroupCode("groupCode");
        cpnLabel.setGroupName("groupName");
        cpnLabel.setSource(0);
        cpnLabel.setIsDisplay(false);
        cpnLabel.setIsActive(false);
        cpnLabel.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabel.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabel.setIsListDisplay(false);
        cpnLabel.setStatus(0);
        final List<CpnLabel> cpnLabels = Arrays.asList(cpnLabel);
        when(mockService.queryAllLabel()).thenReturn(cpnLabels);

        // Configure LabelMapper.to(...).
        final CpnLabelDO cpnLabelDO = new CpnLabelDO();
        cpnLabelDO.setCode(0L);
        cpnLabelDO.setCType(0);
        cpnLabelDO.setName("name");
        cpnLabelDO.setDescription("description");
        cpnLabelDO.setPos(0);
        cpnLabelDO.setGroupCode("groupCode");
        cpnLabelDO.setGroupName("groupName");
        cpnLabelDO.setSource(0);
        cpnLabelDO.setIsDisplay(false);
        cpnLabelDO.setIsActive(false);
        cpnLabelDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelDO.setIsListDisplay(false);
        cpnLabelDO.setStatus(0);
        cpnLabelDO.setProductCategoryId(0);
        when(mockMapper.to(any(CpnLabel.class))).thenReturn(cpnLabelDO);

        // Run the test
        final CpnLabelDO result = queryAllLabelServiceUnderTest.load("area", 0L);
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_LabelServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllLabel()).thenReturn(Collections.emptyList());

        // Configure LabelMapper.to(...).
        final CpnLabelDO cpnLabelDO = new CpnLabelDO();
        cpnLabelDO.setCode(0L);
        cpnLabelDO.setCType(0);
        cpnLabelDO.setName("name");
        cpnLabelDO.setDescription("description");
        cpnLabelDO.setPos(0);
        cpnLabelDO.setGroupCode("groupCode");
        cpnLabelDO.setGroupName("groupName");
        cpnLabelDO.setSource(0);
        cpnLabelDO.setIsDisplay(false);
        cpnLabelDO.setIsActive(false);
        cpnLabelDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelDO.setIsListDisplay(false);
        cpnLabelDO.setStatus(0);
        cpnLabelDO.setProductCategoryId(0);
        when(mockMapper.to(any(CpnLabel.class))).thenReturn(cpnLabelDO);

        // Run the test
        final CpnLabelDO result = queryAllLabelServiceUnderTest.load("area", 0L);
        Assert.isNull(result);
        // Verify the results
    }

    @Test
    public void testLoad_LabelServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllLabel()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllLabelServiceUnderTest.load("area", 0L)).isInstanceOf(Exception.class);
    }

    @Test
    public void testLoadAll() throws Throwable {
        // Setup
        // Configure LabelService.queryAllLabel(...).
        final CpnLabel cpnLabel = new CpnLabel();
        cpnLabel.setCode(0L);
        cpnLabel.setCType(0);
        cpnLabel.setName("name");
        cpnLabel.setDescription("description");
        cpnLabel.setSort(0);
        cpnLabel.setPos(0);
        cpnLabel.setGroupCode("groupCode");
        cpnLabel.setGroupName("groupName");
        cpnLabel.setSource(0);
        cpnLabel.setIsDisplay(false);
        cpnLabel.setIsActive(false);
        cpnLabel.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabel.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabel.setIsListDisplay(false);
        cpnLabel.setStatus(0);
        final List<CpnLabel> cpnLabels = Arrays.asList(cpnLabel);
        when(mockService.queryAllLabel()).thenReturn(cpnLabels);

        // Configure LabelMapper.to(...).
        final CpnLabelDO cpnLabelDO = new CpnLabelDO();
        cpnLabelDO.setCode(0L);
        cpnLabelDO.setCType(0);
        cpnLabelDO.setName("name");
        cpnLabelDO.setDescription("description");
        cpnLabelDO.setPos(0);
        cpnLabelDO.setGroupCode("groupCode");
        cpnLabelDO.setGroupName("groupName");
        cpnLabelDO.setSource(0);
        cpnLabelDO.setIsDisplay(false);
        cpnLabelDO.setIsActive(false);
        cpnLabelDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelDO.setIsListDisplay(false);
        cpnLabelDO.setStatus(0);
        cpnLabelDO.setProductCategoryId(0);
        when(mockMapper.to(any(CpnLabel.class))).thenReturn(cpnLabelDO);

        // Run the test
        final Map<Long, CpnLabelDO> result = queryAllLabelServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_LabelServiceReturnsNoItems() throws Throwable {
        // Setup
        when(mockService.queryAllLabel()).thenReturn(Collections.emptyList());

        // Configure LabelMapper.to(...).
        final CpnLabelDO cpnLabelDO = new CpnLabelDO();
        cpnLabelDO.setCode(0L);
        cpnLabelDO.setCType(0);
        cpnLabelDO.setName("name");
        cpnLabelDO.setDescription("description");
        cpnLabelDO.setPos(0);
        cpnLabelDO.setGroupCode("groupCode");
        cpnLabelDO.setGroupName("groupName");
        cpnLabelDO.setSource(0);
        cpnLabelDO.setIsDisplay(false);
        cpnLabelDO.setIsActive(false);
        cpnLabelDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelDO.setIsListDisplay(false);
        cpnLabelDO.setStatus(0);
        cpnLabelDO.setProductCategoryId(0);
        when(mockMapper.to(any(CpnLabel.class))).thenReturn(cpnLabelDO);

        // Run the test
        final Map<Long, CpnLabelDO> result = queryAllLabelServiceUnderTest.loadAll("area",
                new HashSet<>(Arrays.asList(0L)));
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testLoadAll_LabelServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllLabel()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(
                () -> queryAllLabelServiceUnderTest.loadAll("area", new HashSet<>(Arrays.asList(0L))))
                .isInstanceOf(Exception.class);
    }

    @Test
    public void testPreLoad() throws Exception {
        // Setup
        // Configure LabelService.queryAllLabel(...).
        final CpnLabel cpnLabel = new CpnLabel();
        cpnLabel.setCode(0L);
        cpnLabel.setCType(0);
        cpnLabel.setName("name");
        cpnLabel.setDescription("description");
        cpnLabel.setSort(0);
        cpnLabel.setPos(0);
        cpnLabel.setGroupCode("groupCode");
        cpnLabel.setGroupName("groupName");
        cpnLabel.setSource(0);
        cpnLabel.setIsDisplay(false);
        cpnLabel.setIsActive(false);
        cpnLabel.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabel.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabel.setIsListDisplay(false);
        cpnLabel.setStatus(0);
        final List<CpnLabel> cpnLabels = Arrays.asList(cpnLabel);
        when(mockService.queryAllLabel()).thenReturn(cpnLabels);

        // Configure LabelMapper.to(...).
        final CpnLabelDO cpnLabelDO = new CpnLabelDO();
        cpnLabelDO.setCode(0L);
        cpnLabelDO.setCType(0);
        cpnLabelDO.setName("name");
        cpnLabelDO.setDescription("description");
        cpnLabelDO.setPos(0);
        cpnLabelDO.setGroupCode("groupCode");
        cpnLabelDO.setGroupName("groupName");
        cpnLabelDO.setSource(0);
        cpnLabelDO.setIsDisplay(false);
        cpnLabelDO.setIsActive(false);
        cpnLabelDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelDO.setIsListDisplay(false);
        cpnLabelDO.setStatus(0);
        cpnLabelDO.setProductCategoryId(0);
        when(mockMapper.to(any(CpnLabel.class))).thenReturn(cpnLabelDO);

        // Run the test
        final Map<Long, CpnLabelDO> result = queryAllLabelServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_LabelServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockService.queryAllLabel()).thenReturn(Collections.emptyList());

        // Configure LabelMapper.to(...).
        final CpnLabelDO cpnLabelDO = new CpnLabelDO();
        cpnLabelDO.setCode(0L);
        cpnLabelDO.setCType(0);
        cpnLabelDO.setName("name");
        cpnLabelDO.setDescription("description");
        cpnLabelDO.setPos(0);
        cpnLabelDO.setGroupCode("groupCode");
        cpnLabelDO.setGroupName("groupName");
        cpnLabelDO.setSource(0);
        cpnLabelDO.setIsDisplay(false);
        cpnLabelDO.setIsActive(false);
        cpnLabelDO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelDO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        cpnLabelDO.setIsListDisplay(false);
        cpnLabelDO.setStatus(0);
        cpnLabelDO.setProductCategoryId(0);
        when(mockMapper.to(any(CpnLabel.class))).thenReturn(cpnLabelDO);

        // Run the test
        final Map<Long, CpnLabelDO> result = queryAllLabelServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        Assert.notNull(result);
        // Verify the results
    }

    @Test
    public void testPreLoad_LabelServiceThrowsException() throws Exception {
        // Setup
        when(mockService.queryAllLabel()).thenThrow(Exception.class);

        // Run the test
        assertThatThrownBy(() -> queryAllLabelServiceUnderTest.preLoad("area",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).isInstanceOf(RuntimeException.class);
    }
}
