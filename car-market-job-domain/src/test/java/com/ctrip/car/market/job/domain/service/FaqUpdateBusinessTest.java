package com.ctrip.car.market.job.domain.service;


import com.ctrip.car.market.job.repository.dao.carseodb.SeoFaqDao;
import com.ctrip.car.market.job.repository.entity.carseodb.SeoFaq;
import com.ctrip.car.osd.shopping.api.entity.*;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FaqUpdateBusinessTest {

    @Mock
    private SeoFaqDao seoFaqDao;
    @InjectMocks
    private FaqUpdateBusiness faqUpdateBusiness;

    @Test
    public void adddOrUpdateFaq_test1() {
        QueryProductResponseType responseType = buildQueryProductResponseType();

        boolean result = faqUpdateBusiness.adddOrUpdateFaq(responseType, 2l, 2);
        Assert.assertFalse(result);
        result = faqUpdateBusiness.adddOrUpdateFaq(null, 2l, 2);
        Assert.assertFalse(result);
        result = faqUpdateBusiness.adddOrUpdateFaq(new QueryProductResponseType(), 2l, 2);
        Assert.assertFalse(result);
        result = faqUpdateBusiness.adddOrUpdateFaq(responseType, 2l, 1);
        Assert.assertTrue(result);
        result = faqUpdateBusiness.adddOrUpdateFaq(buildQueryProductResponseType2(), 2l, 1);
        Assert.assertTrue(result);
    }

    @Test
    public void adddOrUpdateFaq_test2() throws Exception {
        QueryProductResponseType responseType = buildQueryProductResponseType2();
        List<SeoFaq> seoFaqs = new ArrayList<>();
        SeoFaq seoFaq = new SeoFaq();
        seoFaq.setCityId(1l);
        seoFaqs.add(seoFaq);
        Mockito.when(seoFaqDao.queryBy(any(SeoFaq.class))).thenReturn(seoFaqs);

        boolean result = faqUpdateBusiness.adddOrUpdateFaq(responseType, 2l, 1);
        Assert.assertTrue(result);
    }

    @Test
    public void adddOrUpdateFaq_test3() throws Exception {
        QueryProductResponseType responseType = new QueryProductResponseType();
        List<SeoFaq> seoFaqs = new ArrayList<>();
        SeoFaq seoFaq = new SeoFaq();
        seoFaq.setCityId(1l);
        seoFaqs.add(seoFaq);

        boolean result = faqUpdateBusiness.adddOrUpdateFaq(responseType, 2l, 1);
        Assert.assertFalse(result);

        QueryProductResponseType responseType2 = new QueryProductResponseType();
        responseType2.setVehicles(null);
        result = faqUpdateBusiness.adddOrUpdateFaq(responseType2, 2l, 1);
        Assert.assertFalse(result);
        result = faqUpdateBusiness.adddOrUpdateFaq(null, 2l, 1);
        Assert.assertFalse(result);

    }

    public QueryProductResponseType buildQueryProductResponseType() {

        QueryProductResponseType responseType = new QueryProductResponseType();
        VehicleInfo vehicleInfo = new VehicleInfo();
        vehicleInfo.setBrandId(1);
        vehicleInfo.setId(1L);
        responseType.setVehicles(Collections.singletonList(vehicleInfo));
        responseType.setVendors(new ArrayList<>());
        return responseType;
    }


    public QueryProductResponseType buildQueryProductResponseType2() {

        QueryProductResponseType responseType = new QueryProductResponseType();
        VehicleInfo vehicleInfo = new VehicleInfo();
        vehicleInfo.setBrandId(1);
        vehicleInfo.setId(1L);
        vehicleInfo.setGroupName("GroupName");
        vehicleInfo.setGroupEName("GroupEName");
        responseType.setVehicles(Collections.singletonList(vehicleInfo));

        List<VendorInfo> list = new ArrayList<>();
        for (int i = 100; i < 105; i++) {
            VendorInfo vendorInfo1 = new VendorInfo();
            vendorInfo1.setVendorName("VendorName" + 1);
            ProductInfo productInfo1 = new ProductInfo();
            PriceInfo priceInfo1 = new PriceInfo();
            priceInfo1.setCurrentDailyPrice(new BigDecimal(i));
            productInfo1.setPriceInfoDtoList(Lists.newArrayList(priceInfo1));
            vendorInfo1.setProductInfoDtoList(Lists.newArrayList(productInfo1));
            list.add(vendorInfo1);
        }


        responseType.setVendors(list);
        return responseType;
    }
}
