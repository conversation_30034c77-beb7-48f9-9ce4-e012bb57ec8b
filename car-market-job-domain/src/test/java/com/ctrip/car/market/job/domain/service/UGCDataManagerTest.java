package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.crossrecommend.service.contract.dto.RecommendProductDTO;
import com.ctrip.car.market.crossrecommend.service.contract.servicetype.CarRecommendProductRequestType;
import com.ctrip.car.market.job.domain.utils.QConfigUtil;
import com.ctrip.car.market.job.domain.utils.RedisUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Matchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        RedisUtil.class
})
@SuppressStaticInitializationFor({
        "com.ctrip.car.market.job.domain.utils.RedisUtil"
})
public class UGCDataManagerTest {

    @InjectMocks
    private UGCDataManager uGCDataManager;

    @Before
    public void before() throws Exception {
        PowerMockito.mockStatic(RedisUtil.class);
        PowerMockito.when(RedisUtil.setByte(any(String.class), any(byte[].class), any(Long.class))).thenReturn(true);
    }


    @Test
    public void testsendToRedis1(){
        List<RecommendProductDTO> recommendProducts = new ArrayList<>();
        recommendProducts.add(new RecommendProductDTO(){{setCityName("shanghai");}});
        boolean key1 = uGCDataManager.sendToRedis(null, new CarRecommendProductRequestType(){{setPickUpCityId(1L);}});
        boolean key2 = uGCDataManager.sendToRedis(recommendProducts, new CarRecommendProductRequestType(){{setPickUpCityId(1L);setVehiclegroupIds(Collections.singletonList("88"));}});
        boolean key3 = uGCDataManager.sendToRedis(recommendProducts, new CarRecommendProductRequestType(){{setPickUpCityId(1L);}});
        boolean key4 = uGCDataManager.sendToRedis(recommendProducts, new CarRecommendProductRequestType(){{setPickUpCityId(1L);setAppType("OSD_C_APP");}});

        Assert.assertFalse(key1);
        Assert.assertTrue(key2);
        Assert.assertTrue(key3);
        Assert.assertTrue(key4);
    }
}