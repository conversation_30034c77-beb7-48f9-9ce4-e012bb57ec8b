package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnLabelGroupDO;
import com.ctrip.car.market.job.domain.mapper.LabelGroupMapper;
import com.ctrip.car.market.job.repository.entity.CpnLabelGroup;
import com.ctrip.car.market.job.repository.service.LabelService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllLabelGroupService implements CachePreLoader<Long, CpnLabelGroupDO>, CacheLoader<Long, CpnLabelGroupDO> {

    @Resource
    private LabelService service;

    @Resource
    private LabelGroupMapper mapper;

    private Map<Long, CpnLabelGroupDO> getAllLabelGroup() throws Exception {
        List<CpnLabelGroup> data = service.queryAllLabelGroup();
        return data.stream().collect(Collectors.toMap(CpnLabelGroup::getId, mapper::to));
    }

    @Override
    public CpnLabelGroupDO load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, CpnLabelGroupDO> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllLabelGroup();
    }

    @Override
    public Map<Long, CpnLabelGroupDO> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllLabelGroup();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
