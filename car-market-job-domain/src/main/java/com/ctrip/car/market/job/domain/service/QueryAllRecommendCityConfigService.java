package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.RecommendCityconfigDO;
import com.ctrip.car.market.job.domain.mapper.RecommendCityConfigMapper;
import com.ctrip.car.market.job.repository.entity.RecommendCityconfig;
import com.ctrip.car.market.job.repository.service.LabelService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllRecommendCityConfigService implements CachePreLoader<Integer, List<RecommendCityconfigDO>>, CacheLoader<Integer, List<RecommendCityconfigDO>> {

    @Resource
    private LabelService service;

    @Resource
    private RecommendCityConfigMapper mapper;

    private Map<Integer, List<RecommendCityconfigDO>> getAllRecommendCityConfig() throws Exception {
        List<RecommendCityconfig> data = service.queryAllRecommendCityConfig();
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(RecommendCityconfigDO::getCityId));
    }

    @Override
    public List<RecommendCityconfigDO> load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, List<RecommendCityconfigDO>> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getAllRecommendCityConfig();
    }

    @Override
    public Map<Integer, List<RecommendCityconfigDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllRecommendCityConfig();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
