package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.seo.SeoVendorCommentScoreDO;
import com.ctrip.car.market.job.repository.entity.SeoVendorCommentScore;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SeoVendorCommentScoreMapper {

    SeoVendorCommentScoreDO to(SeoVendorCommentScore value);

    List<SeoVendorCommentScoreDO> to(List<SeoVendorCommentScore> values);
}
