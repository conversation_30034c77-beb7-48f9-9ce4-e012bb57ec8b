package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.CpnGroupVersionDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.GroupVersionMapper;
import com.ctrip.car.market.job.repository.entity.CpnGroupVersion;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class GroupVersionMessageConvert extends AbstractConvertMessage<Integer, List<CpnGroupVersion>, List<CpnGroupVersionDO>> implements ConvertMessage<Integer, List<CpnGroupVersionDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private GroupVersionMapper mapper;

    public GroupVersionMessageConvert() {
        super(TabelEnum.GroupVersion);
    }

    @Override
    public Map<Integer, List<CpnGroupVersionDO>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Integer getKey(String ids) {
        return Integer.valueOf(ids);
    }

    @Override
    public List<CpnGroupVersion> getData(Integer id) throws Exception {
        List<CpnGroupVersion> data = service.queryGroupVersionByGroupId(id);
        return CollectionUtils.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<CpnGroupVersionDO> mapper(List<CpnGroupVersion> value) {
        return mapper.to(value);
    }
}