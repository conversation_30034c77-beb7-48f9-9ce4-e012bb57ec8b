package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.customer.common.util.DateTimeUtils;
import com.ctrip.car.customer.common.util.StringUtil;
import com.ctrip.car.market.job.domain.dto.RiskVerifyBodyAwardInfo;
import com.ctrip.car.market.job.domain.dto.RiskVerifyBodyDto;
import com.ctrip.car.market.job.domain.dto.RiskVerifyBodyExtensionDto;
import com.ctrip.car.market.job.domain.proxy.CaccountBaseTxServerProxy;
import com.ctrip.car.market.job.domain.proxy.RiskUnifiedAccessServiceProxy;
import com.ctrip.car.market.job.domain.utils.*;
import com.ctrip.car.market.job.repository.entity.MktTaskAssistInfo;
import com.ctrip.car.market.job.repository.entity.MktTaskUserInfo;
import com.ctrip.car.market.job.repository.entity.MktWalletCashbackInfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.framework.foundation.Foundation;
import com.ctrip.payment.soa.caccount.base.tx.server.model.PayInReq;
import com.ctrip.payment.soa.caccount.base.tx.server.model.PayInRsp;
import com.ctrip.payment.soa.caccount.base.tx.server.model.ResquestHeaderType;
import com.ctrip.risk.access.RiskUnifiedAccessService.UnifiedRiskVerifyRequest;
import com.ctrip.risk.access.RiskUnifiedAccessService.UnifiedRiskVerifyResponse;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 * @Description:
 * @create 2025/7/8 20:33
 */
@Component
public class MktTaskAssistPayInSchedule {

    @Resource
    private MarketDBService marketDBService;
    @Autowired
    CaccountBaseTxServerProxy caccountBaseTxServerProxy;
    @Autowired
    RiskUnifiedAccessServiceProxy riskUnifiedAccessServiceProxy;

    private static String SIGNTEMPLATE="amount={0}&orderId={1}&orderName={2}&orderType={3}" +
            "&productCode={4}&requestRefNo={5}&requestTime={6}&uid={7}&reqMerchantId={8}&{9}";


    @QSchedule("car.market.task.assist.pay.in")
    public void task(Parameter parameter) throws Exception {
        int pageIndex = 0;
        int pageSize = Integer.parseInt(QConfigUtil.getConfigOrDefault("mkt.task.assist.query.pageSize", "100"));
        Long beforeTime = QConfigUtil.getConfigLong("mkt.task.assist.before.minute", "3");
        String cashbackEndTime = DateTimeUtils.timeFormatDefault(DateTimeUtils.addMinutesOnCurrent(beforeTime.intValue()));
        List<MktTaskAssistInfo> allMktTaskAssistInfo = new ArrayList<>();
        while (true) {
            List<MktTaskAssistInfo> mktTaskAssistInfos = marketDBService.queryWaitCashbackByPage(pageIndex, pageSize,cashbackEndTime);
            if (CollectionUtils.isEmpty(mktTaskAssistInfos)) {
                break;
            }
            allMktTaskAssistInfo.addAll(mktTaskAssistInfos);
            pageIndex += pageSize;
        }
        for (MktTaskAssistInfo mktTaskAssistInfo : allMktTaskAssistInfo) {
            int maxRetries = 3;
            callPayInWithRetries(mktTaskAssistInfo,maxRetries);
        }

    }

    private void callPayInWithRetries(MktTaskAssistInfo mktTaskAssistInfo, int maxRetries) {
        List<MktTaskUserInfo> mktTaskUserInfoList = new ArrayList<>();
        try {
            mktTaskUserInfoList = marketDBService.queryTaskUserInfo(mktTaskAssistInfo.getProjectId(), mktTaskAssistInfo.getTaskId(), mktTaskAssistInfo.getOrderId());
            if (CollectionUtils.isEmpty(mktTaskUserInfoList)) {
                CLogUtil.error("mktTaskUserInfoList", "empty");
                return;
            }
            UnifiedRiskVerifyRequest unifiedRiskVerifyRequest = buildUnifiedRiskVerifyRequest(mktTaskAssistInfo, mktTaskUserInfoList.get(0));
            UnifiedRiskVerifyResponse unifiedRiskVerifyResponse = riskUnifiedAccessServiceProxy.riskVerify(unifiedRiskVerifyRequest);
            if (unifiedRiskVerifyResponse != null && unifiedRiskVerifyResponse.getRiskLevel() !=null && unifiedRiskVerifyResponse.getRiskLevel() >= 200) {
                //有风险
                mktTaskAssistInfo.setCashbackStatus(4);
                marketDBService.updateMktTaskAssistInfo(mktTaskAssistInfo);
                Metrics.build().withTag("riskLevel",unifiedRiskVerifyResponse.getRiskLevel().toString()).withTag("result","fail").recordOne("assistPayInRiskVerify");
                return;
            }
            //返现之前查询下，是否有记录，避免多次打款
            List<MktWalletCashbackInfo> mktWalletCashbackInfos = marketDBService.selectMktWalletCashbackInfoByTaskAssistId(mktTaskAssistInfo.getId());
            if (CollectionUtils.isNotEmpty(mktWalletCashbackInfos)){ //已经有打款过
                MktWalletCashbackInfo mktWalletCashbackInfo = mktWalletCashbackInfos.stream().filter(x -> Objects.equals(x.getResultCode(), 0) && StringUtils.isNotBlank(x.getMainPayInRecordId())).findFirst().orElse(null);
                if (mktWalletCashbackInfo !=null){//打款成功过
                    return;
                }
            }
            int retries = 0;
            PayInReq payInReq = convert2Req(mktTaskAssistInfo);
            MktWalletCashbackInfo mktWalletCashbackInfo = JsonUtil.parseObject(JsonUtil.toJSONString(payInReq), MktWalletCashbackInfo.class);
            mktWalletCashbackInfo.setReqMerchantId(payInReq.getRequestHeader().getReqMerchantId());
            mktWalletCashbackInfo.setTaskAssistId(mktTaskAssistInfo.getId());
            marketDBService.insertMktWalletCashbackInfo(mktWalletCashbackInfo);
            while (retries < maxRetries) {
                PayInRsp payInRsp = caccountBaseTxServerProxy.payIn(payInReq);
                if (payInRsp != null && payInRsp.getResponseHeader() != null && Objects.equals(payInRsp.getResponseHeader().getResultCode(), "0")) {
                    mktTaskAssistInfo.setCashbackStatus(2);
                    marketDBService.updateMktTaskAssistInfo(mktTaskAssistInfo);
                    mktWalletCashbackInfo.setResultCode(0);
                    mktWalletCashbackInfo.setDescription(payInRsp.getResponseHeader().getDescription());
                    mktWalletCashbackInfo.setMainPayInRecordId(payInRsp.getMainPayInRecordId());
                    marketDBService.updateMktWalletCashbackInfo(mktWalletCashbackInfo);
                    break;
                } else { // 报错
                    if (retries == maxRetries-1) { //最后一次也是失败,埋点告警
                        Metrics.build().withTag("retries", String.valueOf(maxRetries)).withTag("result","fail").recordOne("callPayInWithRetries");
                    }
                    if (payInRsp !=null && payInRsp.getResponseHeader() != null) {
                        mktWalletCashbackInfo.setResultCode(Integer.valueOf(payInRsp.getResponseHeader().getResultCode()));
                        mktWalletCashbackInfo.setMainPayInRecordId(payInRsp.getMainPayInRecordId());
                        mktWalletCashbackInfo.setDescription(payInRsp.getResponseHeader().getDescription());
                        CLogUtil.warn("mktWalletCashbackInfo::fail", "resultCode:" + payInRsp.getResponseHeader().getResultCode() + " description:" + payInRsp.getResponseHeader().getDescription());
                        marketDBService.updateMktWalletCashbackInfo(mktWalletCashbackInfo);
                    }
                }
                retries++;

            }
        } catch (Exception e) {
            CLogUtil.error("callPayInWithRetries", e.getMessage());
            Metrics.build().withTag("result","fail").recordOne("callPayInWithRetriesException");

        }
    }

    private UnifiedRiskVerifyRequest buildUnifiedRiskVerifyRequest(MktTaskAssistInfo mktTaskAssistInfo, MktTaskUserInfo mktTaskUserInfo) {

        UnifiedRiskVerifyRequest unifiedRiskVerifyRequest= new UnifiedRiskVerifyRequest();
        unifiedRiskVerifyRequest.setAppid(Foundation.app().getAppId());
        unifiedRiskVerifyRequest.setRequestTime(DateTimeUtils.getCurrentTimeWithDefaultString());
        unifiedRiskVerifyRequest.setSceneCode("car_rental_order_invite_cashback");
        RiskVerifyBodyDto riskVerifyBodyDto = new RiskVerifyBodyDto();
        riskVerifyBodyDto.setUid(mktTaskUserInfo.getUserId());
        riskVerifyBodyDto.setClientId(mktTaskUserInfo.getClientId());
        riskVerifyBodyDto.setUserIp(mktTaskUserInfo.getIp());
        riskVerifyBodyDto.setServerFrom(mktTaskUserInfo.getPlatform());
        riskVerifyBodyDto.setScene("04");
        riskVerifyBodyDto.setPlatform("CTRIP");
        RiskVerifyBodyExtensionDto extensionDto  = new RiskVerifyBodyExtensionDto();
        extensionDto.setInvokeId("04202507302");
        extensionDto.setActivityId("042025073");
        extensionDto.setActivityType("11");
        extensionDto.setActivityName(QConfigUtil.getConfigOrDefault("mkt.task.assist.merchant.pay.in.activity.name", ""));
        extensionDto.setPageSrc("3");
        RiskVerifyBodyAwardInfo awardInfo = new RiskVerifyBodyAwardInfo();
        awardInfo.setAwardCurrency("CNY");
        awardInfo.setAwardType("cash");
        awardInfo.setAwardValue(mktTaskAssistInfo.getCashbackMoney().toString());
        extensionDto.setOrderId(mktTaskUserInfo.getOrderId().toString());
        extensionDto.setOrderAmount(mktTaskUserInfo.getOrderRentalCarFee().toString());
        extensionDto.setAwardInfoList(Arrays.asList(awardInfo));
        riskVerifyBodyDto.setExtension(extensionDto);
        unifiedRiskVerifyRequest.setEventBody(JsonUtil.toJSONString(riskVerifyBodyDto));
        return  unifiedRiskVerifyRequest;
    }

    @SneakyThrows
    private PayInReq convert2Req(MktTaskAssistInfo mktTaskAssistInfo) {
        PayInReq payInReq = new PayInReq();
        String assistMerchantId = QConfigUtil.getConfigOrDefault("mkt.task.assist.merchant.id", "");
        String assistMerchantSignKey = QConfigUtil.getConfigOrDefault("mkt.task.assist.merchant.sign.key", "");
        ResquestHeaderType resquestHeaderType = new ResquestHeaderType();
        resquestHeaderType.setReqMerchantId(assistMerchantId);//待申请
        payInReq.setRequestRefNo(UUID.randomUUID().toString());
        payInReq.setUid(mktTaskAssistInfo.getUserId());
        payInReq.setRequestTime(DateTimeUtils.getCurrentTimeWithDefaultString());
        payInReq.setProductCode("1011");
        //交易金额 单位：分
        payInReq.setAmount(mktTaskAssistInfo.getCashbackMoney().multiply(BigDecimal.valueOf(100)).longValue());
        payInReq.setOrderId(mktTaskAssistInfo.getOrderId().toString());
        payInReq.setOrderName(QConfigUtil.getConfigOrDefault("mkt.task.assist.merchant.pay.in.order.name", ""));
        payInReq.setOrderType(105);

        String signContent = StringFormat.format(SIGNTEMPLATE
                , payInReq.getAmount(), payInReq.getOrderId(), payInReq.getOrderName(), payInReq.getOrderType(), payInReq.getProductCode(), payInReq.getRequestRefNo(), payInReq.getRequestTime(), payInReq.getUid(),
                assistMerchantId,assistMerchantSignKey);
        String signCode = Base.getMD5(signContent);
        resquestHeaderType.setSignCode(signCode);
        payInReq.setRequestHeader(resquestHeaderType);
        return payInReq;
    }

}
