package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.CpnRestrictedConditionDO;
import com.ctrip.car.market.job.repository.entity.CpnRestrictedCondition;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RestrictedConditionMapper {

    List<CpnRestrictedConditionDO> to(List<CpnRestrictedCondition> cpnRestrictedConditions);

    CpnRestrictedConditionDO to(CpnRestrictedCondition cpnRestrictedCondition);
}
