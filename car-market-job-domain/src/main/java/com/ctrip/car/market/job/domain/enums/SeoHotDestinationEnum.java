package com.ctrip.car.market.job.domain.enums;

import lombok.Getter;
import org.checkerframework.checker.units.qual.A;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Getter
public enum SeoHotDestinationEnum {
    COUNTRY("COUNTRY", "key.seo.carrental","countryId"),
    CITY("CITY", "key.seo.carrental","cityId"),
    AIRPORT("AIRPORT","key.seo.airport","airportCode"),
    SUPPLIER("SUPPLIER","key.seo.vendor","c_supplier"),
    SUPPLIER_CITY("SUPPLIER_CITY","key.seo.vendor.city","vendorNameAndCityName");

    private final String pageType;
    private final String sharkKey;
    private final String productName;

    SeoHotDestinationEnum(String pageType, String sharkKey,String productName) {
        this.pageType = pageType;
        this.sharkKey = sharkKey;
        this.productName = productName;
    }

    public static String getSharkKeyByPageType(String pageType){
        SeoHotDestinationEnum seoHotDestinationEnum = Arrays.stream(SeoHotDestinationEnum.values()).filter(x -> pageType.equals(x.getPageType())).findFirst().orElse(COUNTRY);
        return seoHotDestinationEnum.getSharkKey();
    }
}
