package com.ctrip.car.market.job.domain.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;

public class FastJsonUtil {

    private static Gson gson = new GsonBuilder().serializeNulls().disableHtmlEscaping().create();

    public static String serialize(Object object) {
        return gson.toJson(object);
    }


    public static <T> T DeserializeToObject(String json, Class<T> clazz) {
        return (T) gson.fromJson(json, clazz);
    }

    public static <T> T DeserializeToObject(String json, Type typeOfT) {
        return gson.fromJson(json, typeOfT);
    }

    public static <T> List<T> DeserializeToList(String json, Class<T[]> clazz) {
        T[] array = gson.fromJson(json, clazz);
        return Arrays.asList(array);
    }
}
