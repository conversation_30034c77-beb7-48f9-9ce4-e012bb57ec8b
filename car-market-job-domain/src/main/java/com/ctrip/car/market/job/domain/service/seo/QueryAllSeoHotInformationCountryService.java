package com.ctrip.car.market.job.domain.service.seo;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotInformationDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotInformationMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotInformation;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllSeoHotInformationCountryService implements CachePreLoader<Integer, List<SeoHotInformationDO>>, CacheLoader<Integer, List<SeoHotInformationDO>> {

    @Resource
    private MarketDBService service;

    @Resource
    private SeoHotInformationMapper mapper;

    private Map<Integer, List<SeoHotInformationDO>> getAllHotInformation() throws Exception {
        List<SeoHotInformation> data = service.queryALLInformation();
        return data.stream().filter(l -> l.getCountryId() != null).map(mapper::to).collect(Collectors.groupingBy(SeoHotInformationDO::getCountryId));
    }

    @Override
    public List<SeoHotInformationDO> load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, List<SeoHotInformationDO>> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getAllHotInformation();
    }

    @Override
    public Map<Integer, List<SeoHotInformationDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllHotInformation();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
