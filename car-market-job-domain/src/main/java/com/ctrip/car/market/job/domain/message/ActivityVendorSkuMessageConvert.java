package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.ActVendorSkuInfoDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.ActVendorSkuInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.VendorSkuinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class ActivityVendorSkuMessageConvert extends AbstractConvertMessage<Long, List<VendorSkuinfo>, List<ActVendorSkuInfoDO>> implements ConvertMessage<Long, List<ActVendorSkuInfoDO>> {

    @Resource
    private ActivityService service;

    @Resource
    private ActVendorSkuInfoMapper mapper;

    public ActivityVendorSkuMessageConvert() {
        super(TabelEnum.ActivityVendorSKu);
    }

    @Override
    public Map<Long, List<ActVendorSkuInfoDO>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Long getKey(String ids) {
        return Long.valueOf(ids);
    }

    @Override
    public List<VendorSkuinfo> getData(Long id) throws Exception {
        List<VendorSkuinfo> data = service.queryVendorSku(id);
        return CollectionUtils.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<ActVendorSkuInfoDO> mapper(List<VendorSkuinfo> value) {
        return mapper.to(value);
    }
}
