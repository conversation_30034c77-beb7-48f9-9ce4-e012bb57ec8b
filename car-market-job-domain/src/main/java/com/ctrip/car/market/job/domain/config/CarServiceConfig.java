package com.ctrip.car.market.job.domain.config;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Map;

/**
 * Created by h_z<PERSON><PERSON> on 2017/11/13.
 */
@Service
public class CarServiceConfig {

    @QConfig("conf.properties")
    private Map<String, String> ConfConfig;


    public Map<String, String> getConfConfig() {
        return ConfConfig;
    }

    public void setConfConfig(Map<String, String> confConfig) {
        ConfConfig = confConfig;
    }

    public String getValueFromConfConfigByKey(String key) {
        if (MapUtils.isNotEmpty(ConfConfig) && ConfConfig.containsKey(key)) {
            return ConfConfig.get(key);
        }
        return null;
    }

    public String geIdfaChannelIdFromConfConfigByKey(int channelId ) {
        String key="idfa_"+channelId;
        if (MapUtils.isNotEmpty(ConfConfig) && ConfConfig.containsKey(key)) {
            return ConfConfig.get(key);
        }
        return null;
    }
}
