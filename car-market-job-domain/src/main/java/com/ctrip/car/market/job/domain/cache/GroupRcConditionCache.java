package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnGroupRcConditionDO;
import com.ctrip.car.market.job.domain.message.GroupRcConditionMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllGroupRcConditionService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.GROUP_RC_CONDITION_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.GroupRcConditionCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(GroupRcConditionCache.Name)
@CreateCacheArea(area = "public")
public class GroupRcConditionCache extends BasicCacheAbstract<Integer, CpnGroupRcConditionDO>{

    public static final String Name = GroupRcConditionCacheName;

    @Resource
    private GroupRcConditionMessageConvert convert;

    @Resource
    private QueryAllGroupRcConditionService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = GROUP_RC_CONDITION_HASH_KEY, remotePreLoadName = Name)
    private Cache<Integer, CpnGroupRcConditionDO> cache;


    @Override
    public ConvertMessage<Integer, CpnGroupRcConditionDO> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Integer, CpnGroupRcConditionDO> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, CpnGroupRcConditionDO> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, CpnGroupRcConditionDO> getLoader() {
        return null;
    }
}
