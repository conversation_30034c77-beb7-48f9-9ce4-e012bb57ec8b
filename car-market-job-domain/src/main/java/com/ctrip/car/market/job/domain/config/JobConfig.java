package com.ctrip.car.market.job.domain.config;

import com.ctrip.car.market.job.domain.dto.CityPoiConfigItem;
import com.ctrip.car.market.job.domain.dto.SeoHotVendorDTO;
import com.ctrip.car.market.job.domain.dto.UseStationDto;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class JobConfig {

    @QMapConfig("jobConfig.properties")
    public void onChange(Map<String, String> map) {
        this.expiredDate = map.get("expiredDate");
        this.queryLimit = Integer.valueOf(map.getOrDefault("queryLimit", "1000"));
        this.sleep = Long.valueOf(map.getOrDefault("sleep", "100"));
        this.couponClearControl = StringUtils.equalsIgnoreCase(map.getOrDefault("couponClearControl", "1"), "1");
        this.expiredDay = Integer.valueOf(map.getOrDefault("expiredDay", "365"));
        this.sendEmail = map.getOrDefault("sendEmail", "1").equals("1");
        this.promotionEmailSubject = map.getOrDefault("promotionEmailSubject", "promotionSubject");
        this.emailAcceptors = Lists.newArrayList(map.getOrDefault("emailAcceptors", "<EMAIL>").split(","));
        this.promotionId = map.getOrDefault("promotionId", "promotionId");
        this.promotionName = map.getOrDefault("promotionName", "promotionName");
        this.promotionDisplayName = map.getOrDefault("promotionDisplayName", "promotionDisplayName");
        this.useProductLine = map.getOrDefault("useProductLine", "useProductLine");
        this.useStation = map.getOrDefault("useStation", "useStation");
        this.seoPlatformCacheExpired = Integer.valueOf(map.getOrDefault("seoPlatformCacheExpired", "3"));
    }

    @QConfig("useStationConfig.json")
    private List<UseStationDto> useStationList;

    @QConfig("seoCityPoiConfig.json")
    private List<CityPoiConfigItem> cityPoiConfigList;

    @QConfig("100014699#seoHotVendorConfig.json")
    private SeoHotVendorDTO seoHotVendor;

    private boolean sendEmail;

    private String promotionEmailSubject;

    private List<String> emailAcceptors;

    private String promotionId;

    private String promotionName;

    private String promotionDisplayName;

    private String useProductLine;

    private String useStation;

    private String expiredDate;

    private Integer queryLimit;

    private Long sleep;

    private Boolean couponClearControl;

    private Integer expiredDay;

    private Integer seoPlatformCacheExpired;

    public String getExpiredDate() {
        return expiredDate;
    }

    public Integer getQueryLimit() {
        return queryLimit;
    }

    public Long getSleep() {
        return sleep;
    }

    public Boolean getCouponClearControl() {
        return couponClearControl;
    }

    public Integer getExpiredDay() {
        return expiredDay;
    }

    public List<UseStationDto> getUseStationList() {
        return useStationList;
    }

    public boolean isSendEmail() {
        return sendEmail;
    }

    public String getPromotionEmailSubject() {
        return promotionEmailSubject;
    }

    public List<String> getEmailAcceptors() {
        return emailAcceptors;
    }

    public String getPromotionId() {
        return promotionId;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public String getPromotionDisplayName() {
        return promotionDisplayName;
    }

    public String getUseProductLine() {
        return useProductLine;
    }

    public String getUseStation() {
        return useStation;
    }

    public Integer getSeoPlatformCacheExpired() {
        return seoPlatformCacheExpired;
    }

    public List<CityPoiConfigItem> getCityPoiConfigList() {
        return cityPoiConfigList;
    }

    public SeoHotVendorDTO getSeoHotVendor() {
        return seoHotVendor;
    }
}
