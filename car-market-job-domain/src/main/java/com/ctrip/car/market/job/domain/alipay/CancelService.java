package com.ctrip.car.market.job.domain.alipay;

import com.ctrip.car.market.job.domain.config.AlipayMessageConfig;
import com.ctrip.car.market.job.domain.constant.CommonConstant;
import com.ctrip.car.market.job.domain.dto.MessageData;
import com.ctrip.car.market.job.domain.dto.MessageInfo;
import com.ctrip.car.market.job.domain.enums.MessageTempEnum;
import com.ctrip.car.market.job.domain.utils.DateTimeFormat;
import com.ctrip.car.market.job.domain.utils.LanguageUtils;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.car.market.job.repository.entity.AlipaySubscribeTemplateinfo;
import com.ctrip.car.order.context.query.offline.ActivityInfo;
import com.ctrip.car.order.context.query.offline.OrderDetail;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Objects;
import java.util.Optional;

@Component
public class CancelService extends AlipayOrderService {

    @Resource
    private AlipayService service;

    @Resource
    private AlipayMessageConfig alipayMessageConfig;

    @Override
    protected MessageInfo buildMessage(Long orderId, OrderDetail orderDetail) {
        AlipaySubscribeTemplateinfo templateInfo = service.queryMessageTemplate(MessageTempEnum.Cancel.getType());
        if (templateInfo == null) {
            Metrics.build().withTag("type", String.valueOf(MessageTempEnum.Cancel.getType())).recordOne("alipayMessageTemplateError");
            return null;
        }
        String url = Objects.equals(CommonConstant.ISD, orderDetail.getProductInfo().getProductCategoryId()) ? templateInfo.getIsdUrl() : templateInfo.getOsdUrl();
        MessageInfo messageInfo = new MessageInfo();
        messageInfo.setData(new MessageData());
        messageInfo.setUid(orderDetail.getCustomerInfo().getUid());
        messageInfo.setTemplateId(templateInfo.getTemplateId());
        messageInfo.setPageUrl(LanguageUtils.format(url, orderId));
        messageInfo.getData().getKeyword1().setValue(DateTimeFormat.yyyyMMdd(Calendar.getInstance()));
        messageInfo.getData().getKeyword2().setValue(orderDetail.getCarTypeInfo().getCarType());
        messageInfo.getData().getKeyword3().setValue(getDiscountAmount(orderDetail));
        messageInfo.getData().getKeyword4().setValue(orderId.toString());
        return messageInfo;
    }

    private String getDiscountAmount(OrderDetail orderDetail) {
        BigDecimal amount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(orderDetail.getCouponList())) {
            amount = amount.add(Optional.ofNullable(orderDetail.getCouponList().get(0).getCouponAmount()).orElse(BigDecimal.ZERO));
        }
        if (CollectionUtils.isNotEmpty(orderDetail.getActivityInfo())) {
            ActivityInfo activityInfo = orderDetail.getActivityInfo().stream().filter(l -> l.getActivityId() != null && l.getActivityId() > 0).findFirst().orElse(null);
            if (activityInfo != null) {
                amount = amount.add(Optional.ofNullable(activityInfo.getActivityAmount()).orElse(BigDecimal.ZERO));
            }
        }
        if (amount.compareTo(BigDecimal.ZERO) > 0) {
            return amount.intValue() + alipayMessageConfig.getDiscountSuffix();
        }
        return alipayMessageConfig.getNoDiscount();
    }
}
