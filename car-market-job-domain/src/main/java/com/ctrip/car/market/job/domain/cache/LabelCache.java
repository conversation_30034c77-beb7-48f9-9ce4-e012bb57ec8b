package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnLabelDO;
import com.ctrip.car.market.job.domain.message.LabelMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllLabelService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.LABEL_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.LabelCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(LabelCache.Name)
@CreateCacheArea(area = "public")
public class LabelCache extends BasicCacheAbstract<Long, CpnLabelDO> {

    public static final String Name = LabelCacheName;

    @Resource
    private QueryAllLabelService service;

    @Resource
    private LabelMessageConvert convert;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = LABEL_HASH_KEY, remotePreLoadName = Name)
    private Cache<Long, CpnLabelDO> cache;

    @Override
    public ConvertMessage<Long, CpnLabelDO> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Long, CpnLabelDO> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, CpnLabelDO> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, CpnLabelDO> getLoader() {
        return null;
    }
}
