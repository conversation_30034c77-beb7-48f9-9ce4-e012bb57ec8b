package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnGroupVersionDO;
import com.ctrip.car.market.job.domain.mapper.GroupVersionMapper;
import com.ctrip.car.market.job.repository.entity.CpnGroupVersion;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllGroupVersionService implements CachePreLoader<Integer, List<CpnGroupVersionDO>>, CacheLoader<Integer, List<CpnGroupVersionDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private GroupVersionMapper mapper;

    private Map<Integer, List<CpnGroupVersionDO>> getAllGroupVersion() throws Exception {
        List<CpnGroupVersion> data = service.queryAllGroupVersion();
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(CpnGroupVersionDO::getGroupId));
    }

    @Override
    public List<CpnGroupVersionDO> load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, List<CpnGroupVersionDO>> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getAllGroupVersion();
    }

    @Override
    public Map<Integer, List<CpnGroupVersionDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllGroupVersion();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
