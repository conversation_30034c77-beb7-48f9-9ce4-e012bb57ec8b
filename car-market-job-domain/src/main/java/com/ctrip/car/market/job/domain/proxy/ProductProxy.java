package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.osd.shopping.api.entity.QueryProductRequestType;
import com.ctrip.car.osd.shopping.api.entity.QueryProductResponseType;
import com.ctrip.car.osd.shopping.api.service.CarOsdShoppingServiceClient;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ProductProxy {
    private final ILog log = LogManager.getLogger(OsdBasicDataProxy.class);
    public CarOsdShoppingServiceClient client = CarOsdShoppingServiceClient.getInstance();

    public QueryProductResponseType queryProduct(QueryProductRequestType request) {
        Map<String, String> tags = new HashMap<>();
        tags.put("requestId", request.getBaseRequest().getRequestId());
        tags.put("sourceCountryId", request.getBaseRequest().getSourceCountryId() + "");
        tags.put("pickupCityId", request.getPickupLocation().getCityId() + "");
        tags.put("returnCityId", request.getReturnLocation().getCityId() + "");
        try {
            QueryProductResponseType response = client.queryProduct(request);
            log.info("Client-queryProduct", JsonUtil.toJSONString(request) + "\r\n" + ((response != null && CollectionUtils.isNotEmpty(response.getVehicles())) ? response.getVehicles().size() : 0), tags);
            return response;
        } catch (Exception e) {
            log.error("Client-queryProduct", e.getMessage());
        }
        return null;
    }

}
