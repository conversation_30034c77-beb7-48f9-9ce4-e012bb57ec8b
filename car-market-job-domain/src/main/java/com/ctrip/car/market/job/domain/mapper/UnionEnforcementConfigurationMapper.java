package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.UnionEnforcementconfigurationDO;
import com.ctrip.car.market.job.repository.entity.UnionEnforcementconfiguration;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface UnionEnforcementConfigurationMapper {

    UnionEnforcementconfigurationDO to(UnionEnforcementconfiguration value);

    List<UnionEnforcementconfigurationDO> to(List<UnionEnforcementconfiguration> values);
}
