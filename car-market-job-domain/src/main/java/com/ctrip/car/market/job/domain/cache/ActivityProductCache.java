package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.ActProductDO;
import com.ctrip.car.market.job.domain.message.ActivityProductMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllActivityProductService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.ACT_PRODUCT_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.IsdActivityProductCacheName;

@Component(ActivityProductCache.Name)
@CreateCacheArea(area = "public")
public class ActivityProductCache extends BasicCacheAbstract<Long, List<ActProductDO>>{

    public static final String Name = IsdActivityProductCacheName;

    @Resource
    private QueryAllActivityProductService service;

    @Resource
    private ActivityProductMessageConvert convert;

    @IncUpdateConsumerByQmq(subject = "car.market.activity.data.change", convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = ACT_PRODUCT_KEY, remotePreLoadName = Name)
    private Cache<Long, List<ActProductDO>> cache;

    @Override
    public ConvertMessage<Long, List<ActProductDO>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Long, List<ActProductDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, List<ActProductDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, List<ActProductDO>> getLoader() {
        return null;
    }
}