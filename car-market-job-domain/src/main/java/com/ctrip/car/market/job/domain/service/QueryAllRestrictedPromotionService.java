package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnRestrictedPromotionDO;
import com.ctrip.car.market.job.domain.mapper.RestrictedPromotionMapper;
import com.ctrip.car.market.job.repository.entity.CpnRestrictedPromotion;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllRestrictedPromotionService implements CachePreLoader<Integer, CpnRestrictedPromotionDO>, CacheLoader<Integer, CpnRestrictedPromotionDO> {

    @Resource
    private RestrictedService service;

    @Resource
    private RestrictedPromotionMapper mapper;

    private Map<Integer, CpnRestrictedPromotionDO> getAllRestrictedPromotion() throws Exception {
        List<CpnRestrictedPromotion> data = service.queryAllRestrictedPromotion();
        return data.stream().collect(Collectors.toMap(CpnRestrictedPromotion::getPromotionId, l -> mapper.to(l)));
    }


    @Override
    public CpnRestrictedPromotionDO load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, CpnRestrictedPromotionDO> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllRestrictedPromotion();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<Integer, CpnRestrictedPromotionDO> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getAllRestrictedPromotion();
    }
}
