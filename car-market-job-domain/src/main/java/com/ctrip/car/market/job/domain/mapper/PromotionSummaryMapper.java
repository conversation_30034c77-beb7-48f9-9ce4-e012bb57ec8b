package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.CpnPromotionSummaryDO;
import com.ctrip.car.market.job.repository.entity.CpnPromotionSummary;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PromotionSummaryMapper {

    CpnPromotionSummaryDO to(CpnPromotionSummary value);

    List<CpnPromotionSummaryDO> to(List<CpnPromotionSummary> values);
}
