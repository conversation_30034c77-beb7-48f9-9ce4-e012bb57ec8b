package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.ActTempInfoDO;
import com.ctrip.car.market.job.domain.message.ActivityTempMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllActivityTempService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.ACT_TEMP_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.IsdActivityTempCacheName;

@Component(ActivityTempCache.Name)
@CreateCacheArea(area = "public")
public class ActivityTempCache extends BasicCacheAbstract<Long, ActTempInfoDO>{

    public static final String Name = IsdActivityTempCacheName;

    @Resource
    private QueryAllActivityTempService service;

    @Resource
    private ActivityTempMessageConvert convert;

    @IncUpdateConsumerByQmq(subject = "car.market.activity.data.change", convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = ACT_TEMP_KEY, remotePreLoadName = Name)
    private Cache<Long, ActTempInfoDO> cache;

    @Override
    public ConvertMessage<Long, ActTempInfoDO> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Long, ActTempInfoDO> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, ActTempInfoDO> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, ActTempInfoDO> getLoader() {
        return null;
    }
}
