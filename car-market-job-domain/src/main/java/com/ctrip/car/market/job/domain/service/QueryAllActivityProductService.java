package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.ActProductDO;
import com.ctrip.car.market.job.domain.mapper.ActProductInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.ActProductids;
import com.ctrip.car.market.job.repository.service.ActivityService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllActivityProductService implements CachePreLoader<Long, List<ActProductDO>>, CacheLoader<Long, List<ActProductDO>> {

    @Resource
    private ActivityService service;

    @Resource
    private ActProductInfoMapper mapper;

    private Map<Long, List<ActProductDO>> getAllActivityProduct() throws Exception {
        List<ActProductids> data = service.queryActivityProduct();
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(ActProductDO::getActivityId));
    }

    @Override
    public List<ActProductDO> load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, List<ActProductDO>> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllActivityProduct();
    }

    @Override
    public Map<Long, List<ActProductDO>> preLoad(String s, Date date) {
        try {
            return getAllActivityProduct();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}