package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.car.market.job.domain.dto.MessageInfo;
import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.market.publicplatform.soaclient.*;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component
public class AlipayProxy {

    private final AlipayTokenServiceClient alipayClient = AlipayTokenServiceClient.getInstance();

    public AlipayTemplateMessageSendResponse sendMessage(MessageInfo messageInfo) {
        AlipayTemplateMessageSendRequest request = new AlipayTemplateMessageSendRequest();
        request.setAppId("2018081561055519");
        request.setBu("CAR");
        request.setData(JsonUtil.toJSONString(messageInfo.getData()));
        request.setPage(messageInfo.getPageUrl());
        request.setToUserId(messageInfo.getUid());
        request.setUserTemplateId(messageInfo.getTemplateId());
        request.setToUserIdType(2);
        request.setToUserIdSourceType(1);
        AlipayTemplateMessageSendResponse response = null;
        try {
            Map<String, String> tag = Maps.newHashMap();
            tag.put("uid", messageInfo.getUid());
            tag.put("templateId", messageInfo.getTemplateId());
            response = alipayClient.alipayTemplateMessageSend(request);
            CLogUtil.tagInfo("sendMessage", tag, "request", request, "response", response);
            return response;
        } catch (Exception e) {
            CLogUtil.warn("sendMessage", e);
            return null;
        } finally {
            String result = Objects.nonNull(response) && Objects.equals(response.getCode(), 0) ? "1" : "0";
            String msg = Objects.nonNull(response) && Optional.ofNullable(response.getCode()).orElse(-1) > 0 ? getMsg(response.getMsg()) : "success";
            Metrics.build().withTag("templateId", messageInfo.getTemplateId())
                    .withTag("result", result)
                    .withTag("msg", msg)
                    .recordOne("alipayMessageSend");
        }
    }

    public boolean isSubscribe(String uid, String templateId) {
        AlipayMessageSubscribeQueryRequest request = new AlipayMessageSubscribeQueryRequest();
        request.setAppId("2018081561055519");
        request.setUid(uid);
        request.setTemplateId(templateId);
        request.setUidType(2);
        request.setUidSourceType(1);
        try {
            AlipayMessageSubscribeQueryResponse response = alipayClient.alipayMessageSubscribeQuery(request);
            CLogUtil.info("isSubscribe", "request", request, "response", response);
            return Objects.nonNull(response) && Objects.nonNull(response.getResult()) && Objects.equals(response.getResult().getSubscribeState(), "1");
        } catch (Exception e) {
            return false;
        }
    }

    private String getMsg(String msg) {
        if (StringUtils.isEmpty(msg)) {
            return "success";
        }
        if (msg.contains("sub_msg=")) {
            return msg.substring(msg.lastIndexOf("sub_msg="));
        }
        return msg;
    }
}
