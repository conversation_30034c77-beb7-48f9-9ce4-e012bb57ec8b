package com.ctrip.car.market.job.domain.dto;

import java.util.List;

public class QunarSearchInfoDTO {
    private String name;
    private String productId;
    private Integer weight;
    private String city;
    private String province;
    private String country;
    private List<SchemeInfoDTO> schemeInfos;
    private String lowestVID;
    private String highestVID;
    private List<String> aliasList;
    private String type;
    private String POI;
    private Double score;
    private Integer count;
    private String googleLat;
    private String googleLon;
    private String baiduLat;
    private String baiduLon;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public List<SchemeInfoDTO> getSchemeInfos() {
        return schemeInfos;
    }

    public void setSchemeInfos(List<SchemeInfoDTO> schemeInfos) {
        this.schemeInfos = schemeInfos;
    }

    public String getLowestVID() {
        return lowestVID;
    }

    public void setLowestVID(String lowestVID) {
        this.lowestVID = lowestVID;
    }

    public String getHighestVID() {
        return highestVID;
    }

    public void setHighestVID(String highestVID) {
        this.highestVID = highestVID;
    }

    public List<String> getAliasList() {
        return aliasList;
    }

    public void setAliasList(List<String> aliasList) {
        this.aliasList = aliasList;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPOI() {
        return POI;
    }

    public void setPOI(String POI) {
        this.POI = POI;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getGoogleLat() {
        return googleLat;
    }

    public void setGoogleLat(String googleLat) {
        this.googleLat = googleLat;
    }

    public String getGoogleLon() {
        return googleLon;
    }

    public void setGoogleLon(String googleLon) {
        this.googleLon = googleLon;
    }

    public String getBaiduLat() {
        return baiduLat;
    }

    public void setBaiduLat(String baiduLat) {
        this.baiduLat = baiduLat;
    }

    public String getBaiduLon() {
        return baiduLon;
    }

    public void setBaiduLon(String baiduLon) {
        this.baiduLon = baiduLon;
    }
}
