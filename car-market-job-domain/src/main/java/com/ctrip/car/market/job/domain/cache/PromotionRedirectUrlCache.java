package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnPromotionidredirecturlDO;
import com.ctrip.car.market.job.domain.message.PromotionRedirectUrlMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllPromotionRedirectUrlService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.PROMOTION_REDIRECT_URL_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.PromotionRedirectUrlCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(PromotionRedirectUrlCache.Name)
@CreateCacheArea(area = "public")
public class PromotionRedirectUrlCache extends BasicCacheAbstract<Long, CpnPromotionidredirecturlDO>{

    public static final String Name = PromotionRedirectUrlCacheName;

    @Resource
    private PromotionRedirectUrlMessageConvert convert;

    @Resource
    private QueryAllPromotionRedirectUrlService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = PROMOTION_REDIRECT_URL_HASH_KEY, remotePreLoadName = Name)
    private Cache<Long, CpnPromotionidredirecturlDO> cache;


    @Override
    public ConvertMessage<Long, CpnPromotionidredirecturlDO> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Long, CpnPromotionidredirecturlDO> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, CpnPromotionidredirecturlDO> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, CpnPromotionidredirecturlDO> getLoader() {
        return null;
    }
}
