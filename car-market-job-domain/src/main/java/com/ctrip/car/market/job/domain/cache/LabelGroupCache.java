package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnLabelGroupDO;
import com.ctrip.car.market.job.domain.service.QueryAllLabelGroupService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.LABEL_GROUP_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.LabelGroupCacheName;

@Component(LabelGroupCache.Name)
@CreateCacheArea(area = "public")
public class LabelGroupCache extends BasicCacheAbstract<Long, CpnLabelGroupDO> {

    public static final String Name = LabelGroupCacheName;

    @Resource
    private QueryAllLabelGroupService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = LABEL_GROUP_HASH_KEY, remotePreLoadName = Name)
    private Cache<Long, CpnLabelGroupDO> cache;

    @Override
    public ConvertMessage<Long, CpnLabelGroupDO> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<Long, CpnLabelGroupDO> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, CpnLabelGroupDO> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, CpnLabelGroupDO> getLoader() {
        return null;
    }
}
