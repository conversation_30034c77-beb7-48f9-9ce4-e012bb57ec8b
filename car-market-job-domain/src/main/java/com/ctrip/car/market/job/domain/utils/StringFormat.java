package com.ctrip.car.market.job.domain.utils;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @Description:
 * @create 2025/7/16 21:11
 */
public final class StringFormat {
    private StringFormat() {}

    public static String format(String pattern, Object... arguments) {
        for (int i = 0; i < arguments.length; i++) {
            if(arguments[i] == null) {
                arguments[i] = "";
            }
            else {
                arguments[i] = arguments[i].toString();
            }

        }
        pattern= pattern.replace("'","''");
        return MessageFormat.format(pattern, arguments);
    }


}