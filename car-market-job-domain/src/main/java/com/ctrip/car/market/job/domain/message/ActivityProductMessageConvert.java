package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.ActProductDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.ActProductInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.ActProductids;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class ActivityProductMessageConvert extends AbstractConvertMessage<Long, List<ActProductids>, List<ActProductDO>> implements ConvertMessage<Long, List<ActProductDO>> {

    @Resource
    private ActivityService service;

    @Resource
    private ActProductInfoMapper mapper;

    public ActivityProductMessageConvert() {
        super(TabelEnum.ActivityProduct);
    }

    @Override
    public Map<Long, List<ActProductDO>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Long getKey(String ids) {
        return Long.valueOf(ids);
    }

    @Override
    public List<ActProductids> getData(Long id) throws Exception {
        List<ActProductids> data = service.queryActivityProduct(id);
        return CollectionUtils.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<ActProductDO> mapper(List<ActProductids> value) {
        return mapper.to(value);
    }
}
