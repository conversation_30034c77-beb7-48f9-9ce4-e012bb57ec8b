package com.ctrip.car.market.job.domain.constant;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class CommonConstant {
    public static final String APP_ID = "100043032";
    public static final Integer APP_ID_INT = 100043032;
    public static final Integer SEO_SHARK_APP_ID_INT = 100014699;

    //支付宝小程序消息发送记录key  car.market.alipay.message.send.{uid}.{templateId}
    public static final String ALIPAY_MESSAGE_FORMAT = "car.market.alipay.message.send.%s.%s";

    //待支付、取消订单key car.market.alipay.message.send.{uid}
    public static final String ALIPAY_MESSAGE_PAY_CANCEL_FORMAT = "car.market.alipay.message.send.%s";

    //活动发送记录
    public static final String ALIPAY_ACTIVITY_SEND = "car.market.alipay.message.activity.send";

    public static final Integer ISD = 35;

    public static final Integer OSD = 34;

    public static final String ISD_ALIPAY_SOURCE_FROM = "isd_c_alipay";

    public static final String OSD_ALIPAY_SOURCE_FROM = "osd_c_ali";

    public static final ExecutorService couponExecutorService = Executors.newFixedThreadPool(10);

    public static final ExecutorService actExecutorService = Executors.newFixedThreadPool(10);
}
