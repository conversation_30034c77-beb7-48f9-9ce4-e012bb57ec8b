package com.ctrip.car.market.job.domain.service.seo;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotVendorMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotVendor;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllHotVendorService implements CachePreLoader<String, List<SeoHotVendorDO>>, CacheLoader<String, List<SeoHotVendorDO>> {

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private SeoHotVendorMapper mapper;

    private Map<String, List<SeoHotVendorDO>> getAllHotVendor() throws Exception {
        List<SeoHotVendor> data = marketDBService.queryAllHotVendor();
        return data.stream().filter(l -> StringUtils.isNotEmpty(l.getVendorId())).map(mapper::to).collect(Collectors.groupingBy(l -> l.getVendorId().toLowerCase()));
    }

    @Override
    public List<SeoHotVendorDO> load(String area, String key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<String, List<SeoHotVendorDO>> loadAll(String area, Set<String> keys) throws Throwable {
        return getAllHotVendor();
    }

    @Override
    public Map<String, List<SeoHotVendorDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllHotVendor();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
