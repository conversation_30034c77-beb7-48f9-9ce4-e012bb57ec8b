package com.ctrip.car.market.job.domain.dto;

/**
 * @program: carsemservice
 * @description:
 * @author: yliu
 * @create: 2022-04-16 12:12
 */
public class ChannelDTO {
    private Long primaryChannelId;

    /**
     * 第一级渠道名称
     */
    private String primaryChannelName;

    /**
     * 第二级渠道id
     */
    private Long secondaryChannelId;

    /**
     * 第二级渠道名称
     */
    private String secondaryChannelName;

    /**
     * 第三级渠道id
     */
    private Long tertiaryChannelId;

    /**
     * 第三级渠道名称
     */
    private String tertiaryChannelName;

    /**
     * 是否有效
     */
    private Boolean valid;

    private String operatorId;

    private String operator;


    public Long getPrimaryChannelId() {
        return primaryChannelId;
    }

    public void setPrimaryChannelId(Long primaryChannelId) {
        this.primaryChannelId = primaryChannelId;
    }

    public String getPrimaryChannelName() {
        return primaryChannelName;
    }

    public void setPrimaryChannelName(String primaryChannelName) {
        this.primaryChannelName = primaryChannelName;
    }

    public Long getSecondaryChannelId() {
        return secondaryChannelId;
    }

    public void setSecondaryChannelId(Long secondaryChannelId) {
        this.secondaryChannelId = secondaryChannelId;
    }

    public String getSecondaryChannelName() {
        return secondaryChannelName;
    }

    public void setSecondaryChannelName(String secondaryChannelName) {
        this.secondaryChannelName = secondaryChannelName;
    }

    public Long getTertiaryChannelId() {
        return tertiaryChannelId;
    }

    public void setTertiaryChannelId(Long tertiaryChannelId) {
        this.tertiaryChannelId = tertiaryChannelId;
    }

    public String getTertiaryChannelName() {
        return tertiaryChannelName;
    }

    public void setTertiaryChannelName(String tertiaryChannelName) {
        this.tertiaryChannelName = tertiaryChannelName;
    }

    public Boolean getValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
