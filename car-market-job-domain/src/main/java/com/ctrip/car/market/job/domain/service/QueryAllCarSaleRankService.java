package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CarSaleRankDO;
import com.ctrip.car.market.job.domain.mapper.CarSaleRankMapper;
import com.ctrip.car.market.job.repository.entity.CarSaleRank;
import com.ctrip.car.market.job.repository.service.BiDBService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllCarSaleRankService implements CachePreLoader<Integer, List<CarSaleRankDO>>, CacheLoader<Integer, List<CarSaleRankDO>> {

    @Resource
    private BiDBService service;

    @Resource
    private CarSaleRankMapper mapper;

    private Map<Integer, List<CarSaleRankDO>> getAllSaleRank() throws Exception {
        List<CarSaleRank> data = service.queryAllSaleRank();
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(CarSaleRankDO::getCityId));
    }

    @Override
    public List<CarSaleRankDO> load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, List<CarSaleRankDO>> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getAllSaleRank();
    }

    @Override
    public Map<Integer, List<CarSaleRankDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllSaleRank();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
