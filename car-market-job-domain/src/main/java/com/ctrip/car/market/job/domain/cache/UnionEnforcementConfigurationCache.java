package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.UnionEnforcementconfigurationDO;
import com.ctrip.car.market.job.domain.message.UnionEnforcementConfigurationMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllUnionEnforcementConfigurationService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.UNION_ENFORCEMENT_CONFIGURATION_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.UnionEnforcementConfigurationCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(UnionEnforcementConfigurationCache.Name)
@CreateCacheArea(area = "public")
public class UnionEnforcementConfigurationCache extends BasicCacheAbstract<Long, UnionEnforcementconfigurationDO> {

    public static final String Name = UnionEnforcementConfigurationCacheName;

    @Resource
    private UnionEnforcementConfigurationMessageConvert convert;

    @Resource
    private QueryAllUnionEnforcementConfigurationService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = UNION_ENFORCEMENT_CONFIGURATION_HASH_KEY, remotePreLoadName = Name)
    private Cache<Long, UnionEnforcementconfigurationDO> cache;


    @Override
    public ConvertMessage<Long, UnionEnforcementconfigurationDO> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Long, UnionEnforcementconfigurationDO> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, UnionEnforcementconfigurationDO> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, UnionEnforcementconfigurationDO> getLoader() {
        return null;
    }
}
