package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.ChannelNumberDO;
import com.ctrip.car.market.job.repository.entity.ChannelNumber;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ChannelNumberMapper {

    ChannelNumberDO to(ChannelNumber value);

    List<ChannelNumberDO> to(List<ChannelNumber> value);
}
