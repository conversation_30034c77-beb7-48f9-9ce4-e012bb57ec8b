package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.jetcache.QScheduleUtil;
import qunar.tc.qmq.Message;
import qunar.tc.schedule.Parameter;

import java.util.Date;
import java.util.Map;
import java.util.Set;

public abstract class BasicCacheAbstract<K, V> implements ConvertMessage<K, V>, CachePreLoader<K, V>, CacheLoader<K, V> {

    /**
     * 增量更新
     *
     * @return 消息处理Convert
     */
    public abstract ConvertMessage<K, V> getConvertMessage();

    /**
     * 预加载
     *
     * @return 预加载Loader
     */
    public abstract CachePreLoader<K, V> getPreLoader();

    /**
     * 缓存获取
     *
     * @return CarCache
     */
    protected abstract Cache<K, V> getCache();

    public Cache<K,V> getCache(String area){
        return getCache().getCache(area);
    }


    /**
     * @return Loader
     */
    public abstract CacheLoader<K, V> getLoader();

    @Override
    public Map<K, V> convertMessage(String area, Message message) {
        return getConvertMessage().convertMessage(area, message);
    }

    @Override
    public Map<K, V> preLoad(String area, Date lastUpdateTime) {
        return getPreLoader().preLoad(area,lastUpdateTime);
    }

    @Override
    public Map<K, V> loadAll(String area, Set<K> keys) throws Throwable {
        return getLoader().loadAll(area, keys);
    }

    @Override
    public V load(String area, K key) throws Throwable {
        return getLoader().load(area,key);
    }

    public void qSchedule(Parameter parameter) {
        QScheduleUtil.run(parameter,getCache());
    }
}
