package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.domain.message.ActivityTempMappingMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllActivityTempMappingService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.ACT_INFO_TEMP_MAPPING_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.IsdActivity_TempMappingCacheName;

@Component(ActivityTempMappingCache.Name)
@CreateCacheArea(area = "public")
public class ActivityTempMappingCache extends BasicCacheAbstract<Long, List<Long>> {

    public static final String Name = IsdActivity_TempMappingCacheName;

    @Resource
    private QueryAllActivityTempMappingService service;

    @Resource
    private ActivityTempMappingMessageConvert convert;

    @IncUpdateConsumerByQmq(subject = "car.market.activity.data.change", convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = ACT_INFO_TEMP_MAPPING_KEY, remotePreLoadName = Name)
    private Cache<Long, List<Long>> cache;

    @Override
    public ConvertMessage<Long, List<Long>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Long, List<Long>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, List<Long>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, List<Long>> getLoader() {
        return null;
    }

}
