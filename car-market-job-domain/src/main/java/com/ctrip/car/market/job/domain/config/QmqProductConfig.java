package com.ctrip.car.market.job.domain.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import qunar.tc.qmq.MessageProducer;
import qunar.tc.qmq.producer.MessageProducerProvider;

@Configuration
public class QmqProductConfig {
    @Bean
    MessageProducer producer() {
        MessageProducerProvider provider = new MessageProducerProvider();
        provider.init();
        return provider;
    }
}