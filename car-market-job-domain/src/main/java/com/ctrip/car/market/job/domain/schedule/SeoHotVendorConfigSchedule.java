package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.config.SeoHotVendorConfig;
import com.ctrip.car.market.job.domain.config.SeoVendorCityPageConfig;
import com.ctrip.car.market.job.domain.dto.VendorCityInfo;
import com.ctrip.car.market.job.domain.service.SeoHotDestinationBusiness;
import com.ctrip.car.market.job.repository.entity.SeoHotVendor;
import com.ctrip.car.market.job.repository.entity.SeoHotVendorCity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.Resource;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class SeoHotVendorConfigSchedule {

    private static final Logger LOGGER = LoggerFactory.getLogger(SeoHotVendorConfigSchedule.class);

    @Resource
    private SeoHotVendorConfig seoHotVendorConfig;

    @Resource
    private SeoHotDestinationBusiness seoHotDestinationBusiness;

    @QSchedule("seo.hot.vendor.config.pull")
    public void schedule() {
        try {
            List<SeoVendorCityPageConfig> seoVendorCityPageConfigList = seoHotVendorConfig.getSeoVendorCityPageConfigList();
            List<SeoHotVendor> seoHotVendorInfoList = seoHotDestinationBusiness.getSeoHotVendorInfoList(false);
            List<SeoHotVendorCity> seoHotVendorCityList = seoHotDestinationBusiness.getSeoHotVendorCityInfoList(false);
            // 将数据库中的供应商记录转换为Map，方便查找
            Map<String, SeoHotVendor> dbVendorMap = seoHotVendorInfoList.stream()
                    .collect(Collectors.toMap(SeoHotVendor::getVendorId, vendor -> vendor));

            // 使用 vendorId 和 cityId 组合作为唯一键
            Map<String, SeoHotVendorCity> dbCityMap = seoHotVendorCityList.stream()
                    .collect(Collectors.toMap(
                            city -> city.getVendorId() + "_" + city.getCityId(),
                            city -> city
                    ));

            // 处理新增和更新
            List<SeoHotVendor> updateList = new ArrayList<>();
            List<SeoHotVendor> insertList = new ArrayList<>();

            List<SeoHotVendorCity> updateCityList = new ArrayList<>();
            List<SeoHotVendorCity> insertCityList = new ArrayList<>();


            for (SeoVendorCityPageConfig config : seoVendorCityPageConfigList) {
                if (StringUtils.isBlank(config.getVendorCode()) || StringUtils.isBlank(config.getVendorName())) {
                    LOGGER.warn("Invalid SeoVendorCityPageConfig: {}", config);
                    continue;
                }
                SeoHotVendor existingVendor = dbVendorMap.get(config.getVendorCode());
                if (existingVendor != null) {
                    // 更新记录
                    updateList.add(seoHotDestinationBusiness.buildSeoHotVendor(config, existingVendor));
                } else {
                    // 新增记录
                    insertList.add(seoHotDestinationBusiness.buildSeoHotVendor(config, new SeoHotVendor()));
                }

                if (CollectionUtils.isNotEmpty(config.getCityIdList())) {
                    config.getCityIdList().stream().map(VendorCityInfo::getCityId).distinct().forEach(x -> {
                        if (x == null) {
                            LOGGER.warn("Invalid cityId in SeoVendorCityPageConfig: {}", config);
                            return;
                        }
                        SeoHotVendorCity existingVendorCity = dbCityMap.get(config.getVendorCode() + "_" + x);
                        if (existingVendorCity != null) {
                            updateCityList.add(seoHotDestinationBusiness.buildSeoHotVendorCity(config, existingVendorCity, x));
                        } else {
                            insertCityList.add(seoHotDestinationBusiness.buildSeoHotVendorCity(config, new SeoHotVendorCity(), x));
                        }
                    });
                }
            }

            // 处理需要设置为失效状态的记录
            if (CollectionUtils.isNotEmpty(seoVendorCityPageConfigList)) {

                Set<String> newVendorCodes = seoVendorCityPageConfigList.stream()
                        .map(SeoVendorCityPageConfig::getVendorCode)
                        .collect(Collectors.toSet());

                Set<String> newCityCodes = seoVendorCityPageConfigList.stream()
                        .flatMap(config -> config.getCityIdList().stream()
                                .map(cityId -> config.getVendorCode() + "_" + cityId.getCityId()))
                        .collect(Collectors.toSet());

                for (SeoHotVendor vendor : seoHotVendorInfoList) {
                    if (!newVendorCodes.contains(vendor.getVendorId())) {
                        // 设置为失效状态
                        vendor.setStatus(1);
                        updateList.add(vendor);
                    }
                }
                for (SeoHotVendorCity city : seoHotVendorCityList) {
                    if (!newCityCodes.contains(city.getVendorId() + "_" + city.getCityId())) {
                        // 设置为失效状态
                        city.setStatus(1);
                        updateCityList.add(city);
                    }
                }
            }

            // 批量更新和插入
            if (CollectionUtils.isNotEmpty(updateList)) {
                seoHotDestinationBusiness.batchUpdateVendor(updateList);
            }

            if (CollectionUtils.isNotEmpty(insertList)) {
                seoHotDestinationBusiness.batchInsertIntoVendor(insertList);
            }

            if (CollectionUtils.isNotEmpty(updateCityList)) {
                seoHotDestinationBusiness.batchUpdateVendorCity(updateCityList);
            }

            if (CollectionUtils.isNotEmpty(insertCityList)) {
                seoHotDestinationBusiness.batchInsertIntoVendorCity(insertCityList);
            }
        } catch (Exception e) {
            LOGGER.error("Error in SeoHotVendorConfigSchedule", e);
        }
    }
}
