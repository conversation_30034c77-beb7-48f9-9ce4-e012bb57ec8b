package com.ctrip.car.market.job.domain.service.seo;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotCityinfoDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotCityinfoMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotCityinfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllSeoHotCityService implements CachePreLoader<Integer, List<SeoHotCityinfoDO>>, CacheLoader<Integer, List<SeoHotCityinfoDO>> {

    @Resource
    private MarketDBService service;

    @Resource
    private SeoHotCityinfoMapper mapper;

    private Map<Integer, List<SeoHotCityinfoDO>> getALlHotCity() throws Exception {
        List<SeoHotCityinfo> data = service.queryAllHotCity();
        return data.stream().filter(l -> l.getCityId() != null).map(mapper::to).collect(Collectors.groupingBy(SeoHotCityinfoDO::getCityId));
    }

    @Override
    public List<SeoHotCityinfoDO> load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, List<SeoHotCityinfoDO>> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getALlHotCity();
    }

    @Override
    public Map<Integer, List<SeoHotCityinfoDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getALlHotCity();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
