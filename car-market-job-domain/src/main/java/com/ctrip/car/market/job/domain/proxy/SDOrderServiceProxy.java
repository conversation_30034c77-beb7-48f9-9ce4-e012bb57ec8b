package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.car.customer.common.service.BaseService;
import com.ctrip.car.market.job.common.CommonResult;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.sdorderservice.soa.autogenerate.GetOrderDetailByOrderIdRequestType;
import com.ctrip.car.sdorderservice.soa.autogenerate.GetOrderDetailByOrderIdResponseType;
import com.ctrip.car.sdorderservice.soa.autogenerate.OrderDetailInfo;
import com.ctrip.car.sdorderservice.soa.autogenerate.SDNewOrderServiceClient;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.springframework.stereotype.Component;

@Component
public class SDOrderServiceProxy extends BaseService {
    private static final ILog log = LogManager.getLogger(SDOrderServiceProxy.class);
    SDNewOrderServiceClient client = SDNewOrderServiceClient.getInstance();

    public CommonResult<OrderDetailInfo> getOrderDetailByOrderId(Long orderId) {
        GetOrderDetailByOrderIdRequestType requestType = new GetOrderDetailByOrderIdRequestType();
        requestType.setOrderID(orderId);
//        requestType.setProId(18);

        GetOrderDetailByOrderIdResponseType responseType = null;
        try {
            responseType = client.getOrderDetailByOrderId(requestType);
        } catch (Exception e) {
            log.warn("SDNewOrderServiceClient::getOrderDetailByOrderId failed", e);
            return CommonResult.failed("RPCException", "SDNewOrderServiceClient::getOrderDetailByOrderId failed");
        } finally {
            log.info("SDNewOrderServiceClient::getOrderDetailByOrderId", String.format("requestType=%s\nresponseType=%s", JsonUtil.toJSONString(requestType), JsonUtil.toJSONString(responseType)));
        }

        if (responseType.getResultCode() == 1 && responseType.getOrderDetailInfo() != null) {
            return CommonResult.success(responseType.getOrderDetailInfo());
        }
        return CommonResult.failed(String.valueOf(responseType.getResultCode()), responseType.getResultMsg());
    }

}