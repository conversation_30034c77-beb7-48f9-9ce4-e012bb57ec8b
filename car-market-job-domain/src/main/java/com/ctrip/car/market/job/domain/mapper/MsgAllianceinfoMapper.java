package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.MsgAllianceinfoDO;
import com.ctrip.car.market.job.repository.entity.MsgAllianceinfo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MsgAllianceinfoMapper {

    MsgAllianceinfoDO to(MsgAllianceinfo value);

    List<MsgAllianceinfoDO> to(List<MsgAllianceinfo> values);
}
