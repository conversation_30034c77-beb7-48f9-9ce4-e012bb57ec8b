package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnRestrictedConditionDO;
import com.ctrip.car.market.job.domain.mapper.RestrictedConditionMapper;
import com.ctrip.car.market.job.repository.entity.CpnRestrictedCondition;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllRestrictedConditionService implements CachePreLoader<Integer, CpnRestrictedConditionDO>, CacheLoader<Integer, CpnRestrictedConditionDO> {

    @Resource
    private RestrictedService service;

    @Resource
    private RestrictedConditionMapper mapper;

    private Map<Integer, CpnRestrictedConditionDO> getAllRestrictedCondition() throws Exception {
        List<CpnRestrictedCondition> data = service.queryAllRestrictedCondition();
        return data.stream().collect(Collectors.toMap(CpnRestrictedCondition::getConditionId, mapper::to));
    }

    @Override
    public CpnRestrictedConditionDO load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, CpnRestrictedConditionDO> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllRestrictedCondition();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<Integer, CpnRestrictedConditionDO> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getAllRestrictedCondition();
    }
}
