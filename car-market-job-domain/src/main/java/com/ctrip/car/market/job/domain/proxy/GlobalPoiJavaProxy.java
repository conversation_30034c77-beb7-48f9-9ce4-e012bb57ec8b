package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.gs.globalpoi.soa.contract.BizCodeEnum;
import com.ctrip.gs.globalpoi.soa.contract.GetPoiIdsByBizCodeRequestType;
import com.ctrip.gs.globalpoi.soa.contract.GetPoiIdsByBizCodeResponseType;
import com.ctrip.gs.globalpoi.soa.contract.GlobalPoiJavaClient;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class GlobalPoiJavaProxy {
    private final ILog log = LogManager.getLogger(GlobalPoiJavaProxy.class);

    private GlobalPoiJavaClient client = GlobalPoiJavaClient.getInstance();

    private static final Integer MAX_NUMBER = 500;

    public Map<String, Long> getPoiIdByPoiCode(List<String> poiCode, BizCodeEnum bizCodeEnum) {
        Map<String, Long> result = new HashMap<>();
        try {
            int limit = (poiCode.size() + MAX_NUMBER - 1) / MAX_NUMBER;
            Stream.iterate(0, n -> n + 1).limit(limit).forEach(i -> {
                GetPoiIdsByBizCodeRequestType requestType = new GetPoiIdsByBizCodeRequestType();
                requestType.setBizCode(poiCode.stream().skip((long) i * MAX_NUMBER).limit(MAX_NUMBER).collect(Collectors.toList()));
                requestType.setType(bizCodeEnum);
                GetPoiIdsByBizCodeResponseType poiIdsByBizCode = new GetPoiIdsByBizCodeResponseType();
                try {
                    poiIdsByBizCode = client.getPoiIdsByBizCode(requestType);
                } catch (Exception e) {
                    log.warn("query poiId failed", e);
                }
                if (poiIdsByBizCode != null && !poiIdsByBizCode.getResult().isEmpty()) {
                    result.putAll(poiIdsByBizCode.getResult());
                }
            });
            return result;
        } catch (Exception e) {
            log.warn("query poiId failed", e);
            return result;
        }
    }

    public Long getPoiIdByCode(String poiCode) {
        GetPoiIdsByBizCodeRequestType requestType = new GetPoiIdsByBizCodeRequestType();
        requestType.setType(BizCodeEnum.THREECODE_AIRPORT);
        requestType.setBizCode(Lists.newArrayList(poiCode));
        try {
            GetPoiIdsByBizCodeResponseType poiIdsByBizCode = client.getPoiIdsByBizCode(requestType);
            return poiIdsByBizCode != null && poiIdsByBizCode.getResult() != null ? poiIdsByBizCode.getResult().get(poiCode) : null;
        } catch (Exception e) {
            log.warn("query poiId failed", e);
        }
        return null;
    }

}
