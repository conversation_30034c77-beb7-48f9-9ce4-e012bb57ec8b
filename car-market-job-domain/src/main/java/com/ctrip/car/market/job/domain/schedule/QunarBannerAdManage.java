package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.dto.AdvertisementInfo;
import com.ctrip.car.market.job.domain.dto.CallbackParam;
import com.ctrip.car.market.job.domain.dto.CallbackResult;
import com.ctrip.car.market.job.domain.dto.CommonHttpResponse;
import com.ctrip.car.market.job.domain.proxy.LatTripOpenServiceProxy;
import com.ctrip.car.market.job.domain.utils.FastJsonUtil;
import com.ctrip.car.market.job.domain.utils.HttpTool;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.domain.utils.QConfigUtil;
import com.ctrip.car.market.job.domain.utils.PicUploaderServiceV2;
import com.ctrip.car.market.job.repository.entity.AdQunarBanner;
import com.ctrip.car.market.job.repository.entity.MktapiToken;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.market.admanage.LatMktAPIAdManagementService;
import com.ctrip.market.admanage.dto.*;
import com.ctrip.market.admanage.dto.constant.AdSiteEnum;
import com.ctrip.market.admanage.dto.trip.*;
import com.ctrip.market.lat.open.soa.Element;
import com.ctrip.market.lat.open.soa.ImpInfo;
import com.ctrip.market.lat.open.soa.Style;
import com.ctrip.market.mktapi.common.entity.constant.SourceEnum;
import com.ctrip.market.mktapi.common.entity.constant.SysStatusEnum;
import com.ctrip.market.mktapi.common.entity.constant.OptStatusEnum;
import com.ctrip.market.mktapi.common.entity.constant.OperatorEnum;
import com.ctrip.market.mktapi.common.entity.dto.Filter;
import com.ctrip.market.mktapi.common.entity.dto.LatApiResponse;
import com.google.common.collect.Lists;
import org.apache.commons.beanutils.BeanMap;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
public class QunarBannerAdManage {

    public static final ThreadLocal<Map<String, String>> logTagMap = new ThreadLocal<>();

    private static final ILog log = LogManager.getLogger("QunarBannerAdManage");

    @Resource
    private MarketDBService marketDBService;

    @Autowired
    private LatTripOpenServiceProxy latTripOpenServiceProxy;

    @DubboReference(init = true)
    private LatMktAPIAdManagementService latMktAPIAdManagementService;

    /**
     * @param frameIndex 第几帧
     * @param impId      impId
     * @param styleId    styleId
     * @param qunarUrl   去哪儿接口地址
     * @param type       类型 1: banner 2: 强推
     * @throws Exception
     */
    public void service(Integer frameIndex, String impId, Long styleId, String qunarUrl, Integer type) throws Exception {
        logTagMap.remove();
        logTagMap.set(new HashMap<>());
        logTagMap.get().put("frameIndex", frameIndex + "");
        logTagMap.get().put("impId", impId);
        logTagMap.get().put("styleId", styleId + "");
        logTagMap.get().put("timestamp", System.currentTimeMillis() + "");

        if (Objects.isNull(frameIndex) || StringUtils.isBlank(impId)) {
            return;
        }
        Long clientId = Long.parseLong(QConfigUtil.getByFileAndKey("qunarbannerad.properties", "CLIENT_ID"));
        if (clientId <= 0) {
            return;
        }

        // 调用Qunar物料接口并解析返回值
        AdvertisementInfo qunarAdvertisementInfo = null;
        try {
            qunarAdvertisementInfo = queryQunarMaterial(qunarUrl);
        } catch (Exception e) {
            // TODO 告警
            return;
        }
        // 查询营销数据库中历史数据快照作比较
        List<AdQunarBanner> adQunarBannerList = marketDBService.queryValidByFrameAndImpId(frameIndex, impId);
        if (CollectionUtils.isEmpty(adQunarBannerList)) {
            // 1. 营销数据库中无数据，但去哪接口返回了物料，说明这个广告是要新增的
            logTagMap.get().put("business", "ADD");
            if (Objects.isNull(qunarAdvertisementInfo) || StringUtils.isBlank(qunarAdvertisementInfo.getAdId()) || StringUtils.isBlank(qunarAdvertisementInfo.getFilghtId())) {
                return;
            }
            Long campaignId = this.addCampaign(qunarAdvertisementInfo, frameIndex, impId, styleId, type); //纵横创建广告
            logTagMap.get().put("newCampaignId", campaignId + "");
            if (campaignId > 0L) {
                AdQunarBanner insertEntity = new AdQunarBanner();
                insertEntity.setFrame(frameIndex);
                insertEntity.setImpId(impId);
                insertEntity.setCampaignId(campaignId);
                insertEntity.setActiveStatus(true);
                insertEntity.setImageURL(qunarAdvertisementInfo.getImageURL());
                insertEntity.setClickURL(qunarAdvertisementInfo.getClickURL());
                insertEntity.setMonitorURL(qunarAdvertisementInfo.getMonitorURL());
                insertEntity.setStartDate(qunarAdvertisementInfo.getStartDate());
                insertEntity.setEndDate(qunarAdvertisementInfo.getEndDate());
                insertEntity.setSize(qunarAdvertisementInfo.getSize());
                insertEntity.setFilghtId(qunarAdvertisementInfo.getFilghtId());
                insertEntity.setAdId(qunarAdvertisementInfo.getAdId());
                insertEntity.setCtripImageURL(qunarAdvertisementInfo.getCtripImageURL());
                marketDBService.insertAdQunar(insertEntity);
                // 回调Qunar
                CallbackParam callbackParam = new CallbackParam();
                callbackParam.setFilghtId(Long.parseLong(qunarAdvertisementInfo.getFilghtId()));
                callbackParam.setAdId(Long.parseLong(qunarAdvertisementInfo.getAdId()));
                callbackParam.setCampaignId(campaignId);
                CallbackResult callbackResult = this.callbackQunarMaterial(callbackParam);
                if (callbackResult == null || !callbackResult.getRet()) {
                    log.warn("QunarBannerAdManage.callbackQunarMaterial.callbackFail", FastJsonUtil.serialize(callbackParam), logTagMap.get());
                }
            }
        } else {
            // 2. 创建过广告但去哪儿物料返回为空，说明去哪儿已将广告下线
            if (Objects.isNull(qunarAdvertisementInfo) || StringUtils.isBlank(qunarAdvertisementInfo.getAdId()) || StringUtils.isBlank(qunarAdvertisementInfo.getFilghtId())) {
                logTagMap.get().put("business", "DELETE");
                AdQunarBanner adQunarBanner = adQunarBannerList.get(0);
                Long campaignId = adQunarBanner.getCampaignId();
                Boolean deleteCampaign = this.deleteCampaign(campaignId);
                if (deleteCampaign) {
                    marketDBService.updateStatueByFrameAndImpId(false, frameIndex, impId); //纵横删除后把营销数据库中旧的数据置为无效
                }
            } else {
                // 3. 创建过广告，且去哪儿返回了物料，则要判断物料信息有没有改变
                AdQunarBanner adQunarBanner = adQunarBannerList.get(0);
                logTagMap.get().put("oldCampaignId", adQunarBanner.getCampaignId() + "");
                AdvertisementInfo advertisementInfo = new AdvertisementInfo();
                advertisementInfo.setImageURL(adQunarBanner.getImageURL());
                advertisementInfo.setClickURL(adQunarBanner.getClickURL());
                advertisementInfo.setMonitorURL(adQunarBanner.getMonitorURL());
                advertisementInfo.setStartDate(adQunarBanner.getStartDate());
                advertisementInfo.setEndDate(adQunarBanner.getEndDate());
                advertisementInfo.setSize(adQunarBanner.getSize());
                advertisementInfo.setFilghtId(adQunarBanner.getFilghtId());
                advertisementInfo.setAdId(adQunarBanner.getAdId());
                if (advertisementInfo.isEquals(qunarAdvertisementInfo)) {
                    // 3.1 物料信息没有改变，则不做什么操作
                    return;
                } else {
                    // 3.2 物料信息变了，则要根据 adId 判断去哪儿是改了物料信息、还是下线了旧的广告，上线了新的
                    if (Objects.equals(advertisementInfo.getAdId(), qunarAdvertisementInfo.getAdId())) { //3.2.1 改了物料信息
                        logTagMap.get().put("business", "UPDATE");
                        boolean updateCampaign = this.updateCampaign(adQunarBanner.getCampaignId(), qunarAdvertisementInfo, frameIndex, impId, styleId, type); //纵横更新广告
                        if (updateCampaign) {
                            AdQunarBanner updateEntity = new AdQunarBanner();
                            updateEntity.setId(adQunarBanner.getId());
                            updateEntity.setFrame(frameIndex);
                            updateEntity.setImpId(impId);
                            updateEntity.setCampaignId(adQunarBanner.getCampaignId());
                            updateEntity.setActiveStatus(true);
                            updateEntity.setImageURL(qunarAdvertisementInfo.getImageURL());
                            updateEntity.setClickURL(qunarAdvertisementInfo.getClickURL());
                            updateEntity.setMonitorURL(qunarAdvertisementInfo.getMonitorURL());
                            updateEntity.setStartDate(qunarAdvertisementInfo.getStartDate());
                            updateEntity.setEndDate(qunarAdvertisementInfo.getEndDate());
                            updateEntity.setSize(qunarAdvertisementInfo.getSize());
                            updateEntity.setFilghtId(qunarAdvertisementInfo.getFilghtId());
                            updateEntity.setAdId(qunarAdvertisementInfo.getAdId());
                            updateEntity.setCtripImageURL(qunarAdvertisementInfo.getCtripImageURL());
                            marketDBService.updateAdQunar(updateEntity);
                        }
                    } else { //3.2.2 下线了旧的广告，上线了新的
                        logTagMap.get().put("business", "DELETE ADD");
                        // 先判断新的广告过期时候是否正确
                        DateFormat datetimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        if (datetimeFormat.parse(qunarAdvertisementInfo.getEndDate()).before(new Date())) {
                            return;
                        }
                        Boolean deleteCampaign = this.deleteCampaign(adQunarBanner.getCampaignId()); //纵横新增广告前先删除之前的
                        if (Boolean.TRUE.equals(deleteCampaign)) {
                            marketDBService.updateStatueByFrameAndImpId(false, frameIndex, impId); //插入新的之前先把旧的置为无效
                            Long campaignId = this.addCampaign(qunarAdvertisementInfo, frameIndex, impId, styleId, type); //纵横新增广告
                            logTagMap.get().put("newCampaignId", campaignId + "");
                            if (campaignId > 0) {
                                AdQunarBanner insertEntity = new AdQunarBanner();
                                insertEntity.setFrame(frameIndex);
                                insertEntity.setImpId(impId);
                                insertEntity.setCampaignId(campaignId);
                                insertEntity.setActiveStatus(true);
                                insertEntity.setImageURL(qunarAdvertisementInfo.getImageURL());
                                insertEntity.setClickURL(qunarAdvertisementInfo.getClickURL());
                                insertEntity.setMonitorURL(qunarAdvertisementInfo.getMonitorURL());
                                insertEntity.setStartDate(qunarAdvertisementInfo.getStartDate());
                                insertEntity.setEndDate(qunarAdvertisementInfo.getEndDate());
                                insertEntity.setSize(qunarAdvertisementInfo.getSize());
                                insertEntity.setFilghtId(qunarAdvertisementInfo.getFilghtId());
                                insertEntity.setAdId(qunarAdvertisementInfo.getAdId());
                                insertEntity.setCtripImageURL(qunarAdvertisementInfo.getCtripImageURL());
                                marketDBService.insertAdQunar(insertEntity);
                                // 回调Qunar
                                CallbackParam callbackParam = new CallbackParam();
                                callbackParam.setFilghtId(Long.parseLong(qunarAdvertisementInfo.getFilghtId()));
                                callbackParam.setAdId(Long.parseLong(qunarAdvertisementInfo.getAdId()));
                                callbackParam.setCampaignId(campaignId);
                                CallbackResult callbackResult = this.callbackQunarMaterial(callbackParam);
                                if (callbackResult == null || !callbackResult.getRet()) {
                                    log.warn("QunarBannerAdManage.callbackQunarMaterial.callbackFail", FastJsonUtil.serialize(callbackParam), logTagMap.get());
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private AdvertisementInfo queryQunarMaterial(String qunarUrl) throws Exception {
        CommonHttpResponse response = null;
        try {
            response = HttpTool.doPostWithResponse(qunarUrl, null, "UTF-8");
        } catch (Exception e) {
            log.warn("QunarBannerAdManage.queryQunarMaterial", e, logTagMap.get());
        }
        log.info("QunarBannerAdManage.queryQunarMaterial.response", JsonUtil.toJSONString(response), logTagMap.get());
        if (response == null || response.getCode() != 200) {
            throw new Exception("third http interface is down");
        }
        String responseContent = response.getResponseEntity();
        if (StringUtils.isBlank(responseContent)) return null;
        String[] kvSplits = responseContent.split("\n");
        Map<String, String> map = new HashMap<>();
        for (String kvSplit : kvSplits) {
            for (int i = 0; i < kvSplit.length(); i++) {
                if (kvSplit.charAt(i) == '=') {
                    map.put(kvSplit.substring(0, i), kvSplit.substring(i + 1));
                    break;
                }
            }
        }
        AdvertisementInfo qunarAdvertisementInfo = new AdvertisementInfo();
        BeanMap beanMap = new BeanMap(qunarAdvertisementInfo);
        beanMap.putAll(map);
        return qunarAdvertisementInfo;
    }

    private CallbackResult callbackQunarMaterial(CallbackParam param) throws Exception {
        String callbackQunarUrl = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "CALLBACK_QUNAR_URL");
        CommonHttpResponse response = null;
        try {
            response = HttpTool.doPostWithResponse(callbackQunarUrl, param, "UTF-8");
        } catch (Exception e) {
            log.warn("QunarBannerAdManage.callbackQunarMaterial", e, logTagMap.get());
        }
        if (response == null || response.getCode() != 200) {
            throw new Exception("third http interface is down");
        }
        return FastJsonUtil.DeserializeToObject(response.getResponseEntity(), CallbackResult.class);
    }

    private MktapiToken getAccessToken() {
        Long clientId = Long.parseLong(QConfigUtil.getByFileAndKey("qunarbannerad.properties", "CLIENT_ID"));
        MktapiToken mktapiToken = new MktapiToken();
        mktapiToken.setClientId(clientId);
        try {
            List<MktapiToken> mktapiTokenList = marketDBService.queryMktApiToken(mktapiToken);
            if (CollectionUtils.isNotEmpty(mktapiTokenList)) {
                return mktapiTokenList.get(0);
            }
        } catch (SQLException e) {
            log.warn("QunarBannerAdManage.getAccessToken", e, logTagMap.get());
        }
        return null;
    }

    private Long addCampaign(AdvertisementInfo qunarAdvertisementInfo, Integer index, String impId, Long styleId, Integer type) throws Exception {
        if (StringUtils.isBlank(qunarAdvertisementInfo.getImageURL()) || StringUtils.isBlank(qunarAdvertisementInfo.getSize()) || StringUtils.isBlank(qunarAdvertisementInfo.getClickURL())) {
            return -1L;
        }
        if (StringUtils.isBlank(qunarAdvertisementInfo.getStartDate()) || StringUtils.isBlank(qunarAdvertisementInfo.getEndDate())) {
            return -1L;
        }
        // 判断是否过期
        DateFormat datetimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (datetimeFormat.parse(qunarAdvertisementInfo.getEndDate()).before(new Date())) {
            return -1L;
        }

        AddCampaignRequest addCampaignRequest = new AddCampaignRequest();
        Campaign campaign = new Campaign();
        addCampaignRequest.setData(campaign);

        DateFormat datetimeSerFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String campaignName = Objects.equals(type, 2) ? "QunarPushAd" : "QunarBannerAd";
        campaign.setCampaignName(campaignName + datetimeSerFormat.format(new Date())); //推广计划名称，同一业务方下须唯一
        campaign.setSources(Collections.singletonList(SourceEnum.TRIP));
        campaign.setPromotedObjectType("WEB_PAGE"); //推广类型 PRODUCT-商品推广 、WEB_PAGE-网页推广
        //campaign.setPromotedObjectId(); //推广目标ID （商品推广必传）

        //campaign.setBillingEvent(); //出价方式
        //campaign.setBidAmount(); //出价
        //campaign.setDailyBudget(); //日限额
        Date startDatetime = datetimeFormat.parse(qunarAdvertisementInfo.getStartDate());
        Date endDatetime = datetimeFormat.parse(qunarAdvertisementInfo.getEndDate());
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        campaign.setBeginDate(dateFormat.format(startDatetime));
        campaign.setEndDate(dateFormat.format(endDatetime));
        campaign.setUseAITarget(false);
        //campaign.setWordPackageIds();
        Targeting targeting = new Targeting();
        TimeLimit timeLimit = new TimeLimit();
        timeLimit.setType(TimeLimitType.BEGIN_END_TIME_PERIOD);
        DateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
        timeLimit.setBeginTime(timeFormat.format(startDatetime));
        timeLimit.setEndTime(timeFormat.format(endDatetime));
        targeting.setTimeLimit(timeLimit);
        campaign.setTargeting(targeting);
        campaign.setSysStatus(SysStatusEnum.NORMAL);
        campaign.setOptStatus(OptStatusEnum.NORMAL);
        //campaign.setAutoAmount();
        //campaign.setRejects();
        //campaign.setMoniterCompanyList();
        //campaign.setMessage();
        campaign.setAdSite(AdSiteEnum.CAR_RENTAL);
        SlotDetail slotDetail = new SlotDetail();
        slotDetail.setSlotId(impId);
        SquenceFrame squenceFrame = new SquenceFrame();
        //squenceFrame.setSequence(1);
        squenceFrame.setFrame(index);
        slotDetail.setSquenceFrames(Collections.singletonList(squenceFrame));
        campaign.setSlotDetails(Collections.singletonList(slotDetail));

        log.info("QunarBannerAdManage.addCampaign.queryImpInfoBy.request", impId, logTagMap.get());
        ImpInfo impInfo = latTripOpenServiceProxy.queryImpInfoBy(impId);
        log.info("QunarBannerAdManage.addCampaign.queryImpInfoBy.response", FastJsonUtil.serialize(impInfo), logTagMap.get());
        if (Objects.isNull(impInfo) || CollectionUtils.isEmpty(impInfo.getStyles())) {
            log.warn("QunarBannerAdManage.addCampaign.queryImpInfoBy", "impInfo or styles is empty(impId: " + impId + ")", logTagMap.get());
            return -1L;
        }
        Style style = impInfo.getStyles().stream().filter(s -> Objects.equals(s.getStyleId(), styleId)).findFirst().orElse(null);
        if (Objects.isNull(style)) {
            return -1L;
        }
        CreativeSaveInfo creativeSaveInfo = new CreativeSaveInfo();
        creativeSaveInfo.setMaterialUrl(qunarAdvertisementInfo.getClickURL());
        CreativeMaterial creativeMaterial = new CreativeMaterial();
        creativeMaterial.setType(MaterialType.IMG);
        // 先将图片下载再上传至携程服务器，再保存到纵横
        byte[] image = HttpTool.downloadFileV2(qunarAdvertisementInfo.getImageURL(), "image/jpeg");
        if (Objects.isNull(image)) {
            log.warn("QunarBannerAdManage.addCampaign.downloadQunarFile", "byte is null", logTagMap.get());
            return -1L;
        }
        log.info("QunarBannerAdManage.addCampaign.downloadQunarFile.response", image.length + "", logTagMap.get());
        long fileSize = image.length / 1024L;
        String newImageUrl = PicUploaderServiceV2.uploadImage(image, "image/jpeg", true);
        if (StringUtils.isBlank(newImageUrl)) {
            log.warn("QunarBannerAdManage.addCampaign.uploadQunarFile", "image url is blank", logTagMap.get());
            return -1L;
        }
        log.info("QunarBannerAdManage.addCampaign.uploadQunarFile.response", newImageUrl, logTagMap.get());
        qunarAdvertisementInfo.setCtripImageURL(newImageUrl);
        Element imgElement = style.getElements().stream().filter(element -> Objects.equals(element.getType(), "IMG")).findFirst().orElse(null);
        creativeMaterial.setValue(newImageUrl);
        creativeMaterial.setIndex(imgElement != null ? imgElement.getSort() : 0);
        String size = qunarAdvertisementInfo.getSize();
        String[] xes = size.split("x");
        creativeMaterial.setWidth(Integer.parseInt(xes[0]));
        creativeMaterial.setHeight(Integer.parseInt(xes[1]));
        creativeMaterial.setSize((int) fileSize);
        creativeSaveInfo.setMaterials(Lists.newArrayList(creativeMaterial));

        if (Objects.equals(type, 2) && StringUtils.isNotEmpty(QConfigUtil.getByFileAndKey("qunarbannerad.properties","CTA_VALUE"))) {
            CreativeMaterial cta = new CreativeMaterial();
            cta.setType(MaterialType.CTA);
            cta.setValue("    ");
            cta.setHeight(80);
            cta.setWidth(380);
            creativeSaveInfo.getMaterials().add(cta);
        }

        creativeSaveInfo.setImpStyleId(style.getStyleId());
        creativeSaveInfo.setNoAdCorner(true);
        campaign.setCreativeSaveInfo(creativeSaveInfo);
        //campaign.setShiftThirdAccountId();

        // 必传参数
        MktapiToken mktapiToken = this.getAccessToken();
        if (Objects.isNull(mktapiToken)) {
            return -1L;
        }
        addCampaignRequest.setBizId(mktapiToken.getBizId().longValue());
        addCampaignRequest.setAccessToken(mktapiToken.getAccessToken());
        addCampaignRequest.setTimestamp(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().replace("-", "");
        addCampaignRequest.setNonce(uuid);
        addCampaignRequest.setTrace_id(uuid);
        log.info("QunarBannerAdManage.addCampaign.invokeAddCampaign.request", FastJsonUtil.serialize(addCampaignRequest), logTagMap.get());
        LatApiResponse latApiResponse = latMktAPIAdManagementService.addCampaign(addCampaignRequest);
        log.info("QunarBannerAdManage.addCampaign.invokeAddCampaign.response", FastJsonUtil.serialize(latApiResponse), logTagMap.get());
        if (latApiResponse != null && latApiResponse.getCode() == 0) {
            Long campaignId = (Long) latApiResponse.getData();
            // 创建完成后查一下状态是否正常
            GetCampaignRequest getCampaignRequest = new GetCampaignRequest();
            Filter filter = new Filter();
            filter.setField("campaignId");
            filter.setOperator(OperatorEnum.EQUALS);
            filter.setValues(Collections.singletonList(campaignId.toString()));
            getCampaignRequest.setFilter(filter);
            getCampaignRequest.setPageSize(1);
            getCampaignRequest.setPage(0);
            getCampaignRequest.setBizId(mktapiToken.getBizId().longValue());
            getCampaignRequest.setAccessToken(mktapiToken.getAccessToken());
            getCampaignRequest.setTimestamp(System.currentTimeMillis());
            String uuid2 = UUID.randomUUID().toString().replace("-", "");
            getCampaignRequest.setNonce(uuid2);
            getCampaignRequest.setTrace_id(uuid2);
            long delayTime = Long.parseLong(QConfigUtil.getByFileAndKey("qunarbannerad.properties", "QUERY_DELAY_TIME"));
            Thread.sleep(delayTime); //创建最终结果是异步更新的，需要等一会再查
            log.info("QunarBannerAdManage.addCampaign.invokeGetCampaign.request", FastJsonUtil.serialize(getCampaignRequest), logTagMap.get());
            LatApiResponse getCampaignResponse = latMktAPIAdManagementService.getCampaign(getCampaignRequest);
            log.info("QunarBannerAdManage.addCampaign.invokeGetCampaign.response", FastJsonUtil.serialize(getCampaignResponse), logTagMap.get());
            if (getCampaignResponse != null && getCampaignResponse.getCode() == 0) {
                List<Campaign> getCampaignList = (List<Campaign>) getCampaignResponse.getData();
                if (CollectionUtils.isNotEmpty(getCampaignList)) {
                    log.info("QunarBannerAdManage.addCampaign.invokeGetCampaign.campaignList", FastJsonUtil.serialize(getCampaignList), logTagMap.get());
                    Campaign getCampaign = getCampaignList.get(0);
                    if (getCampaign.getSysStatus() != SysStatusEnum.EXCEPTION) {
                        return campaignId;
                    }
                }
            }
        }
        return -1L;
    }

    private Boolean updateCampaign(Long campaignId, AdvertisementInfo qunarAdvertisementInfo, Integer index, String impId, Long styleId, Integer type) throws Exception {
        if (StringUtils.isBlank(qunarAdvertisementInfo.getImageURL()) || StringUtils.isBlank(qunarAdvertisementInfo.getSize()) || StringUtils.isBlank(qunarAdvertisementInfo.getClickURL())) {
            return false;
        }
        if (StringUtils.isBlank(qunarAdvertisementInfo.getStartDate()) || StringUtils.isBlank(qunarAdvertisementInfo.getEndDate())) {
            return false;
        }
        // 判断是否过期
        DateFormat datetimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (datetimeFormat.parse(qunarAdvertisementInfo.getEndDate()).before(new Date())) {
            return false;
        }

        UpdateCampaignRequest updateCampaignRequest = new UpdateCampaignRequest();
        Campaign campaign = new Campaign();
        updateCampaignRequest.setData(campaign);

        campaign.setCampaignId(campaignId);
        campaign.setSources(Collections.singletonList(SourceEnum.TRIP));
        campaign.setPromotedObjectType("WEB_PAGE"); //推广类型 PRODUCT-商品推广 、WEB_PAGE-网页推广
        //campaign.setPromotedObjectId(); //推广目标ID （商品推广必传）

        Date startDatetime = datetimeFormat.parse(qunarAdvertisementInfo.getStartDate());
        Date endDatetime = datetimeFormat.parse(qunarAdvertisementInfo.getEndDate());
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        campaign.setBeginDate(dateFormat.format(startDatetime));
        campaign.setEndDate(dateFormat.format(endDatetime));
        campaign.setUseAITarget(false);
        Targeting targeting = new Targeting();
        TimeLimit timeLimit = new TimeLimit();
        timeLimit.setType(TimeLimitType.BEGIN_END_TIME_PERIOD);
        DateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
        timeLimit.setBeginTime(timeFormat.format(startDatetime));
        timeLimit.setEndTime(timeFormat.format(endDatetime));
        targeting.setTimeLimit(timeLimit);
        campaign.setTargeting(targeting);

        log.info("QunarBannerAdManage.updateCampaign.queryImpInfoBy.request", impId, logTagMap.get());
        ImpInfo impInfo = latTripOpenServiceProxy.queryImpInfoBy(impId);
        log.info("QunarBannerAdManage.updateCampaign.queryImpInfoBy.response", FastJsonUtil.serialize(impInfo), logTagMap.get());
        if (Objects.isNull(impInfo) || CollectionUtils.isEmpty(impInfo.getStyles())) {
            log.warn("QunarBannerAdManage.updateCampaign.queryImpInfoBy", "impInfo or styles is empty(impId: " + impId + ")", logTagMap.get());
            return false;
        }
        Style style = impInfo.getStyles().stream().filter(s -> Objects.equals(s.getStyleId(), styleId)).findFirst().orElse(null);
        if (Objects.isNull(style)) {
            return false;
        }
        CreativeSaveInfo creativeSaveInfo = new CreativeSaveInfo();
        creativeSaveInfo.setMaterialUrl(qunarAdvertisementInfo.getClickURL());
        CreativeMaterial creativeMaterial = new CreativeMaterial();
        creativeMaterial.setType(MaterialType.IMG);
        // 先将图片上传至携程服务器，再保存到纵横
        byte[] image = HttpTool.downloadFileV2(qunarAdvertisementInfo.getImageURL(), "image/jpeg");
        if (Objects.isNull(image)) {
            log.warn("QunarBannerAdManage.updateCampaign.downloadQunarFile", "byte is null", logTagMap.get());
            return false;
        }
        log.info("QunarBannerAdManage.updateCampaign.downloadQunarFile.response", image.length + "", logTagMap.get());
        long fileSize = image.length / 1024L;
        String newImageUrl = PicUploaderServiceV2.uploadImage(image, "image/jpeg", true);
        if (StringUtils.isBlank(newImageUrl)) {
            log.warn("QunarBannerAdManage.updateCampaign.uploadQunarFile", "image url is blank", logTagMap.get());
            return false;
        }
        log.info("QunarBannerAdManage.updateCampaign.uploadQunarFile.response", newImageUrl, logTagMap.get());
        qunarAdvertisementInfo.setCtripImageURL(newImageUrl);
        Element imgElement = style.getElements().stream().filter(element -> Objects.equals(element.getType(), "IMG")).findFirst().orElse(null);
        creativeMaterial.setValue(newImageUrl);
        creativeMaterial.setIndex(imgElement != null ? imgElement.getSort() : 0);
        String size = qunarAdvertisementInfo.getSize();
        String[] xes = size.split("x");
        creativeMaterial.setWidth(Integer.parseInt(xes[0]));
        creativeMaterial.setHeight(Integer.parseInt(xes[1]));
        creativeMaterial.setSize((int) fileSize);
        creativeSaveInfo.setMaterials(Lists.newArrayList(creativeMaterial));

        if (Objects.equals(type, 2) && StringUtils.isNotEmpty(QConfigUtil.getByFileAndKey("qunarbannerad.properties","CTA_VALUE"))) {
            CreativeMaterial cta = new CreativeMaterial();
            cta.setType(MaterialType.CTA);
            cta.setValue("    ");
            cta.setHeight(80);
            cta.setWidth(380);
            creativeSaveInfo.getMaterials().add(cta);
        }

        creativeSaveInfo.setImpStyleId(style.getStyleId());
        creativeSaveInfo.setNoAdCorner(true);
        campaign.setCreativeSaveInfo(creativeSaveInfo);

        // 必传参数
        MktapiToken mktapiToken = this.getAccessToken();
        if (Objects.isNull(mktapiToken)) {
            return false;
        }
        updateCampaignRequest.setBizId(mktapiToken.getBizId().longValue());
        updateCampaignRequest.setAccessToken(mktapiToken.getAccessToken());
        updateCampaignRequest.setTimestamp(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().replace("-", "");
        updateCampaignRequest.setNonce(uuid);
        updateCampaignRequest.setTrace_id(uuid);
        log.info("QunarBannerAdManage.updateCampaign.invokeUpdateCampaign.request", FastJsonUtil.serialize(updateCampaignRequest), logTagMap.get());
        LatApiResponse latApiResponse = latMktAPIAdManagementService.updateCampaign(updateCampaignRequest);
        log.info("QunarBannerAdManage.updateCampaign.invokeUpdateCampaign.response", FastJsonUtil.serialize(latApiResponse), logTagMap.get());
        if (latApiResponse != null) {
            return latApiResponse.getCode() == 0;
        }
        return false;
    }

    private Boolean deleteCampaign(Long campaignId) {

        DeleteCampaignRequest deleteCampaignRequest = new DeleteCampaignRequest();

        CampaignId campaignIdDTO = new CampaignId();
        campaignIdDTO.setCampaignId(campaignId);
        deleteCampaignRequest.setData(campaignIdDTO);

        // 必传参数
        MktapiToken mktapiToken = this.getAccessToken();
        if (Objects.isNull(mktapiToken)) {
            return false;
        }
        deleteCampaignRequest.setBizId(mktapiToken.getBizId().longValue());
        deleteCampaignRequest.setAccessToken(mktapiToken.getAccessToken());
        deleteCampaignRequest.setTimestamp(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().replace("-", "");
        deleteCampaignRequest.setNonce(uuid);
        deleteCampaignRequest.setTrace_id(uuid);
        log.info("QunarBannerAdManage.updateCampaign.invokeDeleteCampaign.request", FastJsonUtil.serialize(deleteCampaignRequest), logTagMap.get());
        LatApiResponse latApiResponse = latMktAPIAdManagementService.deleteCampaign(deleteCampaignRequest);
        log.info("QunarBannerAdManage.updateCampaign.invokeDeleteCampaign.response", FastJsonUtil.serialize(latApiResponse), logTagMap.get());
        if (latApiResponse != null) {
            return latApiResponse.getCode() == 0;
        }
        return false;
    }
}
