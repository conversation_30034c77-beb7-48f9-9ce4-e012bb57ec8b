package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.seo.SeoHotInformationDO;
import com.ctrip.car.market.job.repository.entity.SeoHotInformation;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SeoHotInformationMapper {

    SeoHotInformationDO to(SeoHotInformation value);

    List<SeoHotInformationDO> to(List<SeoHotInformation> value);
}
