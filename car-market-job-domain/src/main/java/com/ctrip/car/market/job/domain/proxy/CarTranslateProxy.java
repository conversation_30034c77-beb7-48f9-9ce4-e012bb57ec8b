package com.ctrip.car.market.job.domain.proxy;


import com.ctrip.car.customer.common.service.BaseService;
import com.ctrip.car.market.common.cache.annotations.NCache;
import com.ctrip.car.market.job.domain.config.CarServiceConfig;
import com.ctrip.car.market.job.domain.utils.RedisUtil;
import com.ctrip.car.osd.translate.dto.*;
import com.ctrip.car.osd.translate.methodtype.CarTranslateServiceClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Optional;

import static com.ctrip.car.market.job.common.consts.CacheName.*;

@Component
public class CarTranslateProxy extends BaseService {

    CarTranslateServiceClient client = CarTranslateServiceClient.getInstance();

    @Autowired
    private CarServiceConfig carServiceConfig;


    /**
     * 翻译
     *
     * @param request
     * @return
     * @throws Exception
     */
    public TranslateResponseType translate(TranslateRequestType request) throws Exception {
        return client.translate(request);
    }

    public String getTranslateResultByContentCache(String content, String language) {
        String key = SEOCONENTLANGUAGETMSKEY + "::" + content + "::" + language;
        if (StringUtils.isNotEmpty(RedisUtil.getCache(key))) {
            return RedisUtil.getCache(key);
        } else {
            String result = getTranslateResultByContent(content, language);
            if (StringUtils.isNotBlank(result)) {
                RedisUtil.setCache(key, result, Integer.parseInt(Optional.ofNullable(carServiceConfig.getValueFromConfConfigByKey("SEOCONENTLANGUAGETMSKEY")).orElse("36000")));
            }
            return result;
        }

    }

    public String getTranslateResultByContent(String content, String language) {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        TranslateRequestInfo translateInfo = new TranslateRequestInfo(null, language, null, content);
        TranslateRequestType translateRequestType = new TranslateRequestType();
        translateRequestType.setTranslateType(TranslateType.tms);
        translateRequestType.setBuCode("car_calabi");
        translateRequestType.setParams(Arrays.asList(translateInfo));
        try {
            TranslateResponseType translateResponseType = this.translate(translateRequestType);
            if (translateResponseType == null || CollectionUtils.isEmpty(translateResponseType.getResponseInfo()))
                return null;
            TranslateResponseInfo info = translateResponseType.getResponseInfo().stream().filter(x -> StringUtils.equalsIgnoreCase(content, x.getContent())).findAny().orElse(null);
            if (info == null)
                return null;
            TranslateResultInfo translateResultInfo = info.getResults().stream().filter(x -> StringUtils.equalsIgnoreCase(language, x.getTargetLanguage())).findAny().orElse(null);
            if (translateResultInfo == null)
                return null;
            return translateResultInfo.getResult();
        } catch (Exception e) {
            log.error("getTranslateResultByContent", e);
        }
        return null;
    }

    public String getTranslateResultByKeyCache(String key, String language) {
        String redisKey = SEOKEKLANGUAGETMSKEY + "::" + key + "::" + language;
        if (StringUtils.isNotEmpty(RedisUtil.getCache(redisKey))) {
            return RedisUtil.getCache(redisKey);
        } else {
            String result = getTranslateResultByKey(key, language);
            if (StringUtils.isNotBlank(result)) {
                RedisUtil.setCache(redisKey, result, Integer.parseInt(Optional.ofNullable(carServiceConfig.getValueFromConfConfigByKey("SEOKEKLANGUAGETMSKEY")).orElse("36000")));
            }
            return result;
        }
    }

    public String getTranslateResultByKey(String key, String language) {
        if (StringUtils.isBlank(key)) {
            return "";
        }
        TranslateRequestInfo translateInfo = new TranslateRequestInfo(null, language, key, null);
        TranslateRequestType translateRequestType = new TranslateRequestType();
        translateRequestType.setTranslateType(TranslateType.tms);
        translateRequestType.setBuCode("car_calabi");
        translateRequestType.setParams(Arrays.asList(translateInfo));
        try {
            TranslateResponseType translateResponseType = this.translate(translateRequestType);
            if (translateResponseType == null || CollectionUtils.isEmpty(translateResponseType.getResponseInfo()))
                return null;
            TranslateResponseInfo info = translateResponseType.getResponseInfo().stream().filter(x -> StringUtils.equalsIgnoreCase(key, x.getStandardKey())).findAny().orElse(null);
            if (info == null)
                return null;
            TranslateResultInfo translateResultInfo = info.getResults().stream().filter(x -> StringUtils.equalsIgnoreCase(language, x.getTargetLanguage())).findAny().orElse(null);
            if (translateResultInfo == null)
                return null;
            return translateResultInfo.getResult();
        } catch (Exception e) {
            log.error("getTranslateResultByContent", e);
        }
        return null;
    }
}