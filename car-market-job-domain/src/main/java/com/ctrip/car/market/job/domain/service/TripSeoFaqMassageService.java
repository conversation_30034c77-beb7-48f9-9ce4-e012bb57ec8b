package com.ctrip.car.market.job.domain.service;


import com.ctrip.car.maiar.mq.MaiarMessage;
import com.ctrip.car.market.job.domain.config.CarServiceConfig;
import com.ctrip.car.market.job.domain.dto.FaqMassageDTO;
import com.ctrip.car.market.job.domain.dto.FaqVendorDTO;
import com.ctrip.car.market.job.domain.dto.GroupTranslateDTO;
import com.ctrip.car.market.job.domain.dto.TripSeoFaqDTO;
import com.ctrip.car.market.job.domain.proxy.CarTranslateProxy;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.car.market.job.repository.dao.carseodb.SeoFaqDao;
import com.ctrip.car.market.job.repository.entity.carseodb.SeoFaq;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.platform.dal.dao.DalHints;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageProducer;
import qunar.tc.qmq.MessageSendStateListener;
import qunar.tc.qschedule.config.QSchedule;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class TripSeoFaqMassageService {

    private final ILog log = LogManager.getLogger(TripSeoFaqMassageService.class);

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private SeoFaqDao seoFaqDao;

    @Autowired
    private CarTranslateProxy carTranslateProxy;

    @Autowired
    private CarServiceConfig carServiceConfig;



    public boolean execute() {
        List<String> languages = Arrays.asList("CN", "EN", "KR", "JY", "ES", "IT", "DE", "FR", "TH", "HK");
        try {
            int totalSize = seoFaqDao.count(new DalHints());
            int pageSize = 100;
            if (StringUtils.isNotBlank(carServiceConfig.getValueFromConfConfigByKey("sendFaqMassagePageSize"))) {
                pageSize = Integer.parseInt(carServiceConfig.getValueFromConfConfigByKey("sendFaqMassagePageSize"));
            }
            if (StringUtils.isNotEmpty(carServiceConfig.getValueFromConfConfigByKey("tripSeoLanguages"))) {
                languages = Arrays.asList(carServiceConfig.getValueFromConfConfigByKey("tripSeoLanguages").split(","));
            }
            int pageIndex = totalSize / pageSize + 1;
            for (int i = 1; i <= pageIndex; i++) {
                if ("break".equalsIgnoreCase(carServiceConfig.getValueFromConfConfigByKey("executeBreak"))) break;
                List<SeoFaq> seoFaqs = seoFaqDao.queryAllByPage(i, pageSize, new DalHints());
                for (SeoFaq item : seoFaqs) {
                    if (!item.getActive()) continue;
                    if ("break".equalsIgnoreCase(carServiceConfig.getValueFromConfConfigByKey("executeBreak"))) break;
                    TripSeoFaqDTO tripSeoFaqDTO = new TripSeoFaqDTO();
                    List<TripSeoFaqDTO> faqDTOS = new ArrayList<>();
                    FaqMassageDTO faqMassageDTO = new FaqMassageDTO();
                    tripSeoFaqDTO.setCityId(item.getCityId());
                    tripSeoFaqDTO.setHotVehicleGroupName(item.getHotGroupName());
                    tripSeoFaqDTO.setHotVendorName(item.getHotVendorName());
                    List<FaqVendorDTO> vendorList = null;
                    try {
                        if (StringUtils.isNotEmpty(item.getVendorsContent())) {
                            vendorList = JsonUtil.toJson(item.getVendorsContent(), new TypeReference<List<FaqVendorDTO>>() {
                            });
                            int size = vendorList != null ? vendorList.size() : 0;
                        }
                    } catch (Exception e) {
                        log.error("VendorsContentParse", e);
                    }
                    tripSeoFaqDTO.setVendors(vendorList);

                    List<GroupTranslateDTO> groupTranslates = new ArrayList<>();
                    //翻译
                    if (StringUtils.isNotEmpty(item.getGroupEName())) {
                        languages.stream().forEach(l -> {
                            String translateResult = carTranslateProxy.getTranslateResultByContentCache(item.getGroupEName(), l);
                            if (StringUtils.isNotEmpty(translateResult)) {
                                GroupTranslateDTO groupTranslateDTO = new GroupTranslateDTO();
                                groupTranslateDTO.setLanguage(l);
                                groupTranslateDTO.setTranslateName(translateResult);
                                groupTranslates.add(groupTranslateDTO);
                            }
                        });
                        tripSeoFaqDTO.setGroupTranslates(groupTranslates);
                    }
                    faqDTOS.add(tripSeoFaqDTO);
                    faqMassageDTO.setTripSeoFaqs(faqDTOS);
                    sendMassage(faqMassageDTO);
                    Thread.sleep(1000);
                }
            }
        } catch (Exception ex) {
            log.error("execute", ex);
            return false;
        }
        return true;
    }

    private void sendMassage(FaqMassageDTO faqMassage) {
        try {
            String data = JsonUtil.toJSONString(faqMassage);
            if (StringUtils.isNotEmpty(data)) {
                Message message = messageProducer.generateMessage("car.trip.seo.faq.change");
                message.setProperty("type", "faq_phase_car_1");
                message.setProperty("version", System.currentTimeMillis());
                message.setProperty("data", data);
                MaiarMessage.wrapQMQ(message);
                messageProducer.sendMessage(message, new MessageSendStateListener() {
                    @Override
                    public void onSuccess(Message message) {
                        log.info("sendFaqMassage.succ", "message producer success");
                    }

                    @Override
                    public void onFailed(Message message) {
                        log.error("sendFaqMassage.fail", "message producer failed");
                    }
                });
            }
        } catch (Exception e) {
            log.error("faqMassage", e);
        }

    }
}
