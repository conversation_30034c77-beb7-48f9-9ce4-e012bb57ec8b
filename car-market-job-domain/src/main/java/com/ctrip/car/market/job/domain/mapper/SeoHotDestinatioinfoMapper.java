package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.car.market.job.repository.entity.SeoHotDestinatioinfo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SeoHotDestinatioinfoMapper {

    SeoHotDestinatioinfoDO to(SeoHotDestinatioinfo value);

    List<SeoHotDestinatioinfoDO> to(List<SeoHotDestinatioinfo> value);
}
