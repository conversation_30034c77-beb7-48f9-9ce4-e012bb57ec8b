package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.CpnGroupRcConditionDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.GroupRcConditionMapper;
import com.ctrip.car.market.job.repository.entity.CpnGroupRcCondition;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

@Component
public class GroupRcConditionMessageConvert extends AbstractConvertMessage<Integer, CpnGroupRcCondition, CpnGroupRcConditionDO> implements ConvertMessage<Integer, CpnGroupRcConditionDO> {

    @Resource
    private RestrictedService service;

    @Resource
    private GroupRcConditionMapper mapper;

    public GroupRcConditionMessageConvert() {
        super(TabelEnum.GroupRcCondition);
    }

    @Override
    public Map<Integer, CpnGroupRcConditionDO> convertMessage(String area, Message message) {
        try {
            try {
                return super.getMap(message);
            } catch (Exception e) {
                return Collections.emptyMap();
            }
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Integer getKey(String ids) {
        return Integer.valueOf(ids);
    }

    @Override
    public CpnGroupRcCondition getData(Integer id) throws Exception {
        return service.queryGroupRcCondition(id);
    }

    @Override
    public CpnGroupRcConditionDO mapper(CpnGroupRcCondition value) {
        return mapper.to(value);
    }
}
