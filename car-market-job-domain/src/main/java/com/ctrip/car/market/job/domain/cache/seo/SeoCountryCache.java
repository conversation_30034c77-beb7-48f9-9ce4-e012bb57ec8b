package com.ctrip.car.market.job.domain.cache.seo;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotCountryinfoDO;
import com.ctrip.car.market.job.domain.cache.BasicCacheAbstract;
import com.ctrip.car.market.job.domain.service.seo.QueryAllSeoHotCountryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.SEO_COUNTRY_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.SeoCountryCacheName;

@Component(SeoCountryCache.Name)
@CreateCacheArea(area = "public")
public class SeoCountryCache extends BasicCacheAbstract<Integer, List<SeoHotCountryinfoDO>> {

    public static final String Name = SeoCountryCacheName;

    @Resource
    private QueryAllSeoHotCountryService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = SEO_COUNTRY_KEY, remotePreLoadName = Name)
    private Cache<Integer, List<SeoHotCountryinfoDO>> cache;

    @Override
    public ConvertMessage<Integer, List<SeoHotCountryinfoDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<Integer, List<SeoHotCountryinfoDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, List<SeoHotCountryinfoDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, List<SeoHotCountryinfoDO>> getLoader() {
        return null;
    }
}
