package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnRestrictedConditionDO;
import com.ctrip.car.market.job.domain.message.RestrictedConditionMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllRestrictedConditionService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.RESTRICTED_CONDITION_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.RestrictedConditionCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(RestrictedConditionCache.Name)
@CreateCacheArea(area = "public")
public class RestrictedConditionCache extends BasicCacheAbstract<Integer, CpnRestrictedConditionDO>{

    public static final String Name = RestrictedConditionCacheName;

    @Resource
    private RestrictedConditionMessageConvert convert;

    @Resource
    private QueryAllRestrictedConditionService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = RESTRICTED_CONDITION_HASH_KEY, remotePreLoadName = Name)
    private Cache<Integer, CpnRestrictedConditionDO> cache;


    @Override
    public ConvertMessage<Integer, CpnRestrictedConditionDO> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Integer, CpnRestrictedConditionDO> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, CpnRestrictedConditionDO> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, CpnRestrictedConditionDO> getLoader() {
        return null;
    }
}
