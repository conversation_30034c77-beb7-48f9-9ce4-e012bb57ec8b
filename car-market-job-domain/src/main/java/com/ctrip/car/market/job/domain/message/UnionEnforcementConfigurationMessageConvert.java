package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.UnionEnforcementconfigurationDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.UnionEnforcementConfigurationMapper;
import com.ctrip.car.market.job.repository.entity.UnionEnforcementconfiguration;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@Component
public class UnionEnforcementConfigurationMessageConvert extends AbstractConvertMessage<Long, UnionEnforcementconfiguration, UnionEnforcementconfigurationDO> implements ConvertMessage<Long, UnionEnforcementconfigurationDO> {

    @Resource
    private RestrictedService service;

    @Resource
    private UnionEnforcementConfigurationMapper mapper;

    public UnionEnforcementConfigurationMessageConvert() {
        super(TabelEnum.UnionEnforcementConfiguration);
    }

    @Override
    public Map<Long, UnionEnforcementconfigurationDO> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Long getKey(String ids) {
        return Long.valueOf(ids);
    }

    @Override
    public UnionEnforcementconfiguration getData(Long id) throws Exception {
        UnionEnforcementconfiguration obj = service.queryUnionEnforcementConfiguration(id);
        if (obj == null || !Objects.equals(obj.getValidStatu(), 1)) {
            return null;
        }
        return obj;
    }

    @Override
    public UnionEnforcementconfigurationDO mapper(UnionEnforcementconfiguration value) {
        return mapper.to(value);
    }
}
