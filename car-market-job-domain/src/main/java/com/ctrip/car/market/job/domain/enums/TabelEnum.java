package com.ctrip.car.market.job.domain.enums;

public enum TabelEnum {

    GIfItemConfig("gift_itemconfig", "giftNo"),
    GroupRcCondition("cpn_group_rccondition", "groupID"),
    GroupVersion("cpn_group_version", "groupId"),
    MergeCondition("cpn_merge_condition", "ID"),
    ProductRedirectUrl("cpn_productredirecturl", "UnionType"),
    PromotionOrder("cpn_promotion_order", "id"),
    PromotionRedirectUrl("cpn_promotionidredirecturl", "promotionId"),
    RestrictedCondition("cpn_restricted_condition", "conditionId"),
    RestrictedCondition_Promotion("cpn_restricted_condition", "promotionId"),
    RestrictedCondition_Group("cpn_restricted_condition", "groupId"),
    RestrictedPromotion("cpn_restricted_promotion", "promotionId"),
    RestrictedPromotion_Summary("cpn_restricted_promotion", "summaryId"),
    UnionEnforcementConfiguration("union_enforcementconfiguration", "EFID"),
    UnionPromotionLimit("cpn_union_promotion_limit", "promotionId"),
    PromotionSummary("cpn_promotionsummary", "ActivityID"),
    Label("cpn_label","Code"),
    Activity("act_ctripactinfo","Id"),
    Activity_Temp_Mapping("act_ctripactinfo","tempId"),
    Activity_Vendor("act_ctripactinfo","vendorId"),
    ActivityTempId("act_ctriptempinfo","tmpId"),
    ActivityTemp("act_ctriptempinfo","tmpId"),
    ActivityCity("act_cityinfo","activityId"),
    ActivityReturnCity("act_returncityinfo","activityId"),
    ActivityProduct("act_productids","activityId"),
    ActivityVendorSKu("vendor_skuinfo","vendorId"),
    ActivitySku("act_skuinfo","activityId");

    TabelEnum(String tabelName, String primaryKeyName) {
        this.tabelName = tabelName;
        this.primaryKeyName = primaryKeyName;
    }

    private String tabelName;

    private String primaryKeyName;

    public String getTabelName() {
        return tabelName;
    }

    public String getPrimaryKeyName() {
        return primaryKeyName;
    }
}
