package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.config.CarServiceConfig;
import com.ctrip.car.market.job.domain.dto.QunarSearchInfoDTO;
import com.ctrip.car.market.job.domain.dto.SchemeInfoDTO;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.domain.utils.RedisUtil;
import com.ctrip.car.market.job.repository.entity.isddb.Airport;
import com.ctrip.car.market.job.repository.entity.isddb.ExtCity;
import com.ctrip.car.market.job.repository.entity.isddb.IsdLocation;
import com.ctrip.car.market.job.repository.service.IsdDbService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class SendDataToRedisForQunarSearchSchedule {

    private static ILog log = LogManager.getLogger(SendDataToRedisForQunarSearchSchedule.class);

    @Resource
    private IsdDbService isdDbService;

    @Resource
    private CarServiceConfig carServiceConfig;

    private final static String CarQunarSearchCacheAllSemKey = "carcommonjob.qunarsearch.";

    @QSchedule("car.market.common.job.send.data.to.redis.for.qunar.search")
    public void task() {
        log.info("sendDataToRedisForQunarSearch.job start");
        try {
            List<ExtCity> extCities = isdDbService.getCityProvince();
            Map<Integer, ExtCity> cityMap = extCities.stream().collect(Collectors.toMap(ExtCity::getCityId, Function.identity()));
            List<IsdLocation> isdLocations = isdDbService.getAllLocation();
            List<Airport> airports = isdDbService.getAllAirport();
            List<Object> allLocations = new ArrayList<>();
            allLocations.addAll(isdLocations);
            allLocations.addAll(airports);
            Map<Integer, Long> cityWeightMap = allLocations.stream().map(x -> {
                IsdLocation isdLocation = new IsdLocation();
                if (x instanceof Airport) {
                    isdLocation.setCityID(((Airport) x).getCityId());
                } else if (x instanceof IsdLocation) {
                    isdLocation.setCityID(((IsdLocation) x).getCityID());
                }
                return isdLocation;
            }).collect(Collectors.groupingBy(IsdLocation::getCityID, Collectors.counting()));
            int pageSize = 200;
            int size = allLocations.size() / pageSize + 1;
            AtomicInteger productId = new AtomicInteger(1);
            for (int i = 0; i < size; i++) {
                List<QunarSearchInfoDTO> list = new ArrayList<>(pageSize);
                allLocations.stream().skip(i * pageSize).limit(pageSize).forEach(t -> {
                    if (t != null) {
                        QunarSearchInfoDTO qunarSearchInfoDTO = new QunarSearchInfoDTO();
                        if (t instanceof Airport) {
                            Airport airport = (Airport) t;
                            ExtCity airExtCity = cityMap.get(airport.getCityId());
                            if (airExtCity != null) {
                                Long weight = cityWeightMap.get(airport.getCityId());
                                setQunarSearchInfo(qunarSearchInfoDTO, airport.getCityId().toString(), airExtCity.getCityName(), airExtCity.getProvinceName(), airport.getAirportName(), airport.getLatitude() == null ? null : airport.getLatitude().toString(), airport.getLongitude() == null ? null : airport.getLongitude().toString(), productId.toString(), weight.intValue(), airport.getAirportCode());
                            }
                        } else if (t instanceof IsdLocation) {
                            IsdLocation isdLocation = (IsdLocation) t;
                            ExtCity isdLocationExtCity = cityMap.get(isdLocation.getCityID());
                            if (isdLocationExtCity != null) {
                                Long weight = cityWeightMap.get(isdLocation.getCityID());
                                setQunarSearchInfo(qunarSearchInfoDTO, isdLocation.getCityID().toString(), isdLocationExtCity.getCityName(), isdLocationExtCity.getProvinceName(), isdLocation.getLocationName(), isdLocation.getLatitude() == null ? null : isdLocation.getLatitude().toString(), isdLocation.getLongitude() == null ? null : isdLocation.getLongitude().toString(), productId.toString(), weight.intValue(), String.valueOf(isdLocation.getLocationID()));
                            }
                        }
                        if (StringUtils.isNotBlank(qunarSearchInfoDTO.getCity())) {
                            list.add(qunarSearchInfoDTO);
                            productId.getAndIncrement();
                        }
                    }
                });
                String key = CarQunarSearchCacheAllSemKey + (i + 1);
                RedisUtil.setCache(key, JsonUtil.toJSONString(list), 7 * 24 * 60 * 60);
            }
            log.info("sendDataToRedisForQunarSearch.end.count: ", allLocations.size() + "");
        } catch (Exception ex) {
            log.error("SendDataToRedisForQunarSearch sendDataToRedisForQunarSearch error: ", ex);
        }
    }

    private void setQunarSearchInfo(QunarSearchInfoDTO qunarSearchInfoDTO, String cityId, String cityName, String provinceName, String POI, String lat, String lon, String productId, Integer weight, String plId) {
        if (weight == null) {
            weight = 0;
        }
        //URL
        String urlrPreffix = "://react/open?hybridId=isd_car_qp_rn&pageName=Market&initProps=";
        String urlParam = String.format("{\"param\":{\"st\":\"ser\",\"apptype\":\"ISD_Q_APP\",\"fromurl\":\"common\",\"landingto\":\"Home\",\"channelid\":\"14405\",\"pcid\":\"%s\",\"plid\":\"%s\"}}", cityId, plId);
        String suffix = carServiceConfig.getValueFromConfConfigByKey("qunar.search.alias.suffix");
        String aliasCity = cityName + suffix;
        String aliasProvince = provinceName + suffix;
        List<String> alias = Arrays.asList(aliasCity, aliasProvince);
        SchemeInfoDTO schemeInfoDTO = new SchemeInfoDTO();
        schemeInfoDTO.setSchemeUrl(urlrPreffix + urlParam);
        schemeInfoDTO.setEncodedschemeUrl(urlrPreffix + java.net.URLEncoder.encode(urlParam));
//        schemeInfoDTO.setAdr("");
//        schemeInfoDTO.setIos("");
        qunarSearchInfoDTO.setName(POI + suffix);
        qunarSearchInfoDTO.setProductId(productId);
        qunarSearchInfoDTO.setWeight(weight);
        qunarSearchInfoDTO.setCity(cityName);
        qunarSearchInfoDTO.setProvince(provinceName);
        qunarSearchInfoDTO.setCountry(carServiceConfig.getValueFromConfConfigByKey("qunar.search.default.country"));
        qunarSearchInfoDTO.setSchemeInfos(Collections.singletonList(schemeInfoDTO));
        qunarSearchInfoDTO.setLowestVID("60000000;80000000;68999999;99999999");
        qunarSearchInfoDTO.setHighestVID("60099999;80099999;68999999;99999999");
        qunarSearchInfoDTO.setAliasList(alias);
        qunarSearchInfoDTO.setType("4");
        qunarSearchInfoDTO.setPOI(POI);
        qunarSearchInfoDTO.setBaiduLat(lat);
        qunarSearchInfoDTO.setBaiduLon(lon);
    }
}
