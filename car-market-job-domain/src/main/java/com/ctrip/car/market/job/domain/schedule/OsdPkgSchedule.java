package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.config.AllCityQueryConfig;
import com.ctrip.car.market.job.domain.service.ProductManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

@Component
public class OsdPkgSchedule {

    @Autowired
    private AllCityQueryConfig allCityQueryConfig;

    @Autowired
    private ProductManagerService productManagerService;

    @QSchedule("OsdOtherCityQuery")
    public void OsdOtherCityQuery(Parameter parameter) {
        productManagerService.start(allCityQueryConfig.getOsdOtherCityQueryConfig());
    }

    @QSchedule("OsdCoreCityQuery")
    public void OsdCoreCityQuery(Parameter parameter) {
        productManagerService.start(allCityQueryConfig.getOsdCoreCityQueryConfig());
    }

    @QSchedule("OsdNoOrderCityQuery")
    public void OsdNoOrderCityQuery(Parameter parameter) {
        productManagerService.start(allCityQueryConfig.getOsdNoOrderCityQueryConfig());
    }

    @QSchedule("OsdHotMarketCityQueryConfig")
    public void OsdHotMarketCityQueryConfig(Parameter parameter) {
        productManagerService.start(allCityQueryConfig.getOsdHotMarketCityQueryConfig());
    }

    @QSchedule("OsdQunarCoreCityQuery")
    public void OsdQunarCoreCityQueryConfig(Parameter parameter) {
        productManagerService.start(allCityQueryConfig.getOsdQunarCoreCityQueryConfig());
    }

}