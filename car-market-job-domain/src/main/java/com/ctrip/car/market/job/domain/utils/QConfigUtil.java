package com.ctrip.car.market.job.domain.utils;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import qunar.tc.qconfig.client.MapConfig;

import java.util.Map;

public class QConfigUtil {

    private static ILog logger = LogManager.getLogger(QConfigUtil.class);

    public static String getByFileAndKey(String fileName, String key) {
        try {
            MapConfig configMap = MapConfig.get(fileName);
            if (configMap != null) {
                Map<String, String> map = configMap.asMap();
                if (map.size() >= 1 && map.containsKey(key)) {
                    return map.get(key);
                }
            }
            return "";
        } catch (Exception e) {
        }
        return "";
    }

    private final static String configProperties = "conf.properties";
    private final static String seoHotProperties = "seoHotConfig.properties";
    public static Long getConfigLong(String key, String defaultValue) {
        String value = getOrDefault(configProperties, key, defaultValue);
        if (StringUtils.isNotEmpty(value)) {
            return Long.valueOf(value);
        }
        return null;
    }

    public static String getConfigOrDefault(String key, String defaultValue) {
        return getOrDefault(configProperties, key, defaultValue);
    }

    public static String getSeoHotConfigOrDefault(String key, String defaultValue) {
        return getOrDefault(seoHotProperties, key, defaultValue);
    }

    private static String getOrDefault(String fileName, String key, String defaultValue) {
        MapConfig mapConfig = MapConfig.get(fileName);
        if (mapConfig != null) {
            Map<String, String> map = mapConfig.asMap();
            if (MapUtils.isNotEmpty(map)) {
                return map.getOrDefault(key, defaultValue);
            }
        }
        return defaultValue;
    }
}
