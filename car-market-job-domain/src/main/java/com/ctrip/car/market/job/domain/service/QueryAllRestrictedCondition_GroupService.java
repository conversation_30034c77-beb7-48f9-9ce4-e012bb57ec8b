package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnRestrictedConditionDO;
import com.ctrip.car.market.job.domain.mapper.RestrictedConditionMapper;
import com.ctrip.car.market.job.repository.entity.CpnRestrictedCondition;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class QueryAllRestrictedCondition_GroupService implements CachePreLoader<Integer, List<CpnRestrictedConditionDO>>, CacheLoader<Integer, List<CpnRestrictedConditionDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private RestrictedConditionMapper mapper;

    private Map<Integer, List<CpnRestrictedConditionDO>> getAllRestrictedCondition() throws Exception {
        List<CpnRestrictedCondition> data = service.queryAllRestrictedCondition().stream().filter(l -> Objects.nonNull(l.getGroupId()) && l.getGroupId() > 0).collect(Collectors.toList());
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(CpnRestrictedConditionDO::getGroupId));
    }

    @Override
    public List<CpnRestrictedConditionDO> load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, List<CpnRestrictedConditionDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllRestrictedCondition();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<Integer, List<CpnRestrictedConditionDO>> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getAllRestrictedCondition();
    }
}
