package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.CpnMergeConditionDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.MergeConditionMapper;
import com.ctrip.car.market.job.repository.entity.CpnMergeCondition;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class MergeConditionMessageConvert extends AbstractConvertMessage<String, List<CpnMergeCondition>, List<CpnMergeConditionDO>> implements ConvertMessage<String, List<CpnMergeConditionDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private MergeConditionMapper mapper;

    public MergeConditionMessageConvert() {
        super(TabelEnum.MergeCondition);
    }

    @Override
    public Map<String, List<CpnMergeConditionDO>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public String getKey(String ids) {
        return "ALL";
    }

    @Override
    public List<CpnMergeCondition> getData(String id) throws Exception {
        return service.queryAllMergeCondition();
    }

    @Override
    public List<CpnMergeConditionDO> mapper(List<CpnMergeCondition> value) {
        return mapper.to(value);
    }
}
