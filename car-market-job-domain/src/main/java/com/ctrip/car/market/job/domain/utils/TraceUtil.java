package com.ctrip.car.market.job.domain.utils;

import com.ctrip.car.market.job.domain.dto.TraceData;
import com.ctrip.car.osd.notificationcenter.api.CarOsdNotificationCenterClient;
import com.ctrip.car.osd.notificationcenter.dto.TrackerRequestType;
import com.ctrip.framework.foundation.Foundation;

import java.util.Map;

public class TraceUtil {

    private static final CarOsdNotificationCenterClient carOsdNotificationCenterClient = CarOsdNotificationCenterClient.getInstance();

    public static TraceData build(String apiName) {
        return new TraceData(apiName);
    }

    public static void save(Map<String, String> tag) {
        try {
            tag.put("key", "188770");
            tag.put("appId", Foundation.app().getAppId());
            TrackerRequestType requestDefault = new TrackerRequestType();
            requestDefault.setExtendInfo(tag);
            carOsdNotificationCenterClient.trackerAsync(requestDefault);
        } catch (Exception e) {
            CLogUtil.error("TraceUtil->save", e);
        }
    }
}
