package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.CpnUnionPromotionLimitDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.UnionPromotionLimitMapper;
import com.ctrip.car.market.job.repository.entity.CpnUnionPromotionLimit;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class UnionPromotionLimitMessageConvert extends AbstractConvertMessage<Integer, List<CpnUnionPromotionLimit>, List<CpnUnionPromotionLimitDO>> implements ConvertMessage<Integer, List<CpnUnionPromotionLimitDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private UnionPromotionLimitMapper mapper;

    public UnionPromotionLimitMessageConvert() {
        super(TabelEnum.UnionPromotionLimit);
    }

    @Override
    public Map<Integer, List<CpnUnionPromotionLimitDO>> convertMessage(String area, Message message) {

        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Integer getKey(String ids) {
        return Integer.valueOf(ids);
    }

    @Override
    public List<CpnUnionPromotionLimit> getData(Integer id) throws Exception {
        List<CpnUnionPromotionLimit> data = service.queryUnionPromotionLimitByPromotionId(id);
        return CollectionUtils.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<CpnUnionPromotionLimitDO> mapper(List<CpnUnionPromotionLimit> value) {
        return mapper.to(value);
    }
}
