package com.ctrip.car.market.job.domain.cache.seo;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO;
import com.ctrip.car.market.job.domain.cache.BasicCacheAbstract;
import com.ctrip.car.market.job.domain.service.seo.QueryAllHotVendorService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.SEO_VENDOR_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.SeoVendorCacheName;

@Component(SeoVendorCache.Name)
@CreateCacheArea(area = "public")
public class SeoVendorCache extends BasicCacheAbstract<String, List<SeoHotVendorDO>> {

    public static final String Name = SeoVendorCacheName;

    @Resource
    private QueryAllHotVendorService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = SEO_VENDOR_KEY, remotePreLoadName = Name)
    private Cache<String, List<SeoHotVendorDO>> cache;

    @Override
    public ConvertMessage<String, List<SeoHotVendorDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<String, List<SeoHotVendorDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<String, List<SeoHotVendorDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<String, List<SeoHotVendorDO>> getLoader() {
        return null;
    }
}
