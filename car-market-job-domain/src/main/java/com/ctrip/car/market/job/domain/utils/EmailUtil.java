package com.ctrip.car.market.job.domain.utils;

import com.ctrip.framework.foundation.Foundation;
import com.ctrip.soa.platform.basesystem.emailservice.v1.EmailServiceClient;
import com.ctrip.soa.platform.basesystem.emailservice.v1.SendEmailRequest;
import com.ctrip.soa.platform.basesystem.emailservice.v1.SendEmailResponse;

import java.util.Calendar;
import java.util.List;

public class EmailUtil {

    private final static EmailServiceClient serviceClient = EmailServiceClient.getInstance();

    public static boolean sendEmail(String subject, List<String> acceptors, List<String> cc, String body) {
        SendEmailRequest request = new SendEmailRequest();
        request.setAppID(100006312);
        request.setBodyContent(
                String.format("<entry><content><![CDATA[<p>%s</p>]]></content></entry>", body));
        request.setBodyTemplateID(4);
        request.setSubject(subject);
        request.setCc(cc);
        request.setEid("");
        request.setCharset("GB2312");
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 1);
        request.setExpiredTime(cal);
        request.setIsBodyHtml(true);
        request.setOrderID(0L);
        request.setRecipient(acceptors);
        request.setSendCode("12000");
        request.setSender("<EMAIL>");
        request.setSenderName(Foundation.server().getEnv().getName() + "_CarMarketJob");
        request.setSourceID(0);
        request.setUid("ctrip.couponservice");
        SendEmailResponse response;
        try {
            response = serviceClient.sendEmail(request);
            CLogUtil.info("sendEmail", "request", request, "response", response);
            return response != null;
        } catch (Exception e) {
            CLogUtil.error("sendEmail", e);
            return false;
        }
    }
}
