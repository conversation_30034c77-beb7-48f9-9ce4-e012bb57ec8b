package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.utils.QConfigUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Component
public class QunarBannerAdRefreshJob {

    @Resource
    private QunarBannerAdManage qunarBannerAdManage;

    @QSchedule("Qunar_Banner_AD_Car_1_NEW")
    public void execute_car1() throws Exception {
        String impId = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "IMP_ID_RENTALCAR");
        String styleIdS = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "STYLE_ID_RENTALCAR");
        String qunarUrl = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "QUERY_QUANR_MATERIAL_URL_NEW");
        if (StringUtils.isNotBlank(impId) && StringUtils.isNotBlank(styleIdS)) {
            qunarBannerAdManage.service(1, impId, Long.parseLong(styleIdS), String.format(qunarUrl, 1),1);
        }
    }

    @QSchedule("Qunar_Banner_AD_Car_2_NEW")
    public void execute_car2() throws Exception {
        String impId = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "IMP_ID_RENTALCAR");
        String styleIdS = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "STYLE_ID_RENTALCAR");
        String qunarUrl = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "QUERY_QUANR_MATERIAL_URL_NEW");
        if (StringUtils.isNotBlank(impId) && StringUtils.isNotBlank(styleIdS)) {
            qunarBannerAdManage.service(2, impId, Long.parseLong(styleIdS), String.format(qunarUrl, 2),1);
        }
    }

    @QSchedule("Qunar_Banner_AD_Car_3_NEW")
    public void execute_car3() throws Exception {
        String impId = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "IMP_ID_RENTALCAR");
        String styleIdS = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "STYLE_ID_RENTALCAR");
        String qunarUrl = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "QUERY_QUANR_MATERIAL_URL_NEW");
        if (StringUtils.isNotBlank(impId) && StringUtils.isNotBlank(styleIdS)) {
            qunarBannerAdManage.service(3, impId, Long.parseLong(styleIdS), String.format(qunarUrl, 3),1);
        }
    }

    @QSchedule("Qunar_Banner_AD_Dcs_1_NEW")
    public void execute_1() throws Exception {
        String impId = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "IMP_ID_PICKRETURN");
        String styleIdS = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "STYLE_ID_PICKRETURN");
        String qunarUrl = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "QUERY_QUANR_MATERIAL_URL_NEW");
        if (StringUtils.isNotBlank(impId) && StringUtils.isNotBlank(styleIdS)) {
            qunarBannerAdManage.service(1, impId, Long.parseLong(styleIdS), String.format(qunarUrl, 1),1);
        }
    }

    @QSchedule("Qunar_Banner_AD_Dcs_2_NEW")
    public void execute_2() throws Exception {
        String impId = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "IMP_ID_PICKRETURN");
        String styleIdS = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "STYLE_ID_PICKRETURN");
        String qunarUrl = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "QUERY_QUANR_MATERIAL_URL_NEW");
        if (StringUtils.isNotBlank(impId) && StringUtils.isNotBlank(styleIdS)) {
            qunarBannerAdManage.service(2, impId, Long.parseLong(styleIdS), String.format(qunarUrl, 2),1);
        }
    }

    @QSchedule("Qunar_Banner_AD_Dcs_3_NEW")
    public void execute_3() throws Exception {
        String impId = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "IMP_ID_PICKRETURN");
        String styleIdS = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "STYLE_ID_PICKRETURN");
        String qunarUrl = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "QUERY_QUANR_MATERIAL_URL_NEW");
        if (StringUtils.isNotBlank(impId) && StringUtils.isNotBlank(styleIdS)) {
            qunarBannerAdManage.service(3, impId, Long.parseLong(styleIdS), String.format(qunarUrl, 3),1);
        }
    }

    @QSchedule("Qunar_Force_Push_NEW")
    public void qunarPush() throws Exception {
        String impId = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "IMP_ID_QUNAR_PUSH");
        String styleIdS = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "STYLE_ID_QUNAR_PUSH");
        String qunarUrl = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "QUNAR_PUSH_URL");
        if (StringUtils.isNotBlank(impId) && StringUtils.isNotBlank(styleIdS)) {
            qunarBannerAdManage.service(1, impId, Long.parseLong(styleIdS), qunarUrl,2);
        }
    }
}
