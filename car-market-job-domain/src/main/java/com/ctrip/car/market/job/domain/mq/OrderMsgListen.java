package com.ctrip.car.market.job.domain.mq;

import com.ctrip.car.maiar.mq.MaiarMessage;
import com.ctrip.car.maiar.mq.MaiarQMQNode;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageProducer;
import qunar.tc.qmq.MessageSendStateListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.sql.SQLException;

@Component
public class OrderMsgListen {

    private final ILog log = LogManager.getLogger(OrderMsgListen.class);


    @Resource
    private MessageProducer messageProducer;

    @Resource
    private MarketDBService marketDBService;

    @MaiarQMQNode
    @QmqConsumer(prefix = "car.order.status", consumerGroup = "100011922-task-watch")
    public void qunarOrderConsumerRentCarCoupon(Message msg) {

        long orderId = msg.getLongProperty("OrderID");
        //2-处理中 3-成交 4-取消 5-待取消 9-取消中
        int orderStatus = msg.getIntProperty("OrderStatus");
        int categoryId = msg.getIntProperty("CategoryId");

        if (categoryId == 34 || categoryId == 35 || categoryId == 40 || categoryId == 41) {
            // 2: 已确认，x优惠券已经核销 4: 已取消，会退还优惠券，优惠券过期的话，取消订单也不会退换优惠券
            if (orderStatus == 4 || orderStatus == 3 || orderStatus == 2) {
                String couponCode = null;
                try {
                    couponCode = marketDBService.queryUseCoupon(orderId);
                } catch (SQLException e) {
                    log.error("qunarOrderConsumerRentCarCoupon", e);
                }

                if (StringUtils.isEmpty(couponCode)) {
                    return;
                }
                Message message = messageProducer.generateMessage("car.qunar.flight.coupon");
                message.setProperty("orderId", orderId);
                message.setProperty("orderStatus", orderStatus);
                message.setProperty("couponCode", couponCode);
                MaiarMessage.wrapQMQ(message);
                messageProducer.sendMessage(message, new MessageSendStateListener() {
                    @Override
                    public void onSuccess(Message message) {
                        log.info("qunarOrderConsumerRentCarCoupon.succ", orderId + "message producer success");
                    }

                    @Override
                    public void onFailed(Message message) {
                        log.error("qunarOrderConsumerRentCarCoupon.fail", orderId + "message producer failed");
                    }
                });
            }
        }
    }
}
