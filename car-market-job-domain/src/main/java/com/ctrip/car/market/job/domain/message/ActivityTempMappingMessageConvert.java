package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class ActivityTempMappingMessageConvert extends AbstractConvertMessage<Long, List<Long>, List<Long>> implements ConvertMessage<Long, List<Long>> {

    @Resource
    private ActivityService service;

    public ActivityTempMappingMessageConvert() {
        super(TabelEnum.Activity_Temp_Mapping);
    }

    @Override
    public Map<Long, List<Long>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Long getKey(String ids) {
        return Long.valueOf(ids);
    }

    @Override
    public List<Long> getData(Long id) throws Exception {
        List<Long> data = service.queryActivityIDByTemp(id);
        return CollectionUtils.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<Long> mapper(List<Long> value) {
        return value;
    }
}
