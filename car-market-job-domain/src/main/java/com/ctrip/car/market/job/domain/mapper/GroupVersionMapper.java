package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.CpnGroupVersionDO;
import com.ctrip.car.market.job.repository.entity.CpnGroupVersion;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface GroupVersionMapper {

    CpnGroupVersionDO to(CpnGroupVersion version);

    List<CpnGroupVersionDO> to(List<CpnGroupVersion> versions);
}
