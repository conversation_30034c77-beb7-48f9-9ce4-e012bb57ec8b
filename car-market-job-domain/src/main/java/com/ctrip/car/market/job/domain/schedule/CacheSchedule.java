package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.cache.*;
import com.ctrip.car.market.job.domain.cache.seo.*;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import javax.annotation.Resource;

@Component
public class CacheSchedule {

    @Resource(name = RestrictedPromotionCache.Name)
    private BasicCacheAbstract restrictedPromotionCache;

    @Resource(name = RestrictedConditionCache.Name)
    private BasicCacheAbstract restrictedConditionCache;

    @Resource(name = GifItemConfigCache.Name)
    private BasicCacheAbstract gifItemConfigCache;

    @Resource(name = GroupRcConditionCache.Name)
    private BasicCacheAbstract groupRcConditionCache;

    @Resource(name = GroupVersionCache.Name)
    private BasicCacheAbstract groupVersionCache;

    @Resource(name = PromotionOrderCache.Name)
    private BasicCacheAbstract promotionOrderCache;

    @Resource(name = PromotionRedirectUrlCache.Name)
    private BasicCacheAbstract promotionRedirectUrlCache;

    @Resource(name = UnionPromotionLimitCache.Name)
    private BasicCacheAbstract unionPromotionLimitCache;

    @Resource(name = UnionEnforcementConfigurationCache.Name)
    private BasicCacheAbstract unionEnforcementConfigurationCache;

    @Resource(name = MergeConditionCache.Name)
    private BasicCacheAbstract mergeConditionCache;

    @Resource(name = ProductRedirectUrlCache.Name)
    private BasicCacheAbstract productRedirectUrlCache;

    @Resource(name = PromotionSummaryCache.Name)
    private BasicCacheAbstract promotionSummaryCache;

    @Resource(name = LabelCache.Name)
    private BasicCacheAbstract labelCache;

    @Resource(name = LabelGroupCache.Name)
    private BasicCacheAbstract labelGroupCache;

    @Resource(name = RestrictedCondition_GroupCache.Name)
    private BasicCacheAbstract restrictedCondition_GroupCache;

    @Resource(name = RestrictedCondition_PromotionCache.Name)
    private BasicCacheAbstract restrictedCondition_PromotionCache;

    @Resource(name = RestrictedPromotion_SummaryCache.Name)
    private BasicCacheAbstract restrictedPromotion_SummaryCache;

    @Resource(name = MsgAllianceinfoCache.Name)
    private BasicCacheAbstract msgAllianceinfoCache;

    @Resource(name = ChannelNumberCache.Name)
    private BasicCacheAbstract channelNumberCache;

    @Resource(name = RecommendCityConfigCache.Name)
    private BasicCacheAbstract recommendCityConfigCache;

    @Resource(name = CarSaleRankCache.Name)
    private BasicCacheAbstract carSaleRankCache;

    @Resource(name = ActivityCache.Name)
    private BasicCacheAbstract activityCache;

    @Resource(name = Activity_VendorCache.Name)
    private BasicCacheAbstract activity_VendorCache;

    @Resource(name = ActivityTempAllCache.Name)
    private BasicCacheAbstract activityTempAllCache;

    @Resource(name = ActivityCityCache.Name)
    private BasicCacheAbstract activityCityCache;

    @Resource(name = ActivityReturnCityCache.Name)
    private BasicCacheAbstract activityReturnCityCache;

    @Resource(name = ActivityProductCache.Name)
    private BasicCacheAbstract activityProductCache;

    @Resource(name = ActivityTempMappingCache.Name)
    private BasicCacheAbstract activityTempMappingCache;

    @Resource(name = ActivityTempCache.Name)
    private BasicCacheAbstract activityTempCache;

    @Resource(name = ActivityVendorSkuCache.Name)
    private BasicCacheAbstract activityVendorSkuCache;

    @Resource(name = SeoCity_CountryCache.Name)
    private BasicCacheAbstract seoCity_CountryCache;

    @Resource(name = SeoCityCache.Name)
    private BasicCacheAbstract seoCityCache;

    @Resource(name = SeoInformationCache.Name)
    private BasicCacheAbstract seoInformationCache;

    @Resource(name = SeoInformationCountryCache.Name)
    private BasicCacheAbstract seoInformationCountryCache;

    @Resource(name = SeoPoi_CityCache.Name)
    private BasicCacheAbstract seoPoi_CityCache;

    @Resource(name = SeoPoi_CountryCache.Name)
    private BasicCacheAbstract seoPoi_CountryCache;

    @Resource(name = SeoPoiCache.Name)
    private BasicCacheAbstract seoPoiCache;

    @Resource(name = SeoCountryCache.Name)
    private BasicCacheAbstract seoCountryCache;

    @Resource(name = SeoVendorCache.Name)
    private BasicCacheAbstract seoVendorCache;

    @Resource(name = SeoVendorCityCache.Name)
    private BasicCacheAbstract seoVendorCityCache;

    @Resource(name = SeoVendorInformationCache.Name)
    private BasicCacheAbstract seoVendorInformationCache;

    @Resource(name = SeoVendorCommentScoreCache.Name)
    private BasicCacheAbstract seoVendorCommentScoreCache;

    @Resource(name = SeoProvinceCache.Name)
    private BasicCacheAbstract seoProvinceCache;

    @Resource(name = SeoCity_ProvinceCache.Name)
    private BasicCacheAbstract seoCity_ProvinceCache;

    @QSchedule(RestrictedPromotionCache.Name)
    public void restrictedPromotionCache(Parameter parameter) {
        restrictedPromotionCache.qSchedule(parameter);
    }

    @QSchedule(RestrictedConditionCache.Name)
    public void restrictedConditionCache(Parameter parameter) {
        restrictedConditionCache.qSchedule(parameter);
    }

    @QSchedule(GifItemConfigCache.Name)
    public void gifItemConfigCache(Parameter parameter) {
        gifItemConfigCache.qSchedule(parameter);
    }

    @QSchedule(GroupRcConditionCache.Name)
    public void groupRcConditionCache(Parameter parameter) {
        groupRcConditionCache.qSchedule(parameter);
    }

    @QSchedule(GroupVersionCache.Name)
    public void groupVersionCache(Parameter parameter) {
        groupVersionCache.qSchedule(parameter);
    }

    @QSchedule(PromotionOrderCache.Name)
    public void promotionOrderCache(Parameter parameter) {
        promotionOrderCache.qSchedule(parameter);
    }

    @QSchedule(PromotionRedirectUrlCache.Name)
    public void promotionRedirectUrlCache(Parameter parameter) {
        promotionRedirectUrlCache.qSchedule(parameter);
    }

    @QSchedule(UnionPromotionLimitCache.Name)
    public void unionPromotionLimitCache(Parameter parameter) {
        unionPromotionLimitCache.qSchedule(parameter);
    }

    @QSchedule(UnionEnforcementConfigurationCache.Name)
    public void unionEnforcementConfigurationCache(Parameter parameter) {
        unionEnforcementConfigurationCache.qSchedule(parameter);
    }

    @QSchedule(MergeConditionCache.Name)
    public void mergeConditionCache(Parameter parameter) {
        mergeConditionCache.qSchedule(parameter);
    }

    @QSchedule(ProductRedirectUrlCache.Name)
    public void productRedirectUrlCache(Parameter parameter) {
        productRedirectUrlCache.qSchedule(parameter);
    }

    @QSchedule(PromotionSummaryCache.Name)
    public void promotionSummaryCache(Parameter parameter) {
        promotionSummaryCache.qSchedule(parameter);
    }

    @QSchedule(LabelCache.Name)
    public void labelCache(Parameter parameter) {
        labelCache.qSchedule(parameter);
    }

    @QSchedule(LabelGroupCache.Name)
    public void labelGroupCache(Parameter parameter) {
        labelGroupCache.qSchedule(parameter);
    }

    @QSchedule(RestrictedCondition_GroupCache.Name)
    public void restrictedCondition_GroupCache(Parameter parameter) {
        restrictedCondition_GroupCache.qSchedule(parameter);
    }

    @QSchedule(RestrictedCondition_PromotionCache.Name)
    public void restrictedCondition_PromotionCache(Parameter parameter) {
        restrictedCondition_PromotionCache.qSchedule(parameter);
    }

    @QSchedule(RestrictedPromotion_SummaryCache.Name)
    public void restrictedPromotion_SummaryCache(Parameter parameter) {
        restrictedPromotion_SummaryCache.qSchedule(parameter);
    }

    @QSchedule(MsgAllianceinfoCache.Name)
    public void msgAllianceinfoCache(Parameter parameter) {
        msgAllianceinfoCache.qSchedule(parameter);
    }

    @QSchedule(ChannelNumberCache.Name)
    public void channelNumberCache(Parameter parameter) {
        channelNumberCache.qSchedule(parameter);
    }

    @QSchedule(RecommendCityConfigCache.Name)
    public void recommendCityConfigCache(Parameter parameter) {
        recommendCityConfigCache.qSchedule(parameter);
    }

    @QSchedule(CarSaleRankCache.Name)
    public void carSaleRankCache(Parameter parameter) {
        carSaleRankCache.qSchedule(parameter);
    }

    @QSchedule(ActivityCache.Name)
    public void activityCache(Parameter parameter) {
        activityCache.qSchedule(parameter);
    }

    @QSchedule(Activity_VendorCache.Name)
    public void activity_VendorCache(Parameter parameter) {
        activity_VendorCache.qSchedule(parameter);
    }

    @QSchedule(ActivityTempAllCache.Name)
    public void activityTempALlCache(Parameter parameter) {
        activityTempAllCache.qSchedule(parameter);
    }

    @QSchedule(ActivityCityCache.Name)
    public void activityCityCache(Parameter parameter) {
        activityCityCache.qSchedule(parameter);
    }

    @QSchedule(ActivityReturnCityCache.Name)
    public void activityReturnCityCache(Parameter parameter) {
        activityReturnCityCache.qSchedule(parameter);
    }

    @QSchedule(ActivityProductCache.Name)
    public void activityProductCache(Parameter parameter) {
        activityProductCache.qSchedule(parameter);
    }

    @QSchedule(ActivityTempMappingCache.Name)
    public void activityTempMappingCache(Parameter parameter) {
        activityTempMappingCache.qSchedule(parameter);
    }

    @QSchedule(ActivityTempCache.Name)
    public void activityTempCache(Parameter parameter) {
        activityTempCache.qSchedule(parameter);
    }

    @QSchedule(ActivityVendorSkuCache.Name)
    public void activityVendorSkuCache(Parameter parameter) {
        activityVendorSkuCache.qSchedule(parameter);
    }

    @QSchedule(SeoCity_CountryCache.Name)
    public void seoCity_CountryCache(Parameter parameter) {
        seoCity_CountryCache.qSchedule(parameter);
    }

    @QSchedule(SeoCityCache.Name)
    public void seoCityCache(Parameter parameter) {
        seoCityCache.qSchedule(parameter);
    }

    @QSchedule(SeoInformationCache.Name)
    public void seoInformationCache(Parameter parameter) {
        seoInformationCache.qSchedule(parameter);
    }

    @QSchedule(SeoInformationCountryCache.Name)
    public void seoInformationCountryCache(Parameter parameter) {
        seoInformationCountryCache.qSchedule(parameter);
    }

    @QSchedule(SeoPoi_CityCache.Name)
    public void seoPoi_CityCache(Parameter parameter) {
        seoPoi_CityCache.qSchedule(parameter);
    }

    @QSchedule(SeoPoi_CountryCache.Name)
    public void seoPoi_CountryCache(Parameter parameter) {
        seoPoi_CountryCache.qSchedule(parameter);
    }

    @QSchedule(SeoPoiCache.Name)
    public void seoPoiCache(Parameter parameter) {
        seoPoiCache.qSchedule(parameter);
    }

    @QSchedule(SeoCountryCache.Name)
    public void setCountryCache(Parameter parameter) {
        seoCountryCache.qSchedule(parameter);
    }

    @QSchedule(SeoVendorCache.Name)
    public void setSeoVendorCache(Parameter parameter) {
        seoVendorCache.qSchedule(parameter);
    }

    @QSchedule(SeoVendorCityCache.Name)
    public void setVendorCityCache(Parameter parameter) {
        seoVendorCityCache.qSchedule(parameter);
    }

    @QSchedule(SeoVendorInformationCache.Name)
    public void setVendorInformationCache(Parameter parameter) {
        seoVendorInformationCache.qSchedule(parameter);
    }

    @QSchedule(SeoVendorCommentScoreCache.Name)
    public void setVendorCommentScoreCache(Parameter parameter) {
        seoVendorCommentScoreCache.qSchedule(parameter);
    }

    @QSchedule(SeoProvinceCache.Name)
    public void seoProvinceCache(Parameter parameter) {
        seoProvinceCache.qSchedule(parameter);
    }

    @QSchedule(SeoCity_ProvinceCache.Name)
    public void seoCity_ProvinceCache(Parameter parameter) {
        seoCity_ProvinceCache.qSchedule(parameter);
    }
}
