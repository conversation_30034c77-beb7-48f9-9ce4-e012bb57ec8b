package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.ActCityInfoDO;
import com.ctrip.car.market.job.repository.entity.activity.ActCityinfo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ActCityInfoMapper {

    ActCityInfoDO to(ActCityinfo value);

    List<ActCityInfoDO> to(List<ActCityinfo> values);
}
