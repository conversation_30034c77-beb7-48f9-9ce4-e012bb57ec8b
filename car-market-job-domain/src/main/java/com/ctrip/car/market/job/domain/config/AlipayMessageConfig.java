package com.ctrip.car.market.job.domain.config;

import com.ctrip.car.market.job.domain.dto.AlipayCouponMessageDto;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class AlipayMessageConfig {

    @QMapConfig("alipayMessage.properties")
    public void onChange(Map<String, String> map) {
        this.recordCache = map.getOrDefault("recordCache", "0").equals("1");
        this.recordCacheExpireHour = Integer.valueOf(map.getOrDefault("recordCacheExpireHour", "336"));
        this.discountSuffix = map.get("discountSuffix");
        this.noDiscount = map.get("noDiscount");
        this.payTime = map.get("payTime");
        this.payCancelExpireHour = Integer.valueOf(map.getOrDefault("payCancelExpireHour", "24"));
        this.whiteList = Lists.newArrayList(map.getOrDefault("whiteList", "").split(",")).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        this.isSendActivity = map.getOrDefault("isSendActivity", "0").equals("1");
        this.zeusJobId = Long.valueOf(map.getOrDefault("zeusJobId", "1260323"));
        this.checkSubscribe = map.getOrDefault("checkSubscribe", "0").equals("1");
        this.actTaskCount = Integer.valueOf(map.getOrDefault("actTaskCount", "10"));
        this.couponTaskCount = Integer.valueOf(map.getOrDefault("couponTaskCount", "10"));
    }

    @QConfig("alipayCouponMessageConfig.json")
    private AlipayCouponMessageDto alipayCouponMessage;

    private boolean recordCache;

    private Integer recordCacheExpireHour;

    private String discountSuffix;

    private String noDiscount;

    private String payTime;

    private Integer payCancelExpireHour;

    private List<String> whiteList;

    private boolean isSendActivity;

    private Long zeusJobId;

    private boolean checkSubscribe;

    private Integer couponTaskCount;

    private Integer actTaskCount;

    public boolean isRecordCache() {
        return recordCache;
    }

    public Integer getRecordCacheExpireHour() {
        return recordCacheExpireHour;
    }

    public String getDiscountSuffix() {
        return discountSuffix;
    }

    public String getNoDiscount() {
        return noDiscount;
    }

    public String getPayTime() {
        return payTime;
    }

    public Integer getPayCancelExpireHour() {
        return payCancelExpireHour;
    }

    public List<String> getWhiteList() {
        return whiteList;
    }

    public boolean isSendActivity() {
        return isSendActivity;
    }

    public Long getZeusJobId() {
        return zeusJobId;
    }

    public boolean isCheckSubscribe() {
        return checkSubscribe;
    }

    public Integer getCouponTaskCount() {
        return couponTaskCount;
    }

    public Integer getActTaskCount() {
        return actTaskCount;
    }

    public AlipayCouponMessageDto getAlipayCouponMessage() {
        return alipayCouponMessage;
    }
}
