package com.ctrip.car.market.job.domain.dto;

import com.ctrip.car.market.job.domain.enums.SeoHotDestinationEnum;
import lombok.Data;

import java.util.Map;

@Data
public class MessageParams {
    private SeoHotDestinationEnum seoHotDestinationEnum;
    private Integer cityId;
    private Integer countryId;
    private Long poiId;
    private String poiCode;
    private String url;
    private String provinceId;
    private String vendorName;
    private String vendorCityName;
    private Integer vendorCityId;
    private String vendorCode;
    private Map<String, Map<String, String>> nameMap;


    public static MessageParams.Builder builder() {
        return new MessageParams.Builder();
    }


    public static final class Builder {
        private SeoHotDestinationEnum seoHotDestinationEnum;
        private Integer cityId;
        private Integer countryId;
        private Long poiId;
        private String poiCode;
        private String url;
        private String provinceId;
        private String vendorName;
        private String vendorCityName;
        private String vendorCode;
        private Integer vendorCityId;
        private Map<String, Map<String, String>> nameMap;

        private Builder() {
        }

        public MessageParams.Builder seoHotDestinationEnum(SeoHotDestinationEnum seoHotDestinationEnum) {
            this.seoHotDestinationEnum = seoHotDestinationEnum;
            return this;
        }

        public MessageParams.Builder cityId(Integer cityId) {
            this.cityId = cityId;
            return this;
        }

        public MessageParams.Builder countryId(Integer countryId) {
            this.countryId = countryId;
            return this;
        }

        public MessageParams.Builder poiId(Long poiId) {
            this.poiId = poiId;
            return this;
        }

        public MessageParams.Builder url(String url) {
            this.url = url;
            return this;
        }

        public MessageParams.Builder poiCode(String poiCode) {
            this.poiCode = poiCode;
            return this;
        }

        public MessageParams.Builder provinceId(String provinceId) {
            this.provinceId = provinceId;
            return this;
        }

        public MessageParams.Builder nameMap(Map<String, Map<String, String>> nameMap) {
            this.nameMap = nameMap;
            return this;
        }

        public MessageParams.Builder vendorName(String vendorName) {
            this.vendorName = vendorName;
            return this;
        }

        public MessageParams.Builder vendorCityName(String vendorCityName) {
            this.vendorCityName = vendorCityName;
            return this;
        }

        public MessageParams.Builder vendorCode(String vendorCode) {
            this.vendorCode = vendorCode;
            return this;
        }

        public MessageParams.Builder vendorCityId(Integer vendorCityId) {
            this.vendorCityId = vendorCityId;
            return this;
        }

        public MessageParams build() {
            MessageParams messageParams = new MessageParams();
            messageParams.seoHotDestinationEnum = this.seoHotDestinationEnum;
            messageParams.cityId = this.cityId;
            messageParams.countryId = this.countryId;
            messageParams.poiId = this.poiId;
            messageParams.poiCode = this.poiCode;
            messageParams.url = this.url;
            messageParams.provinceId = this.provinceId;
            messageParams.nameMap = this.nameMap;
            messageParams.vendorName = this.vendorName;
            messageParams.vendorCityName = this.vendorCityName;
            messageParams.vendorCode = this.vendorCode;
            messageParams.vendorCityId = this.vendorCityId;
            return messageParams;
        }
    }
}
