package com.ctrip.car.market.job.domain.alipay;

import com.ctrip.car.market.job.domain.config.AlipayMessageConfig;
import com.ctrip.car.market.job.domain.constant.CommonConstant;
import com.ctrip.car.market.job.domain.dto.MessageInfo;
import com.ctrip.car.market.job.domain.proxy.AlipayProxy;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.car.market.job.domain.utils.RedisUtil;
import com.ctrip.car.order.context.query.offline.OrderDetail;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Map;

public abstract class AlipayOrderService {

    @Resource
    private AlipayService alipayService;

    @Resource
    private AlipayMessageConfig alipayMessageConfig;

    @Resource
    private AlipayProxy alipayProxy;

    public final ILog log = LogManager.getLogger(AlipayOrderService.class);

    public void doBusiness(Long orderId, OrderDetail orderDetail) {
        String uid = orderDetail.getCustomerInfo() != null ? orderDetail.getCustomerInfo().getUid() : null;
        if (StringUtils.isEmpty(uid)) {
            log.warn("alipayOrderService", "no uid");
            Metrics.build().withTag("result","0").withTag("errorType","noUid").recordOne("alipayMessageOrder");
            return;
        }
        Map<String, String> tag = Maps.newHashMap();
        tag.put("uid", uid);
        tag.put("orderId", orderId.toString());
        //是否命中白名单
        if (!alipayService.isWitheList(uid)) {
            log.warn("alipayOrderService", "not in witheList", tag);
            Metrics.build().withTag("result","0").withTag("errorType","witheList").recordOne("alipayMessageOrder");
            return;
        }
        MessageInfo messageInfo = buildMessage(orderId, orderDetail);
        if (messageInfo == null) {
            log.warn("alipayOrderService", "message data error", tag);
            Metrics.build().withTag("result","0").withTag("errorType","messageData").recordOne("alipayMessageOrder");
            return;
        }
        //查询是否订阅消息
        boolean isSubscribe = alipayProxy.isSubscribe(uid, messageInfo.getTemplateId());
        if (!isSubscribe && alipayMessageConfig.isCheckSubscribe()) {
            log.warn("alipayOrderService", "no subscribe", tag);
            Metrics.build().withTag("result","0").withTag("errorType","noSubscribe").recordOne("alipayMessageOrder");
            return;
        }
        //发送记录
        String record = alipayService.queryRecord(messageInfo.getTemplateId(), uid);
        if (StringUtils.isNotEmpty(record)) {
            log.warn("alipayOrderService", "frequency limit-14", tag);
            Metrics.build().withTag("result","0").withTag("errorType","record").recordOne("alipayMessageOrder");
            return;
        }
        //待支付-取消发送记录
        String redisKey = String.format(CommonConstant.ALIPAY_MESSAGE_PAY_CANCEL_FORMAT, uid);
        if (StringUtils.isNotEmpty(RedisUtil.get(redisKey))) {
            log.warn("alipayOrderService", "frequency limit-1", tag);
            Metrics.build().withTag("result","0").withTag("errorType","payCancel").recordOne("alipayMessageOrder");
            return;
        }
        if (alipayService.sendMessage(messageInfo)) {
            RedisUtil.set(redisKey, uid, 3600 * alipayMessageConfig.getPayCancelExpireHour());
        }
        Metrics.build().withTag("result","1").withTag("errorType","success").recordOne("alipayMessageOrder");
    }

    protected abstract MessageInfo buildMessage(Long orderId, OrderDetail orderDetail);
}
