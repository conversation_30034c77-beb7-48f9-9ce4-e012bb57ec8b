package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.ActReturnCityInfoDO;
import com.ctrip.car.market.job.domain.mapper.ActReturnCityInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.ActReturnCityinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllActivityReturnCityService implements CachePreLoader<Long, List<ActReturnCityInfoDO>>, CacheLoader<Long, List<ActReturnCityInfoDO>> {

    @Resource
    private ActivityService service;

    @Resource
    private ActReturnCityInfoMapper mapper;

    private Map<Long, List<ActReturnCityInfoDO>> getAllActivityReturnCity() throws Exception {
        List<ActReturnCityinfo> data = service.queryActivityReturnCity();
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(ActReturnCityInfoDO::getActivityId));
    }

    @Override
    public List<ActReturnCityInfoDO> load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, List<ActReturnCityInfoDO>> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllActivityReturnCity();
    }

    @Override
    public Map<Long, List<ActReturnCityInfoDO>> preLoad(String s, Date date) {
        try {
            return getAllActivityReturnCity();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}