package com.ctrip.car.market.job.domain.mq;

import com.ctrip.car.maiar.mq.MaiarMessage;
import com.ctrip.car.market.job.common.CommonResult;
import com.ctrip.car.market.job.domain.proxy.AccountServiceProxy;
import com.ctrip.car.market.job.domain.proxy.SDOrderServiceProxy;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.domain.utils.QConfigUtil;
import com.ctrip.car.sdorderservice.soa.autogenerate.OrderBaseInfo;
import com.ctrip.car.sdorderservice.soa.autogenerate.OrderDetailInfo;
import com.ctrip.car.sdorderservice.soa.autogenerate.StoreInfo;
import com.ctrip.flight.intl.common.metric.Metrics;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageProducer;
import qunar.tc.qmq.MessageSendStateListener;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ShenNongOrderMsgListen {

    private final ILog log = LogManager.getLogger(ShenNongOrderMsgListen.class);
    // 日志tag
    private static final ThreadLocal<Map<String, String>> LOG_TAG = ThreadLocal.withInitial(HashMap::new);
    // 订单各属性是否为空值
    private static final ThreadLocal<Map<String, String>> ORDER_PROPERTY_TAG = ThreadLocal.withInitial(HashMap::new);

    // 处理的Bu（租车）
    private static final String DEAL_BU = "VAC";
    // 需要重试的errorCode
    private static final String RETRY_ERROR_CODE = "RPCException";
    // 最大重试次数
    private static final Integer RETRY_MAX_TIMES = 3;
    // 重试延迟时间（分钟）
    private static final Integer RETRY_DELAY_MINUTE = 5;

    @Resource
    private AccountServiceProxy accountServiceProxy;
    @Resource
    private SDOrderServiceProxy sdOrderServiceProxy;
    @Resource
    private MessageProducer messageProducer;

    @QmqConsumer(prefix = "di.cdp.mkt.auto.discretion.push.to", consumerGroup = "*********-order-msg-watch")
    public void consumer(Message msg) {
        // 关闭自动ACK
        msg.autoAck(false);
        LOG_TAG.remove();
        ORDER_PROPERTY_TAG.remove();

        CommonResult<Void> consumerResult = null;
        try {
            // 消费消息
            consumerResult = doConsumer(msg);
        } catch (Exception e) {
            log.error("ShenNongOrderMsgListen ConsumerFailed ", e);
            consumerResult = CommonResult.failed(ErrorType.SYSTEM_ERROR.name(), ErrorType.SYSTEM_ERROR.name(), e.getMessage());
        } finally {
            boolean retry = false;
            assert consumerResult != null;
            if (!consumerResult.isSuccess() && RETRY_ERROR_CODE.equalsIgnoreCase(consumerResult.getCode()) && msg.times() <= RETRY_MAX_TIMES) {
                // 重新消费
                retry = true;
                msg.ack(new NeedRetryException(System.currentTimeMillis() + 1000L * 60 * RETRY_DELAY_MINUTE, RETRY_ERROR_CODE));
            } else {
                // 正常提交
                msg.ack(null);
            }

            // 记录埋点 & 日志
            recordLog(msg, consumerResult, retry);
        }
    }

    /**
     * 消费消息
     */
    private CommonResult<Void> doConsumer(Message msg) {
        // 校验接收的消息
        CommonResult<ReceiveMsg> checkResult = checkReceiveMsg(msg);
        if (!checkResult.isSuccess()) {
            return CommonResult.failed(checkResult.getErrorType(), checkResult.getCode(), checkResult.getMsg());
        }

        // 构建需要推送的消息
        CommonResult<PushMsg> buildResult = buildPushMsg(checkResult.getData());
        if (!buildResult.isSuccess()) {
            return CommonResult.failed(buildResult.getErrorType(), buildResult.getCode(), buildResult.getMsg());
        }

        // 推送消息
        pushMsg(msg.getMessageId(), buildResult.getData());
        return CommonResult.success(null);
    }

    /**
     * 校验接收的消息
     */
    private CommonResult<ReceiveMsg> checkReceiveMsg(Message msg) {
        String bu = msg.getStringProperty("bu");
        String planId = msg.getStringProperty("plan_id");
        String params = msg.getStringProperty("params");
        String taskId = msg.getStringProperty("task_id");
        String scene = msg.getStringProperty("scene");

        if (StringUtils.isBlank(bu) || !DEAL_BU.equalsIgnoreCase(bu)) {
            return CommonResult.failed(ErrorType.IGNORE.name(), "Ignore", "IgnoreBu: " + bu);
        }
        if (StringUtils.isBlank(planId)) {
            return CommonResult.failed(ErrorType.MESSAGE_ILLEGAL.name(), "MsgMissParam::plan_id", "MsgMissParam: plan_id");
        }
        if (StringUtils.isBlank(params)) {
            return CommonResult.failed(ErrorType.MESSAGE_ILLEGAL.name(), "MsgMissParam::params", "MsgMissParam: params");
        }

        ParamsDTO paramsDTO = JsonUtil.parseObject(params, ParamsDTO.class);
        LOG_TAG.get().put("orderId", paramsDTO.getOrderId());
        LOG_TAG.get().put("phone", paramsDTO.getMobilePhone());
        if (StringUtils.isBlank(paramsDTO.getOrderId())) {
            return CommonResult.failed(ErrorType.MESSAGE_ILLEGAL.name(), "MsgMissParam::order_id", "MsgMissParam: order_id");
        }
        if (StringUtils.isBlank(paramsDTO.getMobilePhone())) {
            return CommonResult.failed(ErrorType.MESSAGE_ILLEGAL.name(), "MsgMissParam::mobile_phone", "MsgMissParam: mobile_phone");
        }

        ReceiveMsg receiveMsg = new ReceiveMsg();
        receiveMsg.setBu(bu);
        receiveMsg.setPlan_id(planId);
        receiveMsg.setParams(params);
        receiveMsg.setParamsDTO(paramsDTO);
        receiveMsg.setTask_id(taskId);
        receiveMsg.setScene(scene);
        return CommonResult.success(receiveMsg);
    }

    /**
     * 构建需要推送的消息
     */
    private CommonResult<PushMsg> buildPushMsg(ReceiveMsg receiveMsg) {
        CommonResult<String> uidResult = accountServiceProxy.getUidByMobile(receiveMsg.getParamsDTO().getMobilePhone());
        if (!uidResult.isSuccess()) {
            return CommonResult.failed(ErrorType.GET_UID_FAILED.name(), uidResult.getCode(), uidResult.getMsg());
        }

        CommonResult<OrderDetailInfo> orderResult = sdOrderServiceProxy.getOrderDetailByOrderId(Long.valueOf(receiveMsg.getParamsDTO().getOrderId()));
        if (!orderResult.isSuccess()) {
            return CommonResult.failed(ErrorType.GET_ORDER_FAILED.name(), orderResult.getCode(), orderResult.getMsg());
        }

        OrderProperty orderProperty = buildOrderProperty(orderResult.getData());
        if (hasEmptyProperty(orderProperty)) {
            return CommonResult.failed(ErrorType.ORDER_INCOMPLETE.name(), ErrorType.ORDER_INCOMPLETE.name(), "Order Incomplete");
        }

        PushMsg pushMsg = new PushMsg();
        pushMsg.setBu(receiveMsg.getBu());
        pushMsg.setPlan_id(receiveMsg.getPlan_id());
        pushMsg.setUid(uidResult.getData());
        pushMsg.setOrderPropertyDTO(orderProperty);
        String originPrams = receiveMsg.getParams();
        String addPrams = JsonUtil.toJSONString(orderProperty);
        pushMsg.setParams(originPrams.substring(0, originPrams.lastIndexOf("}")) + "," + addPrams.substring(1));
        pushMsg.setTask_id(receiveMsg.getTask_id());
        pushMsg.setScene(receiveMsg.getScene());
        return CommonResult.success(pushMsg);
    }

    private OrderProperty buildOrderProperty(OrderDetailInfo orderDetailInfo) {
        OrderProperty orderProperty = new OrderProperty();

        OrderBaseInfo orderBaseInfo = orderDetailInfo.getOrderBaseInfo();
        orderProperty.setCoorperationChannelId(orderBaseInfo.getDistributionChannelID() == null ? "" : orderBaseInfo.getDistributionChannelID().toString());
        orderProperty.setDistributionChannelDesc(QConfigUtil.getByFileAndKey("conf.properties", "ChannelDesc_" + orderBaseInfo.getDistributionChannelID()));
        orderProperty.setContripClientName(orderBaseInfo.getContractName());
        orderProperty.setBrandName(orderBaseInfo.getProductName());

        StoreInfo storeInfo = orderDetailInfo.getStoreInfo();
        orderProperty.setPickUpServiceTypeDesc(storeInfo.getPickUpTypeDesc());
        orderProperty.setPickAddress(storeInfo.getPickUpAdd());
        orderProperty.setPickStoreName(storeInfo.getPickUpStoreName());
        orderProperty.setPickStoreAddress(storeInfo.getPickUpStoreAdd());

        if (StringUtils.isNotBlank(orderDetailInfo.getPickUpTime())) {
            String time = orderDetailInfo.getPickUpTime();
            if (time.lastIndexOf(".000") > 0) {
                time = time.substring(0, time.lastIndexOf(".000"));
                if (time.lastIndexOf(":00") > 0) {
                    time = time.substring(0, time.lastIndexOf(":00"));
                }
            }
            orderProperty.setUseTime(time);
        }

        return orderProperty;
    }

    /**
     * 判断订单属性中，是否有空值属性
     */
    private boolean hasEmptyProperty(OrderProperty orderProperty) {
        Map<String, String> propertyMap = new HashMap<>();
        propertyMap.put("coorperationChannelId", orderProperty.coorperationChannelId);
        propertyMap.put("distributionChannelDesc", orderProperty.distributionChannelDesc);
        propertyMap.put("contripClientName", orderProperty.contripClientName);
        propertyMap.put("brandName", orderProperty.brandName);
        propertyMap.put("useTime", orderProperty.useTime);
        propertyMap.put("pickAddress", orderProperty.pickAddress);
        propertyMap.put("pickStoreName", orderProperty.pickStoreName);
        propertyMap.put("pickStoreAddress", orderProperty.pickStoreAddress);
        propertyMap.put("pickUpServiceTypeDesc", orderProperty.pickUpServiceTypeDesc);

        Map<String, String> orderPropertyEmptyTag = propertyMap.entrySet().stream().collect(Collectors.toMap(entry -> "emptyOrderProperty_" + entry.getKey(), entry -> String.valueOf(StringUtils.isBlank(entry.getValue()))));
        ORDER_PROPERTY_TAG.set(orderPropertyEmptyTag);
        ORDER_PROPERTY_TAG.get().put("coorperationChannelId", orderProperty.coorperationChannelId);
        ORDER_PROPERTY_TAG.get().put("distributionChannelDesc", orderProperty.distributionChannelDesc);

        String serviceTypeMustExistChannelIds = QConfigUtil.getByFileAndKey("conf.properties", "OrderProperty_ServiceType_MustExist_ChannelIds");
        if (Arrays.asList(serviceTypeMustExistChannelIds.split(",")).contains(orderProperty.coorperationChannelId)) {
            return propertyMap.entrySet().stream().anyMatch(entry -> StringUtils.isBlank(entry.getValue()));
        } else {
            return propertyMap.entrySet().stream().anyMatch(entry -> !entry.getKey().equals("pickUpServiceTypeDesc") && StringUtils.isBlank(entry.getValue()));
        }
    }

    /**
     * 推送消息
     */
    private void pushMsg(String receiveMessageId, PushMsg pushMsg) {
        Message message = messageProducer.generateMessage("di.cdp.mkt.auto.discretion.push.from");
        message.setProperty("bu", pushMsg.getBu());
        message.setProperty("plan_id", pushMsg.getPlan_id());
        message.setProperty("uid", pushMsg.getUid());
        message.setProperty("params", pushMsg.getParams());
        message.setProperty("task_id", pushMsg.getTask_id());
        message.setProperty("scene", pushMsg.getScene());
        MaiarMessage.wrapQMQ(message);
        messageProducer.sendMessage(message, new MessageSendStateListener() {

            @Override
            public void onSuccess(Message message) {
                Map<String, String> tags = new HashMap<>();
                tags.put("receiveMessageId", receiveMessageId);
                tags.put("pushMessageId", message.getMessageId());
                log.info("di.cdp.mkt.auto.discretion.push.from success", String.format("pushMessage=%s", JsonUtil.toJSONString(message)), tags);
            }

            @Override
            public void onFailed(Message message) {
                Map<String, String> tags = new HashMap<>();
                tags.put("receiveMessageId", receiveMessageId);
                tags.put("pushMessageId", message.getMessageId());
                log.error("di.cdp.mkt.auto.discretion.push.from failed", String.format("pushMessage=%s", JsonUtil.toJSONString(message)), tags);
            }
        });
    }

    /**
     * 记录埋点 & 日志
     */
    private void recordLog(Message msg, CommonResult<Void> consumerResult, boolean retry) {
        // 记录埋点
        Map<String, String> metricsTags = new HashMap<>();
        metricsTags.put("bu", msg.getStringProperty("bu"));
        metricsTags.put("planId", msg.getStringProperty("plan_id"));
        metricsTags.put("success", String.valueOf(consumerResult.isSuccess()));
        metricsTags.put("errorType", consumerResult.getErrorType());
        metricsTags.put("errorCode", consumerResult.getCode());
        metricsTags.put("retry", String.valueOf(retry));
        metricsTags.putAll(ORDER_PROPERTY_TAG.get());
        Metrics.withTags(metricsTags).recordOne("ShenNongOrderMsgListen");

        // 记录日志
        Map<String, String> logTags = new HashMap<>(metricsTags);
        logTags.putAll(LOG_TAG.get());
        logTags.put("messageId", msg.getMessageId());
        logTags.put("errorMsg", consumerResult.getMsg());
        log.info("ShenNongOrderMsgListen", String.format("Message=%s", JsonUtil.toJSONString(msg)), logTags);
    }

    /**
     * 接收消息DTO
     */
    @Data
    public static class ReceiveMsg {
        // 业务
        private String bu;
        // 计划id
        private String plan_id;
        // 参数-json字符串
        private String params;
        // 参数-DTO
        private ParamsDTO paramsDTO;

        // 消息回传用
        private String task_id;
        // 消息回传用
        private String scene;
    }

    /**
     * 接收消息-参数DTO
     */
    @Data
    public static class ParamsDTO {
        // 订单ID
        @JsonProperty("[order_id]")
        private String orderId;

        // 手机号
        @JsonProperty("[mobile_phone]")
        private String mobilePhone;
    }


    /**
     * 推送消息DTO
     */
    @Data
    public static class PushMsg {
        // 业务
        private String bu;
        // 计划id
        private String plan_id;
        // uid
        private String uid;
        // 参数-json字符串
        private String params;
        // 参数-DTO
        private OrderProperty orderPropertyDTO;

        // 消息回传（透传）字段
        private String task_id;
        // 消息回传（透传）字段
        private String scene;
    }

    /**
     * 推送消息-参数DTO
     */
    @Data
    public static class OrderProperty {
        // 渠道号
        @JsonProperty("[coorperation_channel_id]")
        private String coorperationChannelId;

        // 渠道描述
        @JsonProperty("[distribution_channel_desc]")
        private String distributionChannelDesc;

        // 驾驶员姓名
        @JsonProperty("[contrip_client_name]")
        private String contripClientName;

        // 取车方式
        @JsonProperty("[pick_up_service_type_desc]")
        private String pickUpServiceTypeDesc;

        // 用车时间
        @JsonProperty("[use_time]")
        private String useTime;

        // 取车地址
        @JsonProperty("[pick_address]")
        private String pickAddress;

        // 取车门店名称
        @JsonProperty("[pick_store_name]")
        private String pickStoreName;

        // 取车门店地址
        @JsonProperty("[pick_store_address]")
        private String pickStoreAddress;

        // 车型品牌名称
        @JsonProperty("[brand_name]")
        private String brandName;
    }

    /**
     * 消息消费处理失败，失败类型
     */
    public enum ErrorType {
        /**
         * 忽略消息（非租车消息）
         */
        IGNORE,

        /**
         * 消息不合法
         */
        MESSAGE_ILLEGAL,

        /**
         * 获取uid失败
         */
        GET_UID_FAILED,

        /**
         * 获取订单失败
         */
        GET_ORDER_FAILED,

        /**
         * 订单信息缺失
         */
        ORDER_INCOMPLETE,

        /**
         * 系统异常
         */
        SYSTEM_ERROR;
    }
}
