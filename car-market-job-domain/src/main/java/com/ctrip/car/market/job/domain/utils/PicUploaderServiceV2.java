package com.ctrip.car.market.job.domain.utils;

import com.ctrip.car.market.job.domain.dto.CreateTokenReqDTO;
import com.ctrip.car.market.job.domain.dto.CreateTokenResDTO;
import com.ctrip.car.market.job.domain.dto.UploadImageRespDTO;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class PicUploaderServiceV2 {

    private static final Logger logger = LoggerFactory.getLogger(PicUploaderServiceV2.class);

    private static final String CHANNEL = "car";

    public static String uploadImage(byte[] image, String subfix, boolean noProxy) {
        String uuid = UUIDUtil.getGuid();
        try {
            String tokenResult = HttpTool.post_String(QConfigUtil.getByFileAndKey("conf.properties", "IMAGE_TOKEN_CREATE_URL"), getToken(uuid), noProxy, "utf-8");
            logger.warn("uploadImageV2==>" + "tokenResult:" + tokenResult);
            String token = "";
            if (StringUtils.isNotEmpty(tokenResult)) {
                CreateTokenResDTO createTokenResDTO = JsonUtils.fromJson(tokenResult, CreateTokenResDTO.class);
                if (createTokenResDTO != null) {
                    token = createTokenResDTO.getContent().getToken();
                }
            }
            String baseUrl = String.format("%s?channel=%s&scene=%s&rand=%s&public=1",
                    QConfigUtil.getByFileAndKey("conf.properties", "BASE_64_UPLOAD_URL"),
                    "car",
                    QConfigUtil.getByFileAndKey("conf.properties", "APP_SCENE"),
                    uuid);
            String credential = Base64.getEncoder().encodeToString(String.format("token:%s", token).getBytes());
            DateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm");
            String date = dateFormat.format(System.currentTimeMillis());
            String contentMD5 = UUIDUtil.getContentMd5(image);
            // 拼接的uuid只能有数字和字母 不能有“-”，去除
            String authorization = String.format("Nephele %s/%s/%s/%s", uuid, date, CHANNEL, credential);
            Map<String, String> map = new HashMap<>();
            map.put("Authorization", authorization);
            map.put("Content-MD5", contentMD5);
            map.put("Content-Type", "image/png");
            String response = HttpTool.postImage(baseUrl, image, subfix, noProxy, map);
            logger.warn("uploadImageV2==>response:" + response);
            UploadImageRespDTO uploadImageRespDTO = JsonUtils.fromJson(response, UploadImageRespDTO.class);
            if (uploadImageRespDTO != null && uploadImageRespDTO.getCode() == 2000 && uploadImageRespDTO.getContent() != null) {
                return uploadImageRespDTO.getContent().getUrl();
            } else {
                logger.warn("uploadImageV2==>response:" + "readValue error");
            }
        } catch (Exception ex) {
            logger.warn("uploadImageV2==>", ex);
        }
        return "";
    }

    private static CreateTokenReqDTO getToken(String uuid) {
        try {
            CreateTokenReqDTO uploadImageTokenReq = new CreateTokenReqDTO();

            uploadImageTokenReq.setChannel(CHANNEL);
            uploadImageTokenReq.setRand(uuid);

            String contentMd5 = "";
            try {
                String app_secret_kms = KmsUtils.getPwdWithValue("APP_SECRET_KMS");
                logger.warn("uploadImageV2==>" + "APP_SECRET" + app_secret_kms);
                contentMd5 = UUIDUtil.getContentMd5(String.format("%s%s%s", CHANNEL, uuid, app_secret_kms).getBytes());
            } catch (Exception e) {
                logger.warn("uploadImageV2==>" + "getToken:" + e);
            }
            uploadImageTokenReq.setSign(contentMd5);
            return uploadImageTokenReq;
        } catch (Exception ex) {
            logger.warn("uploadImageV2==>" + "getToken:" + ex);
        }
        return null;
    }
}
