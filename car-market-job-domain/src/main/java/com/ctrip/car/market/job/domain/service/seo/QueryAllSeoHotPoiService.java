package com.ctrip.car.market.job.domain.service.seo;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotDestinatioinfoMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotDestinatioinfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllSeoHotPoiService implements CachePreLoader<String, List<SeoHotDestinatioinfoDO>>, CacheLoader<String, List<SeoHotDestinatioinfoDO>> {

    @Resource
    private MarketDBService service;

    @Resource
    private SeoHotDestinatioinfoMapper mapper;

    private Map<String, List<SeoHotDestinatioinfoDO>> getALlHotPoi() throws Exception {
        List<SeoHotDestinatioinfo> data = service.queryAllHotPoi();
        return data.stream().filter(l -> l.getPoiType() != null && StringUtils.isNotEmpty(l.getPoiCode())).map(mapper::to).collect(Collectors.groupingBy(l -> l.getPoiType() + "-" + l.getPoiCode().toLowerCase()));
    }

    @Override
    public List<SeoHotDestinatioinfoDO> load(String area, String key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<String, List<SeoHotDestinatioinfoDO>> loadAll(String area, Set<String> keys) throws Throwable {
        return getALlHotPoi();
    }

    @Override
    public Map<String, List<SeoHotDestinatioinfoDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getALlHotPoi();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
