package com.ctrip.car.market.job.domain.cache.seo;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotCityinfoDO;
import com.ctrip.car.market.job.domain.cache.BasicCacheAbstract;
import com.ctrip.car.market.job.domain.service.seo.QueryAllSeoHotCityService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.SEO_CITY_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.SeoCityCacheName;

@Component(SeoCityCache.Name)
@CreateCacheArea(area = "public")
public class SeoCityCache extends BasicCacheAbstract<Integer, List<SeoHotCityinfoDO>> {

    public static final String Name = SeoCityCacheName;

    @Resource
    private QueryAllSeoHotCityService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = SEO_CITY_KEY, remotePreLoadName = Name)
    private Cache<Integer, List<SeoHotCityinfoDO>> cache;

    @Override
    public ConvertMessage<Integer, List<SeoHotCityinfoDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<Integer, List<SeoHotCityinfoDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, List<SeoHotCityinfoDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, List<SeoHotCityinfoDO>> getLoader() {
        return null;
    }
}
