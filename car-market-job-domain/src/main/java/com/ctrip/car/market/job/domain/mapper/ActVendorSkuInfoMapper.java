package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.ActVendorSkuInfoDO;
import com.ctrip.car.market.job.repository.entity.activity.VendorSkuinfo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ActVendorSkuInfoMapper {

    ActVendorSkuInfoDO to(VendorSkuinfo value);

    List<ActVendorSkuInfoDO> to(List<VendorSkuinfo> value);
}
