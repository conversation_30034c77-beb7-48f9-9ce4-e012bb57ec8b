package com.ctrip.car.market.job.domain.utils;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.framework.foundation.Foundation;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import org.elasticsearch.common.Strings;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

public class CLogUtil {

    private final static String Scenario = "car-common-log";

    private static final ILog log = LogManager.getLogger(CLogUtil.class);

    public static void info(String title, Object... objects) {
        info(title, Maps.newHashMap(), objects);
    }

    public static void tagInfo(String title, Map<String, String> indexTags, Object... objects) {
        info(title, indexTags, objects);
    }

    public static void error(String title, String message) {
        log.error(title, message);
    }

    public static void error(String title, Throwable throwable) {
        log.error(title, throwable);
    }

    public static void error(String title, Throwable throwable,  Map<String, String> indexTags) {
        log.error(title, throwable, indexTags);
    }

    public static void warn(String title, Throwable throwable) {
        log.warn(title, throwable);
    }

    public static void warn(String title, String message) {
        log.warn(title, message);
    }

    public static void warn(String title, String message,  Map<String, String> indexTags) {
        log.warn(title, message, indexTags);
    }

    private static void info(String title, Map<String, String> indexTags, Object... objects) {
        try {
            if (Objects.isNull(objects) || objects.length <= 0) {
                return;
            }
            indexTags.put("title", title);
            String msg = getObjsStr(objects);
            log.info(title, msg, indexTags);
        } catch (Exception e) {
            log.warn("log", e);
        }
    }

    private static String getObjsStr(Object... objects) throws Exception {

        StringBuilder stringBuilder = new StringBuilder();

        if (objects.length % 2 == 0) {
            int obLen = objects.length / 2;
            for (int i = 0; i < obLen; i++) {
                stringBuilder.append(objects[i * 2]);
                stringBuilder.append(":");
                Object value = objects[i * 2 + 1];
                if (value == null) {
                    stringBuilder.append("<null>");
                } else if (value instanceof String) {
                    stringBuilder.append(value);
                } else {
                    stringBuilder.append(JsonUtil.toJSONString(value));
                }
                stringBuilder.append("\r\n");
            }
        } else {
            for (Object object : objects) {
                if (object == null) {
                    stringBuilder.append("<null>");
                } else if (object instanceof String) {
                    stringBuilder.append(object);
                } else {
                    stringBuilder.append(JsonUtil.toJSONString(object));
                }
                stringBuilder.append("\r\n");
            }
        }
        return stringBuilder.toString();
    }

    public static void esLog(String title, String uid, Object req, Object res) {
        CompletableFuture.runAsync(() -> {
            try {
                Map<String, String> indexedTags = Maps.newHashMap();
                indexedTags.put("timestamp", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                indexedTags.put("cat_client_appid", Foundation.app().getAppId());
                indexedTags.put("operation_type", title);
                indexedTags.put("operate", JsonUtil.toJSONString(req));
                indexedTags.put("content", JsonUtil.toJSONString(res));
                indexedTags.put("primary_key", !Strings.isNullOrEmpty(uid) ? uid : "");
                Cat.logTags(Scenario, indexedTags, Maps.newHashMap());
            } catch (Exception e) {
                log.warn("commonLog", e);
            }
        });
    }
}
