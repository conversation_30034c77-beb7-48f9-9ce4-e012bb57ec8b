package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CarSaleRankDO;
import com.ctrip.car.market.job.domain.service.QueryAllCarSaleRankService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.CAR_SALE_RANK_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.CarSaleRankCacheName;

@Component(CarSaleRankCache.Name)
@CreateCacheArea(area = "public")
public class CarSaleRankCache extends BasicCacheAbstract<Integer, List<CarSaleRankDO>>{

    public static final String Name = CarSaleRankCacheName;

    @Resource
    private QueryAllCarSaleRankService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = CAR_SALE_RANK_HASH_KEY, remotePreLoadName = Name)
    private Cache<Integer, List<CarSaleRankDO>> cache;

    @Override
    public ConvertMessage<Integer, List<CarSaleRankDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<Integer, List<CarSaleRankDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, List<CarSaleRankDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, List<CarSaleRankDO>> getLoader() {
        return null;
    }
}
