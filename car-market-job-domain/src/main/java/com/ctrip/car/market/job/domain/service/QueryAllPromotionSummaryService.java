package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnPromotionSummaryDO;
import com.ctrip.car.market.job.domain.mapper.PromotionSummaryMapper;
import com.ctrip.car.market.job.repository.entity.CpnPromotionSummary;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllPromotionSummaryService implements CachePreLoader<Integer, List<CpnPromotionSummaryDO>>, CacheLoader<Integer, List<CpnPromotionSummaryDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private PromotionSummaryMapper mapper;

    private Map<Integer, List<CpnPromotionSummaryDO>> getAllGroupRcCondition() throws Exception {
        List<CpnPromotionSummary> data = service.queryAllPromotionSummary();
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(CpnPromotionSummaryDO::getActivityID));
    }

    @Override
    public List<CpnPromotionSummaryDO> load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, List<CpnPromotionSummaryDO>> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getAllGroupRcCondition();
    }

    @Override
    public Map<Integer, List<CpnPromotionSummaryDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllGroupRcCondition();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
