package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.CpnProductRedirectUrlDO;
import com.ctrip.car.market.job.repository.entity.CpnProductRedirectUrl;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ProductRedirectUrlMapper {

    CpnProductRedirectUrlDO to(CpnProductRedirectUrl value);

    List<CpnProductRedirectUrlDO> to(List<CpnProductRedirectUrl> values);
}
