package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.car.market.crossrecommend.service.contract.CarCrossRecommendedServiceClient;
import com.ctrip.car.market.crossrecommend.service.contract.dto.RecommendProductDTO;
import com.ctrip.car.market.crossrecommend.service.contract.servicetype.CarRecommendProductRequestType;
import com.ctrip.car.market.crossrecommend.service.contract.servicetype.CarRecommendProductResponseType;
import com.ctrip.car.market.job.domain.service.UGCDataManager;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.*;

@Component
public class CarCrossRecommendedServiceClientProxy {

    private static final ILog log = LogManager.getLogger(CarCrossRecommendedServiceClientProxy.class);

    @Autowired
    private UGCDataManager ugcDataManager;
    CarCrossRecommendedServiceClient client = CarCrossRecommendedServiceClient.getInstance();
    public List<RecommendProductDTO> queryProductByRequest(CarRecommendProductRequestType requestType) {

        try {
            CarRecommendProductResponseType responseType = client.carRecommendProduct(requestType);
            HashMap<String, String> logAttrs = new HashMap<String, String>();
            logAttrs.put("cityId", requestType.getPickUpCityId() + "");
            if (responseType != null && CollectionUtils.isNotEmpty(responseType.getRecommendProducts())) {
                logAttrs.put("size", responseType.getRecommendProducts().size() + "");
                log.info("CarCrossRecommendedServiceClientProxy", JsonUtil.toJSONString(requestType), logAttrs);
                ugcDataManager.sendToRedis(responseType.getRecommendProducts(), requestType);
                return responseType.getRecommendProducts();
            } else {
                logAttrs.put("size", "0");
                log.warn("CarCrossRecommendedServiceClientProxy", JsonUtil.toJSONString(requestType), logAttrs);
            }

        } catch (Exception e) {
            log.warn("CarCrossRecommendedServiceClientProxy", e.getMessage());
        }
        return null;
    }

}

