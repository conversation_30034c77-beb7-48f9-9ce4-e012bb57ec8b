package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnPromotionidredirecturlDO;
import com.ctrip.car.market.job.domain.mapper.PromotionidredirecturlMapper;
import com.ctrip.car.market.job.repository.entity.CpnPromotionidredirecturl;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllPromotionRedirectUrlService implements CachePreLoader<Long, CpnPromotionidredirecturlDO>, CacheLoader<Long, CpnPromotionidredirecturlDO> {

    @Resource
    private RestrictedService service;

    @Resource
    private PromotionidredirecturlMapper mapper;

    private Map<Long, CpnPromotionidredirecturlDO> getAllGroupRcCondition() throws Exception {
        List<CpnPromotionidredirecturl> data = service.queryAllPromotionRedirectUrl();
        return data.stream().collect(Collectors.toMap(CpnPromotionidredirecturl::getPromotionId, mapper::to, (k1, k2) -> k1));
    }

    @Override
    public CpnPromotionidredirecturlDO load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, CpnPromotionidredirecturlDO> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllGroupRcCondition();
    }

    @Override
    public Map<Long, CpnPromotionidredirecturlDO> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllGroupRcCondition();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
