package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.igt.geo.interfaces.message.QueryFuzzyAddressResponseType;
import com.ctrip.igt.geo.interfaces.message.QueryFuzzyAddressRequestType;
import com.ctrip.igt.RequestHeader;
import com.ctrip.igt.geo.interfaces.OchGeoServiceClient;
import org.springframework.stereotype.Component;

@Component
public class IgtGeoServiceProxy {

    private final ILog log = LogManager.getLogger(IgtGeoServiceProxy.class);

    private final OchGeoServiceClient client = OchGeoServiceClient.getInstance();

    public QueryFuzzyAddressResponseType queryFuzzyAddress(Long cityId, String poiName){
        try {
            QueryFuzzyAddressRequestType requestType = new QueryFuzzyAddressRequestType();
            requestType.setCityCode(cityId.toString());
            requestType.setQuery(poiName);
            requestType.setCityLimit(true);
            requestType.setLimitCount(1);
            requestType.setRequestHeader(new RequestHeader());
            requestType.getRequestHeader().setSeverFrom("carrental");
            requestType.getRequestHeader().setLocale("zh-CN");
            return client.queryFuzzyAddress(requestType);
        } catch (Exception e) {
            log.warn("queryFuzzyAddress", e);
            return null;
        }
    }
}