package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.soa.platform.account.promocodeservice.data.v1.CouponCodeStatusItem;
import com.ctrip.soa.platform.account.promocodeservice.data.v1.PromotionCouponCode;
import com.ctrip.soa.platform.account.promocodeservice.message.v1.*;
import com.ctrip.soa.platform.account.promocodeservice.v1.GetUserCouponCodeDetailRequestType;
import com.ctrip.soa.platform.account.promocodeservice.v1.GetUserCouponCodeDetailResponseType;
import com.ctrip.soa.platform.account.promocodeservice.v1.PromocodeServiceClient;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class PromotionProxy {

    private static final ILog log = LogManager.getLogger(PromotionProxy.class);

    private final static PromocodeServiceClient client = PromocodeServiceClient.getInstance();

    public GetPromotionStrategyResponseType getPromotionStrategy(Integer promotionId) {
        try {
            GetPromotionStrategyRequestType request = new GetPromotionStrategyRequestType();
            request.setPromotionID(promotionId);
            request.setProductLineID(18);
            GetPromotionStrategyResponseType response = client.getPromotionStrategy(request);
            CLogUtil.info("GetPromotionStrategy", "req", request, "res", response);
            return response;
        } catch (Exception e) {
            CLogUtil.warn("GetPromotionStrategy", e);
            return null;
        }
    }

    public GetUserCouponCodeDetailResponseType getUserCouponCodeDetail(String uid, String couponCode) {
        try {
            GetUserCouponCodeDetailRequestType request = new GetUserCouponCodeDetailRequestType();
            request.setUID(uid);
            request.setCouponCode(couponCode);
            GetUserCouponCodeDetailResponseType response = client.getUserCouponCodeDetail(request);
            CLogUtil.info("getUserCouponCodeDetail", "req", request, "res", response);
            return response;
        } catch (Exception e) {
            CLogUtil.warn("getUserCouponCodeDetail", e);
            return null;
        }
    }

    public ReceiveCouponCodeResponseType receiveCoupon(String uid, Integer promotionId) {
        try {
            ReceiveCouponCodeRequestType request = new ReceiveCouponCodeRequestType();
            request.setProductLineID(18);
            request.setPromotionID(promotionId);
            request.setCustomerID(uid);
            ReceiveCouponCodeResponseType response = client.receiveCouponCode(request);
            CLogUtil.info("receiveCoupon", "req", request, "res", response);
            return response;
        } catch (Exception e) {
            CLogUtil.warn("receiveCoupon", e);
            return null;
        }
    }

    public List<CouponCodeStatusItem> getPromotionCoupon(String uid, Integer promotionId) {
        try {
            GetPromotionCouponCodeRequestType request = new GetPromotionCouponCodeRequestType();
            request.setCustomerID(uid);
            //1、已生效 2、已过期 3、已使用 4、已作废，-1：全部 ，不传时默认为-1
            request.setStatus(-1);
            request.setPromotionID(Lists.newArrayList(promotionId));
            GetPromotionCouponCodeResponseType response = client.getPromotionCouponCode(request);
            CLogUtil.info("getPromotionCoupon", "req", request, "res", response);
            if (response != null && CollectionUtils.isNotEmpty(response.getPromotionCouponCodeList())) {
                PromotionCouponCode promotionCouponCode = response.getPromotionCouponCodeList().stream().filter(l -> Objects.equals(l.getPromotionID(), promotionId)).findFirst().orElse(null);
                if (promotionCouponCode != null && CollectionUtils.isNotEmpty(promotionCouponCode.getCouponCodeList())) {
                    //排除已过期的券
                    return promotionCouponCode.getCouponCodeList().stream().filter(l -> !Objects.equals(l.getCouponStatus(), 2)).collect(Collectors.toList());
                }
            }
            return null;
        } catch (Exception e) {
            CLogUtil.warn("getPromotionCoupon", e);
            return null;
        }
    }
}
