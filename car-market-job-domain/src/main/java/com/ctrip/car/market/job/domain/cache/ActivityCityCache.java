package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.ActCityInfoDO;
import com.ctrip.car.market.job.domain.message.ActivityCityMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllActivityCityService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.ACT_CITY_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.IsdActivityCityCacheName;

@Component(ActivityCityCache.Name)
@CreateCacheArea(area = "public")
public class ActivityCityCache extends BasicCacheAbstract<Long, List<ActCityInfoDO>>{

    public static final String Name = IsdActivityCityCacheName;

    @Resource
    private QueryAllActivityCityService service;

    @Resource
    private ActivityCityMessageConvert convert;

    @IncUpdateConsumerByQmq(subject = "car.market.activity.data.change", convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = ACT_CITY_KEY, remotePreLoadName = Name)
    private Cache<Long, List<ActCityInfoDO>> cache;

    @Override
    public ConvertMessage<Long, List<ActCityInfoDO>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Long, List<ActCityInfoDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, List<ActCityInfoDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, List<ActCityInfoDO>> getLoader() {
        return null;
    }
}