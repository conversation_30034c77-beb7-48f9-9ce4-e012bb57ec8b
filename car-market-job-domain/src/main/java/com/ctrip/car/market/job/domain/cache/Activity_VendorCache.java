package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.domain.message.Activity_VendorMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllActivity_VendorService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.ACT_INFO_VENDOR_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.IsdActivity_VendorCacheName;

@Component(Activity_VendorCache.Name)
@CreateCacheArea(area = "public")
public class Activity_VendorCache extends BasicCacheAbstract<Long, List<ActInfoDO>>{

    public static final String Name = IsdActivity_VendorCacheName;

    @Resource
    private QueryAllActivity_VendorService service;

    @Resource
    private Activity_VendorMessageConvert convert;

    @IncUpdateConsumerByQmq(subject = "car.market.activity.data.change", convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = ACT_INFO_VENDOR_KEY, remotePreLoadName = Name)
    private Cache<Long, List<ActInfoDO>> cache;

    @Override
    public ConvertMessage<Long, List<ActInfoDO>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Long, List<ActInfoDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, List<ActInfoDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, List<ActInfoDO>> getLoader() {
        return null;
    }
}
