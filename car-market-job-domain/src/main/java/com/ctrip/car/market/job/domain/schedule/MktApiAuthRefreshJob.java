package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.dto.AccessTokenData;
import com.ctrip.car.market.job.domain.dto.AccessTokenResponse;
import com.ctrip.car.market.job.domain.utils.FastJsonUtil;
import com.ctrip.car.market.job.domain.utils.QConfigUtil;
import com.ctrip.car.market.job.repository.entity.MktapiToken;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.car.market.job.domain.utils.HttpTool;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;

@Component
public class MktApiAuthRefreshJob {

    private static final ILog log = LogManager.getLogger("MktApiAuthRefreshJob");

    @Resource
    private MarketDBService service;

    @QSchedule("MktApi.Auth.Refresh.NEW")
    public void execute() {
        Long CLIENT_ID = Long.parseLong(QConfigUtil.getByFileAndKey("qunarbannerad.properties", "CLIENT_ID"));
        String CLIENT_SECRET = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "CLIENT_SECRET");
        String REFRESH_TOKEN_URL = QConfigUtil.getByFileAndKey("qunarbannerad.properties", "REFRESH_TOKEN_URL");
        if (CLIENT_ID <= 0) return;

        MktapiToken updateEntity = null;
        String refreshToken = null;
        try {
            MktapiToken queryEntity = new MktapiToken();
            queryEntity.setClientId(CLIENT_ID);
            List<MktapiToken> mktapiTokenList = service.queryMktApiToken(queryEntity);
            if (CollectionUtils.isNotEmpty(mktapiTokenList)) {
                updateEntity = mktapiTokenList.get(0);
                refreshToken = mktapiTokenList.get(0).getRefreshToken();
            }
        } catch (SQLException e) {
            log.warn("MktApiAuthRefreshJob.getRefreshToken", e);
        }
        if (updateEntity == null || StringUtils.isBlank(refreshToken)) return;

        AccessTokenResponse accessTokenResponse = null;
        try {
            String responseContent = HttpTool.get_String(String.format(REFRESH_TOKEN_URL, CLIENT_ID, CLIENT_SECRET, refreshToken), "UTF-8");
            log.info("MktApiAuthRefreshJob.invokeMktApi.response", responseContent);
            accessTokenResponse = FastJsonUtil.DeserializeToObject(responseContent, AccessTokenResponse.class);
        } catch (Exception e) {
            log.warn("MktApiAuthRefreshJob.invokeMktApi", e);
        }
        if (accessTokenResponse != null) {
            AccessTokenData accessTokenData = accessTokenResponse.getData();
            try {
                updateEntity.setBizId(accessTokenData.getAccount().getBiz_id());
                updateEntity.setAccessToken(accessTokenData.getAccess_token());
                updateEntity.setAccessTokenExpiresIn(new Timestamp(accessTokenData.getAccess_token_expires_in() * 1000L));
                updateEntity.setRefreshToken(accessTokenData.getRefresh_token());
                updateEntity.setRefreshTokenExpiresIn(new Timestamp(accessTokenData.getRefresh_token_expires_in() * 1000L));
                service.updateMktApiToken(updateEntity);
            } catch (SQLException e) {
                log.warn("MktApiAuthRefreshJob.updateAccessToken", e);
            }
        }

    }
}
