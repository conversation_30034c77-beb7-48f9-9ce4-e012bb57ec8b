package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnUnionPromotionLimitDO;
import com.ctrip.car.market.job.domain.mapper.UnionPromotionLimitMapper;
import com.ctrip.car.market.job.repository.entity.CpnUnionPromotionLimit;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllUnionPromotionLimitService implements CachePreLoader<Integer, List<CpnUnionPromotionLimitDO>>, CacheLoader<Integer, List<CpnUnionPromotionLimitDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private UnionPromotionLimitMapper mapper;

    private Map<Integer, List<CpnUnionPromotionLimitDO>> getAllUnionPromotionLimit() throws Exception {
        List<CpnUnionPromotionLimit> data = service.queryAllUnionPromotionLimit();
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(CpnUnionPromotionLimitDO::getPromotionId));
    }

    @Override
    public List<CpnUnionPromotionLimitDO> load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, List<CpnUnionPromotionLimitDO>> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getAllUnionPromotionLimit();
    }

    @Override
    public Map<Integer, List<CpnUnionPromotionLimitDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllUnionPromotionLimit();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
