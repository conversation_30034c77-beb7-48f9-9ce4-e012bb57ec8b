package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.CpnMergeConditionDO;
import com.ctrip.car.market.job.repository.entity.CpnMergeCondition;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MergeConditionMapper {

    CpnMergeConditionDO to(CpnMergeCondition value);

    List<CpnMergeConditionDO> to(List<CpnMergeCondition> values);
}
