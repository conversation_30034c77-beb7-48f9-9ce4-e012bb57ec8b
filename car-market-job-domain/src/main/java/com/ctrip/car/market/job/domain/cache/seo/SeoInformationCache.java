package com.ctrip.car.market.job.domain.cache.seo;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotInformationDO;
import com.ctrip.car.market.job.domain.cache.BasicCacheAbstract;
import com.ctrip.car.market.job.domain.service.seo.QueryAllSeoHotInformationService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.SEO_INFORMATION_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.SeoInformationCacheName;

@Component(SeoInformationCache.Name)
@CreateCacheArea(area = "public")
public class SeoInformationCache extends BasicCacheAbstract<String, List<SeoHotInformationDO>> {

    public static final String Name = SeoInformationCacheName;

    @Resource
    private QueryAllSeoHotInformationService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = SEO_INFORMATION_KEY, remotePreLoadName = Name)
    private Cache<String, List<SeoHotInformationDO>> cache;

    @Override
    public ConvertMessage<String, List<SeoHotInformationDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<String, List<SeoHotInformationDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<String, List<SeoHotInformationDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<String, List<SeoHotInformationDO>> getLoader() {
        return null;
    }
}
