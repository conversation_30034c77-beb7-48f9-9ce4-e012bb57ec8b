package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.CpnRestrictedConditionDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.RestrictedConditionMapper;
import com.ctrip.car.market.job.repository.entity.CpnRestrictedCondition;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@Component
public class RestrictedConditionMessageConvert extends AbstractConvertMessage<Integer, CpnRestrictedCondition, CpnRestrictedConditionDO> implements ConvertMessage<Integer, CpnRestrictedConditionDO> {

    @Resource
    private RestrictedService service;

    @Resource
    private RestrictedConditionMapper mapper;

    public RestrictedConditionMessageConvert() {
        super(TabelEnum.RestrictedCondition);
    }

    @Override
    public Map<Integer, CpnRestrictedConditionDO> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Integer getKey(String ids) {
        return Integer.valueOf(ids);
    }

    @Override
    public CpnRestrictedCondition getData(Integer id) throws Exception {
        CpnRestrictedCondition obj = service.queryRestrictedCondition(id);
        if (obj == null || !Objects.equals(obj.getIsValid(), 1)) {
            return null;
        }
        return obj;
    }

    @Override
    public CpnRestrictedConditionDO mapper(CpnRestrictedCondition value) {
        return mapper.to(value);
    }
}
