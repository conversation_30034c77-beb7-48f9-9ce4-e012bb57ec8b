package com.ctrip.car.market.job.domain.utils;


import org.apache.commons.lang3.StringUtils;

import javax.xml.datatype.XMLGregorianCalendar;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class DateUtil {

    public static Calendar toCalendar(String dateStr) throws ParseException {
        Calendar calendar;

        try {
            calendar = toCalendar(dateStr, "yyyy-MM-dd HH:mm:ss");
        } catch (ParseException e) {
            calendar = toCalendar(dateStr, "yyyy/MM/dd HH:mm:ss");
        }
        return calendar;
    }

    public static Calendar toCalendar(String dateStr, String pattern) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        Date date = format.parse(dateStr);
        long timeInMillions = date.getTime();
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timeInMillions);

        return calendar;
    }


    public static Calendar getDefaultPickupDateCalendar() {
        // 获取第二天的10:00
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 10);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal;
    }



    public static Calendar getOSDDefaultPickupDateCalendar() {
        // 获取第二天的10:00
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_YEAR, 10);
        cal.set(Calendar.HOUR_OF_DAY, 10);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal;
    }



    public static final String format = "yyyy-MM-dd HH:mm:ss";



    public static Calendar calendar(XMLGregorianCalendar xmlCalendar) {
        if (xmlCalendar == null) return null;
        return xmlCalendar.toGregorianCalendar();
    }

}
