package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.domain.message.ActivityTempIdMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllActivityTempIdService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.ACT_TEMP_ALL_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.IsdActivityTempAllCacheName;

@Component(ActivityTempAllCache.Name)
@CreateCacheArea(area = "public")
public class ActivityTempAllCache extends BasicCacheAbstract<String, List<Long>>{

    public static final String Name = IsdActivityTempAllCacheName;

    @Resource
    private QueryAllActivityTempIdService service;

    @Resource
    private ActivityTempIdMessageConvert convert;

    @IncUpdateConsumerByQmq(subject = "car.market.activity.data.change", convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = ACT_TEMP_ALL_KEY, remotePreLoadName = Name)
    private Cache<String, List<Long>> cache;

    @Override
    public ConvertMessage<String, List<Long>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<String, List<Long>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<String, List<Long>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<String, List<Long>> getLoader() {
        return null;
    }
}
