package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.GiftItemconfigDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.GiftItemconfigMapper;
import com.ctrip.car.market.job.repository.entity.GiftItemconfig;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class GifItemConfigMessageConvert extends AbstractConvertMessage<String, List<GiftItemconfig>, List<GiftItemconfigDO>> implements ConvertMessage<String, List<GiftItemconfigDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private GiftItemconfigMapper mapper;

    public GifItemConfigMessageConvert() {
        super(TabelEnum.GIfItemConfig);
    }

    @Override
    public Map<String, List<GiftItemconfigDO>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public String getKey(String ids) {
        return ids;
    }

    @Override
    public List<GiftItemconfig> getData(String id) throws Exception {
        List<GiftItemconfig> data = service.queryGifItemConfigByNo(id);
        return CollectionUtils.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<GiftItemconfigDO> mapper(List<GiftItemconfig> value) {
        return mapper.to(value);
    }
}
