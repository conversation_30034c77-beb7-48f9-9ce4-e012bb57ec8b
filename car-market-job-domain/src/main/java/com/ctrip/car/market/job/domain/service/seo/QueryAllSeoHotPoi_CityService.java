package com.ctrip.car.market.job.domain.service.seo;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotDestinatioinfoMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotDestinatioinfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllSeoHotPoi_CityService implements CachePreLoader<Integer, List<SeoHotDestinatioinfoDO>>, CacheLoader<Integer, List<SeoHotDestinatioinfoDO>> {

    @Resource
    private MarketDBService service;

    @Resource
    private SeoHotDestinatioinfoMapper mapper;

    private Map<Integer, List<SeoHotDestinatioinfoDO>> getALlHotPoi() throws Exception {
        List<SeoHotDestinatioinfo> data = service.queryAllHotPoi();
        return data.stream().filter(l -> l.getCityId() != null).map(mapper::to).collect(Collectors.groupingBy(SeoHotDestinatioinfoDO::getCityId));
    }

    @Override
    public List<SeoHotDestinatioinfoDO> load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, List<SeoHotDestinatioinfoDO>> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getALlHotPoi();
    }

    @Override
    public Map<Integer, List<SeoHotDestinatioinfoDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getALlHotPoi();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}