package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.CpnGroupRcConditionDO;
import com.ctrip.car.market.job.repository.entity.CpnGroupRcCondition;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface GroupRcConditionMapper {

    List<CpnGroupRcConditionDO> to(List<CpnGroupRcCondition> conditions);

    CpnGroupRcConditionDO to(CpnGroupRcCondition condition);
}
