package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.ActCityInfoDO;
import com.ctrip.car.market.job.common.entity.ActReturnCityInfoDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.ActCityInfoMapper;
import com.ctrip.car.market.job.domain.mapper.ActReturnCityInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.ActCityinfo;
import com.ctrip.car.market.job.repository.entity.activity.ActReturnCityinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class ActivityReturnCityMessageConvert extends AbstractConvertMessage<Long, List<ActReturnCityinfo>, List<ActReturnCityInfoDO>> implements ConvertMessage<Long, List<ActReturnCityInfoDO>> {

    @Resource
    private ActivityService service;

    @Resource
    private ActReturnCityInfoMapper mapper;

    public ActivityReturnCityMessageConvert() {
        super(TabelEnum.ActivityReturnCity);
    }

    @Override
    public Map<Long, List<ActReturnCityInfoDO>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Long getKey(String ids) {
        return Long.valueOf(ids);
    }

    @Override
    public List<ActReturnCityinfo> getData(Long id) throws Exception {
        List<ActReturnCityinfo> data = service.queryActivityReturnCity(id);
        return CollectionUtils.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<ActReturnCityInfoDO> mapper(List<ActReturnCityinfo> value) {
        return mapper.to(value);
    }
}
