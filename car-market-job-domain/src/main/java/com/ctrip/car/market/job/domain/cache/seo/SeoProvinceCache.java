package com.ctrip.car.market.job.domain.cache.seo;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotProvinceinfoDO;
import com.ctrip.car.market.job.domain.cache.BasicCacheAbstract;
import com.ctrip.car.market.job.domain.service.seo.QueryAllSeoHotProvinceService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.SEO_PROVINCE_LEY;
import static com.ctrip.car.market.job.common.consts.CacheName.SeoProvinceCacheName;

@Component(SeoProvinceCache.Name)
@CreateCacheArea(area = "public")
public class SeoProvinceCache extends BasicCacheAbstract<Integer, List<SeoHotProvinceinfoDO>> {

    public static final String Name = SeoProvinceCacheName;

    @Resource
    private QueryAllSeoHotProvinceService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = SEO_PROVINCE_LEY, remotePreLoadName = Name)
    private Cache<Integer, List<SeoHotProvinceinfoDO>> cache;

    @Override
    public ConvertMessage<Integer, List<SeoHotProvinceinfoDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<Integer, List<SeoHotProvinceinfoDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, List<SeoHotProvinceinfoDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, List<SeoHotProvinceinfoDO>> getLoader() {
        return null;
    }
}
