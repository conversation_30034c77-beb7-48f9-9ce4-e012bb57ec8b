package com.ctrip.car.market.job.domain.alipay;

import com.ctrip.car.market.job.domain.config.AlipayMessageConfig;
import com.ctrip.car.market.job.domain.constant.CommonConstant;
import com.ctrip.car.market.job.domain.dto.AlipayCouponMessageDto;
import com.ctrip.car.market.job.domain.dto.MessageData;
import com.ctrip.car.market.job.domain.dto.MessageInfo;
import com.ctrip.car.market.job.domain.enums.MessageTempEnum;
import com.ctrip.car.market.job.domain.proxy.PromotionProxy;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.car.market.job.repository.entity.AlipaySubscribeTemplateinfo;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.soa.platform.account.promocodeservice.data.v1.CouponCodeStatusItem;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Component
public class CouponService {

    private final ILog log = LogManager.getLogger(CouponService.class);

    @Resource
    private AlipayService service;

    @Resource
    private PromotionProxy promotionProxy;

    @Resource
    private AlipayMessageConfig alipayMessageConfig;

    @QSchedule("car.market.alipay.message.coupon.send")
    public void task(Parameter parameter) {
        AlipaySubscribeTemplateinfo templateInfo = service.queryMessageTemplate(MessageTempEnum.Coupon.getType());
        if (templateInfo == null) {
            log.error("CouponService", "template is null");
            Metrics.build().withTag("type", String.valueOf(MessageTempEnum.Coupon.getType())).recordOne("alipayMessageTemplateError");
            return;
        }
        if (CollectionUtils.isEmpty(templateInfo.getParamaterList()) || templateInfo.getParamaterList().size() < 4) {
            log.error("CouponService", "parameter error");
            Metrics.build().withTag("type", String.valueOf(MessageTempEnum.Coupon.getType())).recordOne("alipayMessageTemplateError");
            return;
        }
        if (Optional.ofNullable(templateInfo.getStrategyId()).orElse(0) <= 0) {
            log.error("CouponService", "StrategyId is null");
            Metrics.build().withTag("type", String.valueOf(MessageTempEnum.Coupon.getType())).recordOne("alipayMessageTemplateError");
            return;
        }
        List<String> uidList = service.querySubscribeUser(templateInfo.getTemplateId());
        List<List<String>> uidListPar = Lists.partition(uidList, alipayMessageConfig.getCouponTaskCount());
        CompletableFuture.allOf(uidListPar.stream().map(l -> CompletableFuture.runAsync(() -> this.send(l, templateInfo), CommonConstant.couponExecutorService)).toArray(CompletableFuture[]::new)).join();
    }

    public void send(List<String> uidList, AlipaySubscribeTemplateinfo templateInfo) {
        for (String uid : uidList) {
            Map<String, String> tag = Maps.newHashMap();
            tag.put("uid", uid);
            //判断是否满足发送频次
            if (service.queryRecord(templateInfo.getTemplateId(), uid) != null) {
                log.warn("CouponService", "recordAlreadyExists_" + uid, tag);
                Metrics.build().withTag("result", "0").withTag("errorType", "record").recordOne("alipayMessageCoupon");
                continue;
            }
            MessageInfo messageInfo = buildMessage(uid, templateInfo);
            //判断是否有领取记录
            List<CouponCodeStatusItem> promotionResponse = promotionProxy.getPromotionCoupon(uid, templateInfo.getStrategyId());
            if (CollectionUtils.isEmpty(promotionResponse)) {
                service.sendMessage(messageInfo);
                Metrics.build().withTag("result", "1").withTag("errorType", "success").withTag("sendType", "1").recordOne("alipayMessageCoupon");
            } else {
                //有领取记录取qconfig配置的发送内容
                AlipayCouponMessageDto alipayCouponMessageDto = alipayMessageConfig.getAlipayCouponMessage();
                if (alipayCouponMessageDto != null && StringUtils.isNotEmpty(alipayCouponMessageDto.getKeyword1()) && StringUtils.isNotEmpty(alipayCouponMessageDto.getKeyword2())
                        && StringUtils.isNotEmpty(alipayCouponMessageDto.getKeyword3()) && StringUtils.isNotEmpty(alipayCouponMessageDto.getKeyword4())) {
                    messageInfo.getData().getKeyword1().setValue(alipayCouponMessageDto.getKeyword1());
                    messageInfo.getData().getKeyword2().setValue(alipayCouponMessageDto.getKeyword2());
                    messageInfo.getData().getKeyword3().setValue(alipayCouponMessageDto.getKeyword3());
                    messageInfo.getData().getKeyword4().setValue(alipayCouponMessageDto.getKeyword4());
                    service.sendMessage(messageInfo);
                    Metrics.build().withTag("result", "1").withTag("errorType", "success").withTag("sendType", "0").recordOne("alipayMessageCoupon");
                }
            }
        }
    }

    private MessageInfo buildMessage(String uid, AlipaySubscribeTemplateinfo templateInfo) {
        MessageInfo messageInfo = new MessageInfo();
        messageInfo.setData(new MessageData());
        messageInfo.setUid(uid);
        messageInfo.setTemplateId(templateInfo.getTemplateId());
        messageInfo.setPageUrl(templateInfo.getIsdUrl());
        messageInfo.getData().getKeyword1().setValue(templateInfo.getParamaterList().get(0));
        messageInfo.getData().getKeyword2().setValue(templateInfo.getParamaterList().get(1));
        messageInfo.getData().getKeyword3().setValue(templateInfo.getParamaterList().get(2));
        messageInfo.getData().getKeyword4().setValue(templateInfo.getParamaterList().get(3));
        return messageInfo;
    }
}
