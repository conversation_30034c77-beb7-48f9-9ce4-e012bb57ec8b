package com.ctrip.car.market.job.domain.dto;

import com.ctrip.car.market.job.domain.utils.TraceUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;

import java.util.Collection;
import java.util.Map;

public class TraceData {

    private Map<String, String> tag = Maps.newHashMap();

    public TraceData(String apiName) {
        this.tag.put("apiName", apiName);
    }

    public TraceData addTag(String key, Object value) {
        this.tag.put(key, value == null ? "null" : value.toString());
        return this;
    }

    public TraceData addCity(Object value) {
        this.tag.put("cityId", value == null ? "null" : value.toString());
        return this;
    }

    public TraceData addChannel(Object value) {
        this.tag.put("channelid", value == null ? "null" : value.toString());
        return this;
    }

    public TraceData addSourceFrom(Object value) {
        this.tag.put("sourceFrom", value == null ? "null" : value.toString());
        return this;
    }

    public TraceData addUid(Object value) {
        this.tag.put("uId", value == null ? "null" : value.toString());
        return this;
    }

    public TraceData addCost(Object value) {
        this.tag.put("cost", value == null ? "null" : value.toString());
        return this;
    }

    public TraceData addResult(Collection coll) {
        this.tag.put("result", coll == null || coll.isEmpty() ? "0" : "1");
        return this;
    }

    public TraceData addResult(String value) {
        this.tag.put("result", Strings.isNullOrEmpty(value) ? "0" : "1");
        return this;
    }

    public void push() {
        TraceUtil.save(this.tag);
    }
}
