package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.CpnRestrictedPromotionDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.RestrictedPromotionMapper;
import com.ctrip.car.market.job.repository.entity.CpnRestrictedPromotion;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

@Component
public class RestrictedPromotionMessageConvert extends AbstractConvertMessage<Integer, CpnRestrictedPromotion, CpnRestrictedPromotionDO> implements ConvertMessage<Integer, CpnRestrictedPromotionDO> {

    @Resource
    private RestrictedService service;

    @Resource
    private RestrictedPromotionMapper mapper;

    public RestrictedPromotionMessageConvert() {
        super(TabelEnum.RestrictedPromotion);
    }

    @Override
    public Map<Integer, CpnRestrictedPromotionDO> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Integer getKey(String ids) {
        return Integer.valueOf(ids);
    }

    @Override
    public CpnRestrictedPromotion getData(Integer id) throws Exception {
        return service.queryRestrictedPromotion(id);
    }

    @Override
    public CpnRestrictedPromotionDO mapper(CpnRestrictedPromotion value) {
        return mapper.to(value);
    }
}
