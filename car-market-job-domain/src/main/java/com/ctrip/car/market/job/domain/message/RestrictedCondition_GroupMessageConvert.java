package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.CpnRestrictedConditionDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.RestrictedConditionMapper;
import com.ctrip.car.market.job.repository.entity.CpnRestrictedCondition;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class RestrictedCondition_GroupMessageConvert extends AbstractConvertMessage<Integer, List<CpnRestrictedCondition>, List<CpnRestrictedConditionDO>> implements ConvertMessage<Integer, List<CpnRestrictedConditionDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private RestrictedConditionMapper mapper;

    public RestrictedCondition_GroupMessageConvert() {
        super(TabelEnum.RestrictedCondition_Group);
    }

    @Override
    public Map<Integer, List<CpnRestrictedConditionDO>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Integer getKey(String ids) {
        return Integer.valueOf(ids);
    }

    @Override
    public List<CpnRestrictedCondition> getData(Integer id) throws Exception {
        if (id <= 0) {
            return null;
        }
        List<CpnRestrictedCondition> data = service.queryRestrictedConditionByGroupId(id);
        return CollectionUtils.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<CpnRestrictedConditionDO> mapper(List<CpnRestrictedCondition> value) {
        return mapper.to(value);
    }
}
