package com.ctrip.car.market.job.domain.cache.seo;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.car.market.job.domain.cache.BasicCacheAbstract;
import com.ctrip.car.market.job.domain.service.seo.QueryAllSeoHotPoi_CountryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.SEO_POI_COUNTRY_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.SeoPoiCountryCacheName;

@Component(SeoPoi_CountryCache.Name)
@CreateCacheArea(area = "public")
public class SeoPoi_CountryCache extends BasicCacheAbstract<Integer, List<SeoHotDestinatioinfoDO>> {

    public static final String Name = SeoPoiCountryCacheName;

    @Resource
    private QueryAllSeoHotPoi_CountryService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = SEO_POI_COUNTRY_KEY, remotePreLoadName = Name)
    private Cache<Integer, List<SeoHotDestinatioinfoDO>> cache;

    @Override
    public ConvertMessage<Integer, List<SeoHotDestinatioinfoDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<Integer, List<SeoHotDestinatioinfoDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, List<SeoHotDestinatioinfoDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, List<SeoHotDestinatioinfoDO>> getLoader() {
        return null;
    }
}
