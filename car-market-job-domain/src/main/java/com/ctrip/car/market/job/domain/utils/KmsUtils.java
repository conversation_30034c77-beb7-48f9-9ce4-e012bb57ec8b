package com.ctrip.car.market.job.domain.utils;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.infosec.kms.KmsUtilCacheable;
import com.ctrip.infosec.kms.pojo.KmsPwd;
import com.ctrip.infosec.kms.pojo.KmsResponse;

import java.util.Objects;

public class KmsUtils {

    private static ILog logger = LogManager.getLogger(KmsUtils.class);

    public static String getPwdWithValue(String key) {
        String kmsToken = QConfigUtil.getByFileAndKey("conf.properties", key);
        try {
            logger.warn("getPwdWithValue:"+key+";"+kmsToken);
            KmsResponse<KmsPwd> kmsKeyResponse = KmsUtilCacheable.getPwd(kmsToken);
            if (Objects.nonNull(kmsKeyResponse) && kmsKeyResponse.getCode() == 0) {
                KmsPwd kmsKey = kmsKeyResponse.getResult();
                if (Objects.nonNull(kmsKey)) {
                    return kmsKey.getPwdValue();
                }
            } else {
                logger.warn("getPwdWithValue:"+ kmsKeyResponse != null ? kmsKeyResponse.getMessage() : "null");
            }
            return null;
        } catch (Exception exception) {
            logger.error("getPwdWithValue", exception);
        }
        return null;
    }
}
