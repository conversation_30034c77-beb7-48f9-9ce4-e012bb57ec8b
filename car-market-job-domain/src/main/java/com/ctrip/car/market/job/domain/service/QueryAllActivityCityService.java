package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.ActCityInfoDO;
import com.ctrip.car.market.job.domain.mapper.ActCityInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.ActCityinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllActivityCityService implements CachePreLoader<Long, List<ActCityInfoDO>>, CacheLoader<Long, List<ActCityInfoDO>> {

    @Resource
    private ActivityService service;

    @Resource
    private ActCityInfoMapper mapper;

    private Map<Long, List<ActCityInfoDO>> getAllActivityCity() throws Exception {
        List<ActCityinfo> data = service.queryActivityCity();
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(ActCityInfoDO::getActivityId));
    }

    @Override
    public List<ActCityInfoDO> load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, List<ActCityInfoDO>> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllActivityCity();
    }

    @Override
    public Map<Long, List<ActCityInfoDO>> preLoad(String s, Date date) {
        try {
            return getAllActivityCity();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
