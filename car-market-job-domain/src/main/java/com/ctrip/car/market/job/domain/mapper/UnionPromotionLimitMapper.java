package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.CpnUnionPromotionLimitDO;
import com.ctrip.car.market.job.repository.entity.CpnUnionPromotionLimit;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface UnionPromotionLimitMapper {

    CpnUnionPromotionLimitDO to(CpnUnionPromotionLimit cpnUnionPromotionLimit);

    List<CpnUnionPromotionLimitDO> to(List<CpnUnionPromotionLimit> cpnUnionPromotionLimits);
}
