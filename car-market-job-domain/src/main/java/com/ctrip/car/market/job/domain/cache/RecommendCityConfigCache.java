package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.RecommendCityconfigDO;
import com.ctrip.car.market.job.domain.service.QueryAllRecommendCityConfigService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.RECOMMEND_CITY_CONFIG_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.RecommendCityConfigCacheName;

@Component(RecommendCityConfigCache.Name)
@CreateCacheArea(area = "public")
public class RecommendCityConfigCache extends BasicCacheAbstract<Integer, List<RecommendCityconfigDO>>{

    public static final String Name = RecommendCityConfigCacheName;

    @Resource
    private QueryAllRecommendCityConfigService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = RECOMMEND_CITY_CONFIG_HASH_KEY, remotePreLoadName = Name)
    private Cache<Integer, List<RecommendCityconfigDO>> cache;

    @Override
    public ConvertMessage<Integer, List<RecommendCityconfigDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<Integer, List<RecommendCityconfigDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, List<RecommendCityconfigDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, List<RecommendCityconfigDO>> getLoader() {
        return null;
    }
}
