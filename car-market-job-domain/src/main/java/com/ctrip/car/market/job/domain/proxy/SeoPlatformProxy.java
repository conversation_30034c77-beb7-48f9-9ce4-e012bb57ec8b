package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.car.market.job.domain.utils.JsonUtils;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.ibu.seo.platform.admin.contract.GetSingleSeoInfRequestType;
import com.ctrip.ibu.seo.platform.admin.contract.GetSingleSeoInfResponseType;
import com.ctrip.ibu.seo.platform.admin.contract.InfType;
import com.ctrip.ibu.seo.platform.admin.contract.SeoPlatformManagerClient;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SeoPlatformProxy {

    private final ILog logger = LogManager.getLogger(SeoPlatformProxy.class);

    private final SeoPlatformManagerClient client = SeoPlatformManagerClient.getInstance();

    public String querySeoName(int type, String locale, String code) {
        try {
            GetSingleSeoInfRequestType requestType = new GetSingleSeoInfRequestType();
            switch (type) {
                case 1:
                    requestType.setType(InfType.COUNTRY_ID);
                    break;
                case 2:
                    requestType.setType(InfType.PROVINCE_ID);
                    break;
                case 3:
                    requestType.setType(InfType.BASE_CITY_ID);
                    break;
                case 4:
                    requestType.setType(InfType.AIRPORT_CODE);
                    break;
            }
            requestType.setLocale(locale);
            requestType.setInfCode(code);
            GetSingleSeoInfResponseType responseType = client.getSeoInf(requestType);
            Map<String, String> tag = Maps.newHashMap();
            tag.put("locale", locale);
            tag.put("type", String.valueOf(type));
            tag.put("code", code);
            logger.info("getSeoInf", JsonUtils.toString(responseType), tag);
            String name = null;
            if (responseType != null && responseType.getInfo() != null) {
                name = responseType.getInfo().getOriginName();
                if (StringUtils.isNotEmpty(responseType.getInfo().getSeoName())) {
                    name = responseType.getInfo().getSeoName();
                }
            }
            return name;
        } catch (Exception e) {
            logger.warn("querySeoName", e);
            return null;
        }
    }

}
