package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.seo.SeoHotCountryinfoDO;
import com.ctrip.car.market.job.repository.entity.SeoHotCountryinfo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SeoHotCountryinfoMapper {

    SeoHotCountryinfoDO to(SeoHotCountryinfo value);

    List<SeoHotCountryinfoDO> to(List<SeoHotCountryinfo> values);
}
