package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.GiftItemconfigDO;
import com.ctrip.car.market.job.domain.mapper.GiftItemconfigMapper;
import com.ctrip.car.market.job.repository.entity.GiftItemconfig;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllGifItemConfigService implements CachePreLoader<String, List<GiftItemconfigDO>>, CacheLoader<String, List<GiftItemconfigDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private GiftItemconfigMapper mapper;

    private Map<String, List<GiftItemconfigDO>> getAllGifItemConfig() throws Exception {
        List<GiftItemconfig> data = service.queryAllGifItemConfig();
        if (CollectionUtils.isEmpty(data)) {
            Map<String, List<GiftItemconfigDO>> map = Maps.newHashMap();
            map.put("_flag", null);
            return map;
        }
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(GiftItemconfigDO::getGiftNo));
    }

    @Override
    public List<GiftItemconfigDO> load(String area, String key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<String, List<GiftItemconfigDO>> loadAll(String area, Set<String> keys) throws Throwable {
        return getAllGifItemConfig();
    }

    @Override
    public Map<String, List<GiftItemconfigDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllGifItemConfig();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
