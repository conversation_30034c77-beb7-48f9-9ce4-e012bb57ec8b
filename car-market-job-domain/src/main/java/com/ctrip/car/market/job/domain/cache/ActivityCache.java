package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.domain.message.ActivityMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllActivityService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.ACT_INFO_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.IsdActivityCacheName;

@Component(ActivityCache.Name)
@CreateCacheArea(area = "public")
public class ActivityCache extends BasicCacheAbstract<Long, ActInfoDO> {

    public static final String Name = IsdActivityCacheName;

    @Resource
    private QueryAllActivityService service;

    @Resource
    private ActivityMessageConvert convert;

    @IncUpdateConsumerByQmq(subject = "car.market.activity.data.change", convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = ACT_INFO_KEY, remotePreLoadName = Name)
    private Cache<Long, ActInfoDO> cache;

    @Override
    public ConvertMessage<Long, ActInfoDO> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Long, ActInfoDO> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, ActInfoDO> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, ActInfoDO> getLoader() {
        return null;
    }
}
