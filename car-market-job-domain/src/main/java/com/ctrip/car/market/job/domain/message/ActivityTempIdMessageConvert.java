package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.repository.entity.activity.ActCtriptempinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ActivityTempIdMessageConvert extends AbstractConvertMessage<String, List<Long>, List<Long>> implements ConvertMessage<String, List<Long>> {

    private final ILog log = LogManager.getLogger(ActivityTempIdMessageConvert.class);

    @Resource
    private ActivityService service;

    public ActivityTempIdMessageConvert() {
        super(TabelEnum.ActivityTempId);
    }

    @Override
    public Map<String, List<Long>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public String getKey(String ids) {
        return "ALL";
    }

    @Override
    public List<Long> getData(String id) throws Exception {
        List<ActCtriptempinfo> data = service.queryAllTemp();
        return data.stream().map(ActCtriptempinfo::getTmpId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<Long> mapper(List<Long> value) {
        return value;
    }
}
