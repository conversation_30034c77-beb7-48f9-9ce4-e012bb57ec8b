package com.ctrip.car.market.job.domain.dto;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class GetHostDestinationInfo {
    // 取车国家id
    private String pickupcountryid;
    // 取车城市id
    private String pickupcityid;
    // 取车地点id(机场码)
    private String pickuplocationcode;
    // 预定单量
    private Long advanceorder_cnt;
    // 取车地点名称(机场名称)
    private String pickuplocationname;
    // 取车城市名称
    private String pickupcityname;
    // 取车国家名称
    private String pickupcountryname;
    //自增主键ID
    private Long mysql_id;
    //datec
    private String hive_d;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GetHostDestinationInfo that = (GetHostDestinationInfo) o;
        return Objects.equals(pickuplocationcode, that.pickuplocationcode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pickuplocationcode.toLowerCase());
    }
}
