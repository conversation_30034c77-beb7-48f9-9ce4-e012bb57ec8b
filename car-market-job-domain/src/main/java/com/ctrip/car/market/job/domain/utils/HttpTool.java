package com.ctrip.car.market.job.domain.utils;

import com.ctrip.car.market.job.domain.dto.CommonHttpResponse;
import com.ctrip.car.market.third.support.common.HttpProxyUtils;
import com.ctrip.car.market.third.support.common.ProxyIpHost;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.Map;

public class HttpTool {

    private static final ILog log = LogManager.getLogger("HttpTool");

    static HttpHost host = null;

    static {
        if (System.getProperties().getProperty("os.name").toLowerCase().contains("windows") || System.getProperties().getProperty("os.name").toLowerCase().contains("mac")) {
            host = null;
        } else {
            ProxyIpHost proxyIpHost = HttpProxyUtils.selectProxyHost("http://www.baidu.com");
            host = new HttpHost(proxyIpHost.proxyHost, proxyIpHost.proxyPort);
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(HttpTool.class);
    private static PoolingHttpClientConnectionManager connMgr;

    private static final int MAX_TIMEOUT = 7000;

    static {
        // 设置连接池
        connMgr = new PoolingHttpClientConnectionManager();
        // 设置连接池大小
        connMgr.setMaxTotal(100);
        connMgr.setDefaultMaxPerRoute(connMgr.getMaxTotal());

        RequestConfig.Builder configBuilder = RequestConfig.custom();
        // 设置连接超时
        configBuilder.setConnectTimeout(MAX_TIMEOUT);
        // 设置读取超时
        configBuilder.setSocketTimeout(MAX_TIMEOUT);
        // 设置从连接池获取连接实例的超时
        configBuilder.setConnectionRequestTimeout(MAX_TIMEOUT);
    }

    public static String get_String(String url, String charset) throws Exception {
        CloseableHttpClient httpclient = HttpClients.custom().setProxy(host).build();
        CloseableHttpResponse response = null;
        String result = null;
        HttpGet httpGet = new HttpGet(url);
        try {
            response = httpclient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                result = EntityUtils.toString(entity, charset);
            }
        } finally {
            if (response != null) {
                response.close();
            }
            httpclient.close();
        }
        return result;
    }

    public static String post_String(String url, Object obj, boolean noProxy, String charset) throws Exception {
        CloseableHttpClient httpclient;
        if (Boolean.TRUE.equals(noProxy)) {
            httpclient = HttpClients.custom().build();
        } else {
            httpclient = HttpClients.custom().setProxy(host).build();
        }
        CloseableHttpResponse response = null;
        String result = null;
        HttpPost httpPost = new HttpPost(url);
        try {
            String params = obj instanceof String ? (String) obj : FastJsonUtil.serialize(obj);
            StringEntity se = new StringEntity(params, charset);
            se.setContentType("application/json; charset=" + charset);
            httpPost.setEntity(se);
            response = httpclient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                result = EntityUtils.toString(entity, charset);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.close();
            }
            httpclient.close();
        }
        return result;
    }

    public static CommonHttpResponse doPostWithResponse(String url, Object obj, String charset) throws Exception {
        CloseableHttpClient httpclient = HttpClients.custom().setProxy(host).build();
        CloseableHttpResponse response = null;
        CommonHttpResponse result = new CommonHttpResponse();
        result.setCode(-1);
        HttpPost httpPost = new HttpPost(url);
        try {
            String params = obj instanceof String ? (String) obj : FastJsonUtil.serialize(obj);
            StringEntity se = new StringEntity(params, charset);
            se.setContentType("application/json; charset=" + charset);
            httpPost.setEntity(se);
            response = httpclient.execute(httpPost);
            result.setCode(response.getStatusLine().getStatusCode());
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                result.setResponseEntity(EntityUtils.toString(entity, charset));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.close();
            }
            httpclient.close();
        }
        return result;
    }

    public static String postImage(String url, byte[] image, String subfix, boolean noProxy, Map<String, String> headers) throws Exception {
        logger.warn("postImage===>" + url);
        CloseableHttpClient httpclient;
        if (noProxy) {
            httpclient = HttpClients.custom().build();
        } else {
            httpclient = HttpClients.custom().setProxy(host).build();
        }
        CloseableHttpResponse response = null;
        String result = null;

        HttpPost httpPost = new HttpPost(url);
        try {
            ByteArrayEntity se = new ByteArrayEntity(image);
            if ("jpg".equalsIgnoreCase(subfix)) {
                subfix = "jpeg";
            }
            se.setContentType("image/" + subfix);
            httpPost.setEntity(se);

            if (!CollectionUtils.isEmpty(headers)) {
                for (String header : headers.keySet()) {
                    httpPost.removeHeaders(header);
                    httpPost.addHeader(new BasicHeader(header, headers.get(header)));
                }
            }

            response = httpclient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            return IOUtils.toString(entity.getContent());
        } catch (Exception e) {
            logger.warn("postImage", e);
        } finally {
            if (response != null) {
                response.close();
            }
            httpclient.close();
        }
        return result;
    }

    public static byte[] downloadFileV2(String url, String mediaType) {
        CloseableHttpClient httpclient = HttpClients.custom().setProxy(host).build();
        CloseableHttpResponse response = null;
        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("Content-Type", mediaType);
        try {
            response = httpclient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                return IOUtils.toByteArray(entity.getContent());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
