package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnRestrictedPromotionDO;
import com.ctrip.car.market.job.domain.message.RestrictedPromotionMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllRestrictedPromotionService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.RESTRICTED_PROMOTION_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.RestrictedPromotionCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(RestrictedPromotionCache.Name)
@CreateCacheArea(area = "public")
public class RestrictedPromotionCache extends BasicCacheAbstract<Integer, CpnRestrictedPromotionDO> {

    public static final String Name = RestrictedPromotionCacheName;

    @Resource
    private RestrictedPromotionMessageConvert convert;

    @Resource
    private QueryAllRestrictedPromotionService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = RESTRICTED_PROMOTION_HASH_KEY, remotePreLoadName = Name)
    private Cache<Integer, CpnRestrictedPromotionDO> cache;

    @Override
    public ConvertMessage<Integer, CpnRestrictedPromotionDO> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Integer, CpnRestrictedPromotionDO> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, CpnRestrictedPromotionDO> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, CpnRestrictedPromotionDO> getLoader() {
        return null;
    }
}
