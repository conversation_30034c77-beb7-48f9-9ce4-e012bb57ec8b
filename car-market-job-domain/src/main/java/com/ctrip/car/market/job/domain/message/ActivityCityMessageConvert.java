package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.ActCityInfoDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.ActCityInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.ActCityinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class ActivityCityMessageConvert extends AbstractConvertMessage<Long, List<ActCityinfo>, List<ActCityInfoDO>> implements ConvertMessage<Long, List<ActCityInfoDO>> {

    @Resource
    private ActivityService service;

    @Resource
    private ActCityInfoMapper mapper;

    public ActivityCityMessageConvert() {
        super(TabelEnum.ActivityCity);
    }

    @Override
    public Map<Long, List<ActCityInfoDO>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Long getKey(String ids) {
        return Long.valueOf(ids);
    }

    @Override
    public List<ActCityinfo> getData(Long id) throws Exception {
        List<ActCityinfo> data = service.queryActivityCity(id);
        return CollectionUtils.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<ActCityInfoDO> mapper(List<ActCityinfo> value) {
        return mapper.to(value);
    }
}
