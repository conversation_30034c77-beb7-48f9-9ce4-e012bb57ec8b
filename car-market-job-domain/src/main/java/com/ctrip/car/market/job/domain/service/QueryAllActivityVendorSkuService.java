package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.ActVendorSkuInfoDO;
import com.ctrip.car.market.job.domain.mapper.ActVendorSkuInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.VendorSkuinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class QueryAllActivityVendorSkuService implements CachePreLoader<Long, List<ActVendorSkuInfoDO>>, CacheLoader<Long, List<ActVendorSkuInfoDO>> {

    private final ILog log = LogManager.getLogger(QueryAllActivityService.class);

    @Resource
    private ActivityService service;

    @Resource
    private ActVendorSkuInfoMapper mapper;

    private Map<Long, List<ActVendorSkuInfoDO>> getAllActivityVendorSku() throws Exception {
        List<VendorSkuinfo> data = service.queryVendorSku();
        if (CollectionUtils.isEmpty(data)) {
            Map<Long, List<ActVendorSkuInfoDO>> map = new HashMap<>();
            ActVendorSkuInfoDO item = new ActVendorSkuInfoDO();
            item.setVendorId(0L);
            item.setSkuId(0L);
            item.setStoreId(0L);
            item.setStandardProductId(0L);
            map.put(0L, Lists.newArrayList(item));
            return map;
        }
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(ActVendorSkuInfoDO::getVendorId));
    }

    @Override
    public List<ActVendorSkuInfoDO> load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, List<ActVendorSkuInfoDO>> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllActivityVendorSku();
    }

    @Override
    public Map<Long, List<ActVendorSkuInfoDO>> preLoad(String s, Date date) {
        try {
            return getAllActivityVendorSku();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
