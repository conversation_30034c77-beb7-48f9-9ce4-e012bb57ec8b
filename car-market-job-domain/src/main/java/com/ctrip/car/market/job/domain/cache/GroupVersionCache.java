package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnGroupVersionDO;
import com.ctrip.car.market.job.domain.message.GroupVersionMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllGroupVersionService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.GROUP_VERSION_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.GroupVersionCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(GroupVersionCache.Name)
@CreateCacheArea(area = "public")
public class GroupVersionCache extends BasicCacheAbstract<Integer, List<CpnGroupVersionDO>> {

    public static final String Name = GroupVersionCacheName;

    @Resource
    private GroupVersionMessageConvert convert;

    @Resource
    private QueryAllGroupVersionService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = GROUP_VERSION_HASH_KEY, remotePreLoadName = Name)
    private Cache<Integer, List<CpnGroupVersionDO>> cache;


    @Override
    public ConvertMessage<Integer, List<CpnGroupVersionDO>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Integer, List<CpnGroupVersionDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, List<CpnGroupVersionDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, List<CpnGroupVersionDO>> getLoader() {
        return null;
    }


}
