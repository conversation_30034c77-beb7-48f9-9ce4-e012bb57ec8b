package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnGroupRcConditionDO;
import com.ctrip.car.market.job.domain.mapper.GroupRcConditionMapper;
import com.ctrip.car.market.job.repository.entity.CpnGroupRcCondition;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllGroupRcConditionService implements CachePreLoader<Integer, CpnGroupRcConditionDO>, CacheLoader<Integer, CpnGroupRcConditionDO> {

    @Resource
    private RestrictedService service;

    @Resource
    private GroupRcConditionMapper mapper;

    private Map<Integer, CpnGroupRcConditionDO> getAllGroupRcCondition() throws Exception {
        List<CpnGroupRcCondition> data = service.queryAllGroupRcCondition();
        return data.stream().collect(Collectors.toMap(CpnGroupRcCondition::getGroupID, mapper::to));
    }

    @Override
    public CpnGroupRcConditionDO load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, CpnGroupRcConditionDO> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getAllGroupRcCondition();
    }

    @Override
    public Map<Integer, CpnGroupRcConditionDO> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllGroupRcCondition();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
