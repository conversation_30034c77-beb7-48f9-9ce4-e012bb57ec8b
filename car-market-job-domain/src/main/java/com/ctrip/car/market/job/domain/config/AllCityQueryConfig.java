package com.ctrip.car.market.job.domain.config;

import lombok.Getter;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.List;

@Component
public class AllCityQueryConfig {

    @Getter
    @QConfig("IsdOtherCityQueryConfig.json")
    private List<CommonCityQueryConfig> IsdOtherCityQueryConfig;

    @QConfig("IsdCoreCityQueryConfig.json")
    private List<CommonCityQueryConfig> IsdCoreCityQueryConfig;

    @QConfig("OsdCoreCityQueryConfig.json")
    private List<CommonCityQueryConfig> OsdCoreCityQueryConfig;

    @QConfig("OsdOtherCityQueryConfig.json")
    private List<CommonCityQueryConfig> OsdOtherCityQueryConfig;

    @QConfig("OsdNoOrderCityQueryConfig.json")
    private List<CommonCityQueryConfig> OsdNoOrderCityQueryConfig;

    @QConfig("OsdHotMarketCityQueryConfig.json")
    private List<CommonCityQueryConfig> OsdHotMarketCityQueryConfig;

    @QConfig("OsdQunarCoreCityQueryConfig.json")
    private List<CommonCityQueryConfig> osdQunarCoreCityQueryConfig;

    public List<CommonCityQueryConfig> getIsdOtherCityQueryConfig() {
        return IsdOtherCityQueryConfig;
    }

    public List<CommonCityQueryConfig> getIsdCoreCityQueryConfig() {
        return IsdCoreCityQueryConfig;
    }

    public List<CommonCityQueryConfig> getOsdCoreCityQueryConfig() {
        return OsdCoreCityQueryConfig;
    }

    public List<CommonCityQueryConfig> getOsdOtherCityQueryConfig() {
        return OsdOtherCityQueryConfig;
    }

    public List<CommonCityQueryConfig> getOsdNoOrderCityQueryConfig() {
        return OsdNoOrderCityQueryConfig;
    }

    public List<CommonCityQueryConfig> getOsdHotMarketCityQueryConfig() {
        return OsdHotMarketCityQueryConfig;
    }

    public List<CommonCityQueryConfig> getOsdQunarCoreCityQueryConfig() {
        return osdQunarCoreCityQueryConfig;
    }
}
