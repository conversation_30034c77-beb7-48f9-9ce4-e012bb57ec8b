package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.crossrecommend.service.contract.dto.RecommendProductDTO;
import com.ctrip.car.market.crossrecommend.service.contract.servicetype.CarRecommendProductRequestType;
import com.ctrip.car.market.job.domain.utils.QConfigUtil;
import com.ctrip.car.market.job.domain.utils.RedisUtil;
import com.ctrip.car.osd.framework.common.utils.GzipUtil;
import credis.java.client.util.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class UGCDataManager {

    public Boolean sendToRedis(List<RecommendProductDTO> recommendProducts, CarRecommendProductRequestType requestType) {
        boolean result = false;
        if (CollectionUtils.isEmpty(recommendProducts)) {
            return false;
        }
        // 特殊处理，前端境外城市也传的ISD_C_APP,在此转换
        if (requestType.getAppType() == null || "OSD_C_APP".equalsIgnoreCase(requestType.getAppType())) {
            requestType.setAppType("ISD_C_APP");
        }
        String redisKey = buildISDUGCCarRecommendProductRequestRedisKey(requestType.getPickUpCityId(), requestType.getAppType());
        if (!CollectionUtils.isEmpty(requestType.getVehiclegroupIds()) && requestType.getVehiclegroupIds().contains("88")) {
            // 一口价缓存
            String oneBiteRedisKey = redisKey + "_oneBite_88";
            return send(recommendProducts, result, oneBiteRedisKey);
        }else {
            // 普通资源缓存
            return send(recommendProducts, result, redisKey);
        }
    }


    private boolean send(List<RecommendProductDTO> recommendProducts, boolean result, String redisKey) {
        String responseJson = JsonUtil.toJson(recommendProducts.stream().limit(3).collect(Collectors.toList()));
        // 注释，关注携程即可
        byte[] compress = null;
        try {
            compress = GzipUtil.compress(responseJson);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (Objects.nonNull(compress)) {
            result = RedisUtil.setByte(redisKey, compress, 10 * 24 * 60 * 60);
            RedisUtil.get(redisKey);
        }
        return result;
    }

    /**
     * ugc挂货缓存
     */
    private String buildISDUGCCarRecommendProductRequestRedisKey(Long cityId, String appType) {
        StringBuilder builder = new StringBuilder("UGCBusiness_recommend_product_2");
        if (cityId != null) {
            builder.append("_pcid_").append(cityId);
        }
        if (appType != null) {
            builder.append("_appType_").append(appType);
        }
        return builder.toString();
    }
}
