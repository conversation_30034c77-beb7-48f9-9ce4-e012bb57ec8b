package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.CpnLabelGroupDO;
import com.ctrip.car.market.job.repository.entity.CpnLabelGroup;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface LabelGroupMapper {

    CpnLabelGroupDO to(CpnLabelGroup value);

    List<CpnLabelGroupDO> to(List<CpnLabelGroup> values);
}
