package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.common.entity.act.ActivityTempContent;
import com.ctrip.car.market.job.common.entity.ActTempInfoDO;
import com.ctrip.car.market.job.domain.mapper.ActTempInfoMapper;
import com.ctrip.car.market.job.domain.utils.JsonUtils;
import com.ctrip.car.market.job.repository.entity.activity.ActCtriptempinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class QueryAllActivityTempService implements CachePreLoader<Long, ActTempInfoDO>, CacheLoader<Long, ActTempInfoDO> {

    private final ILog log = LogManager.getLogger(QueryAllActivityService.class);

    @Resource
    private ActivityService service;

    @Resource
    private ActTempInfoMapper mapper;

    private Map<Long, ActTempInfoDO> getAllActivityTemp() throws Exception {
        List<ActCtriptempinfo> data = service.queryAllTemp();
        for (ActCtriptempinfo item : data) {
            try {
                item.setContent(JsonUtils.toObject(item.getTempContent(), ActivityTempContent.class));
            } catch (Exception e) {
                log.warn("getAllActivityTemp", e);
            }
        }
        return data.stream().map(mapper::to).filter(l -> Objects.nonNull(l.getContent())).collect(Collectors.toMap(ActTempInfoDO::getTmpId, l -> l, (k1, k2) -> k1));
    }

    @Override
    public ActTempInfoDO load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, ActTempInfoDO> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllActivityTemp();
    }

    @Override
    public Map<Long, ActTempInfoDO> preLoad(String s, Date date) {
        try {
            return getAllActivityTemp();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
