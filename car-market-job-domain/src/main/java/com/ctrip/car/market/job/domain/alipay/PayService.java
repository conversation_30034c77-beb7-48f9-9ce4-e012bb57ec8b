package com.ctrip.car.market.job.domain.alipay;

import com.ctrip.car.market.job.domain.config.AlipayMessageConfig;
import com.ctrip.car.market.job.domain.constant.CommonConstant;
import com.ctrip.car.market.job.domain.dto.MessageData;
import com.ctrip.car.market.job.domain.dto.MessageInfo;
import com.ctrip.car.market.job.domain.enums.MessageTempEnum;
import com.ctrip.car.market.job.domain.utils.DateUtil;
import com.ctrip.car.market.job.domain.utils.LanguageUtils;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.car.market.job.repository.entity.AlipaySubscribeTemplateinfo;
import com.ctrip.car.order.context.query.offline.OrderDetail;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Objects;

@Component
public class PayService extends AlipayOrderService {

    @Resource
    private AlipayService service;

    @Resource
    private AlipayMessageConfig alipayMessageConfig;

    @Override
    protected MessageInfo buildMessage(Long orderId, OrderDetail orderDetail) {
        AlipaySubscribeTemplateinfo templateInfo = service.queryMessageTemplate(MessageTempEnum.Payment.getType());
        if (templateInfo == null) {
            Metrics.build().withTag("type", String.valueOf(MessageTempEnum.Payment.getType())).recordOne("alipayMessageTemplateError");
            return null;
        }
        String url = Objects.equals(CommonConstant.ISD, orderDetail.getProductInfo().getProductCategoryId()) ? templateInfo.getIsdUrl() : templateInfo.getOsdUrl();
        MessageInfo messageInfo = new MessageInfo();
        messageInfo.setData(new MessageData());
        messageInfo.setUid(orderDetail.getCustomerInfo().getUid());
        messageInfo.setTemplateId(templateInfo.getTemplateId());
        messageInfo.setPageUrl(LanguageUtils.format(url, orderId));
        messageInfo.getData().getKeyword1().setValue(orderId.toString());
        messageInfo.getData().getKeyword2().setValue(orderDetail.getTotalAmount().intValue() + alipayMessageConfig.getDiscountSuffix());
        messageInfo.getData().getKeyword3().setValue(orderDetail.getCarTypeInfo().getCarType());
        messageInfo.getData().getKeyword4().setValue(getRemainingTime(orderDetail));

        if (messageInfo.getData().getKeyword4() == null) {
            log.warn("PayService", "remainingTime null");
            return null;
        }
        return messageInfo;
    }

    private String getRemainingTime(OrderDetail orderDetail) {
        try {
            Calendar orderDate = DateUtil.toCalendar(orderDetail.getOrderDate().getDatetime());
            Calendar currDate = Calendar.getInstance();
            long m = (currDate.getTimeInMillis() - orderDate.getTimeInMillis()) / 1000 / 60;
            if (m > 30) {
                return null;
            }
            return 30 - m + alipayMessageConfig.getPayTime();
        } catch (Exception e) {
            log.warn("PayService", e);
            return null;
        }
    }
}
