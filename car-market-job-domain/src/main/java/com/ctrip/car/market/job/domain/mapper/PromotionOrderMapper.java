package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.CpnPromotionOrderDO;
import com.ctrip.car.market.job.repository.entity.CpnPromotionOrder;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PromotionOrderMapper {

    CpnPromotionOrderDO to(CpnPromotionOrder cpnPromotionOrder);

    List<CpnPromotionOrderDO> to(List<CpnPromotionOrder> cpnPromotionOrders);
}
