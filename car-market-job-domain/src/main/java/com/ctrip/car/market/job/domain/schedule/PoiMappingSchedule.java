package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.proxy.IgtGeoServiceProxy;
import com.ctrip.car.market.job.domain.utils.RedisUtil;
import com.ctrip.car.market.job.repository.entity.CarPoiMapping;
import com.ctrip.car.market.job.repository.service.PoiService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.igt.geo.interfaces.dto.FuzzyAddressDTO;
import com.ctrip.igt.geo.interfaces.message.QueryFuzzyAddressResponseType;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Component
public class PoiMappingSchedule {

    private static final ILog log = LogManager.getLogger(PoiMappingSchedule.class);

    @Resource
    private PoiService poiService;

    @Resource
    private IgtGeoServiceProxy igtGeoServiceProxy;

    private final static String KEY = "car.market.query.rule.poi.mappings.";

    private final static int expireTime = 60 * 60 * 24 * 30;

    private static final int POOL_SIZE = 4;

    private final ExecutorService executorService = Executors.newFixedThreadPool(POOL_SIZE);

    @QSchedule("car.market.common.job.poi.mapping.job")
    public void task() {
        try {
            long poiId = 0;
            List<CarPoiMapping> list;
            do {
                list = poiService.query(poiId);
                if (CollectionUtils.isNotEmpty(list)) {
                    poiId = list.stream().max(Comparator.comparing(CarPoiMapping::getPoiId)).get().getPoiId();
                    doBusiness(list);
                }
            } while (CollectionUtils.isNotEmpty(list));
        } catch (Exception e) {
            log.error("PoiMappingSchedule", e);
        }
    }

    private void doBusiness(List<CarPoiMapping> dataList) {
        List<List<CarPoiMapping>> list = Lists.partition(dataList, POOL_SIZE);
        for (List<CarPoiMapping> li : list) {
            List<CompletableFuture<Void>> taskList = Lists.newArrayList();
            taskList.add(CompletableFuture.runAsync(() -> this.matching(li), executorService));
            CompletableFuture.allOf(taskList.stream().toArray(CompletableFuture[]::new)).join();
        }
    }

    public void matching(List<CarPoiMapping> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            for (CarPoiMapping mapping : list) {
                try {
                    String carPlaceId = queryCarPlaceId(mapping.getPoiId(), mapping.getCityId(), mapping.getPoiName());
                    log.info("PoiMappingSchedule", "poiId: " + mapping.getPoiId() + ", carPlaceId: " + carPlaceId);
                    if (StringUtils.isNotBlank(carPlaceId)) {
                        mapping.setCarPlaceId(carPlaceId);
                        mapping.setIsActive(true);
                        if (poiService.update(mapping)) {
                            RedisUtil.set(KEY + mapping.getPoiId(), carPlaceId, expireTime);
                        }
                    }
                } catch (Exception e) {
                    log.warn("matching", e);
                    log.warn("matching", mapping.getPoiId().toString());
                }
            }
        }
    }

    private String queryCarPlaceId(Long poiID, Long cityId, String poiName) {
        if (poiID == null || cityId == null || StringUtils.isEmpty(poiName)) {
            return null;
        }
        try {
            QueryFuzzyAddressResponseType response = igtGeoServiceProxy.queryFuzzyAddress(cityId, poiName);
            if (response != null && CollectionUtils.isNotEmpty(response.getAddressList())) {
                FuzzyAddressDTO addressDTO = response.getAddressList().get(0);
                if (addressDTO.getAddressExtInfo() != null && StringUtils.isNotBlank(addressDTO.getAddressExtInfo().getCarPlaceId())) {
                    return addressDTO.getAddressExtInfo().getCarPlaceId();
                }
            }
            return null;
        } catch (Exception e) {
            log.warn("queryCarPlaceId", e);
            return null;
        }
    }

}
