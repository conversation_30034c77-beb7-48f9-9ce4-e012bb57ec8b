package com.ctrip.car.market.job.domain.service.seo;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotProvinceinfoDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotProvinceinfoMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotProvinceinfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllSeoHotProvinceService implements CachePreLoader<Integer, List<SeoHotProvinceinfoDO>>, CacheLoader<Integer, List<SeoHotProvinceinfoDO>> {

    @Resource
    private MarketDBService service;

    @Resource
    private SeoHotProvinceinfoMapper mapper;

    private Map<Integer, List<SeoHotProvinceinfoDO>> getAllHotProvince() throws Exception {
        List<SeoHotProvinceinfo> data = service.queryAllSeoHotProvince();
        return data.stream().filter(l -> l.getProvinceId() != null).map(mapper::to).collect(Collectors.groupingBy(SeoHotProvinceinfoDO::getProvinceId));
    }

    @Override
    public List<SeoHotProvinceinfoDO> load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, List<SeoHotProvinceinfoDO>> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getAllHotProvince();
    }

    @Override
    public Map<Integer, List<SeoHotProvinceinfoDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllHotProvince();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
