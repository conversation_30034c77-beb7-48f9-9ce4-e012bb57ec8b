package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.seo.SeoHotProvinceinfoDO;
import com.ctrip.car.market.job.repository.entity.SeoHotProvinceinfo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SeoHotProvinceinfoMapper {

    SeoHotProvinceinfoDO to(SeoHotProvinceinfo value);

    List<SeoHotProvinceinfoDO> to(List<SeoHotProvinceinfo> value);
}
