package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.ActTempInfoDO;
import com.ctrip.car.market.job.repository.entity.activity.ActCtriptempinfo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ActTempInfoMapper {

    ActTempInfoDO to(ActCtriptempinfo value);

    List<ActTempInfoDO> to(List<ActCtriptempinfo> values);
}
