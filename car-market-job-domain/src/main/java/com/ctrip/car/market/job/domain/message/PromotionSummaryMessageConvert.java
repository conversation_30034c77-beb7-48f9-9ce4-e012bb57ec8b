package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.CpnPromotionSummaryDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.PromotionSummaryMapper;
import com.ctrip.car.market.job.repository.entity.CpnPromotionSummary;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class PromotionSummaryMessageConvert extends AbstractConvertMessage<Integer, List<CpnPromotionSummary>, List<CpnPromotionSummaryDO>> implements ConvertMessage<Integer, List<CpnPromotionSummaryDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private PromotionSummaryMapper mapper;

    public PromotionSummaryMessageConvert() {
        super(TabelEnum.PromotionSummary);
    }

    @Override
    public Map<Integer, List<CpnPromotionSummaryDO>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Integer getKey(String ids) {
        return Integer.valueOf(ids);
    }

    @Override
    public List<CpnPromotionSummary> getData(Integer id) throws Exception {
        List<CpnPromotionSummary> data = service.queryPromotionSummaryByActivityId(id);
        return CollectionUtils.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<CpnPromotionSummaryDO> mapper(List<CpnPromotionSummary> value) {
        return mapper.to(value);
    }
}
