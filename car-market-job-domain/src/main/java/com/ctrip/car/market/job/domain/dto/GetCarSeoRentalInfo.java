package com.ctrip.car.market.job.domain.dto;

import lombok.Data;

import java.util.Objects;

@Data
public class GetCarSeoRentalInfo {
    // 取车国家id
    private String pickcountryid;
    // 取车国家名称
    private String pickcountryname;
    // 取车城市id
    private String pickcityid;
    // 取车城市名称
    private String pickcityname;
    // 取车地点id(机场码)
    private String pickuplocationcode;


    //自增主键ID
    private Long mysql_id;
    //date
    private String hive_d;

    // 服务商id
    private String vendorid;
    // 服务商名称
    private String vendorname;
    // 车型组id
    private String vehivlegroupid;
    // 车型组名称
    private String vehivlegroupname;
    // 租期
    private String common_period;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GetCarSeoRentalInfo that = (GetCarSeoRentalInfo) o;
        return Objects.equals(pickuplocationcode, that.pickuplocationcode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pickuplocationcode);
    }
}
