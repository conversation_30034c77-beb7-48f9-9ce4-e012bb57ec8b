package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.common.entity.act.CustomContent;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.domain.mapper.ActInfoMapper;
import com.ctrip.car.market.job.domain.utils.JsonUtils;
import com.ctrip.car.market.job.repository.entity.activity.ActCtripactinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllActivity_VendorService implements CachePreLoader<Long, List<ActInfoDO>>, CacheLoader<Long, List<ActInfoDO>> {

    private final ILog log = LogManager.getLogger(QueryAllActivityService.class);

    @Resource
    private ActivityService service;

    @Resource
    private ActInfoMapper mapper;

    private Map<Long, List<ActInfoDO>> getAllVendorActivity() throws Exception {
        List<ActCtripactinfo> data = service.queryAllActivity();
        for (ActCtripactinfo item : data) {
            try {
                item.setContent(JsonUtils.toObject(item.getCustomContent(), CustomContent.class));
            } catch (Exception e) {
                log.warn("getAllActivity", e);
            }
        }
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(ActInfoDO::getVendorId));
    }

    @Override
    public List<ActInfoDO> load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, List<ActInfoDO>> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllVendorActivity();
    }

    @Override
    public Map<Long, List<ActInfoDO>> preLoad(String s, Date date) {
        try {
            return getAllVendorActivity();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
