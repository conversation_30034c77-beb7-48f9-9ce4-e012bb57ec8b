package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.MsgAllianceinfoDO;
import com.ctrip.car.market.job.domain.service.QueryAllMsgAllianceinfoService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.MSG_ALLIANCEINFO_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.MsgAllianceinfoCacheName;

@Component(MsgAllianceinfoCache.Name)
@CreateCacheArea(area = "public")
public class MsgAllianceinfoCache extends BasicCacheAbstract<String, List<MsgAllianceinfoDO>> {

    public static final String Name = MsgAllianceinfoCacheName;

    @Resource
    private QueryAllMsgAllianceinfoService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = MSG_ALLIANCEINFO_HASH_KEY, remotePreLoadName = Name)
    private Cache<String, List<MsgAllianceinfoDO>> cache;


    @Override
    public ConvertMessage<String, List<MsgAllianceinfoDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<String, List<MsgAllianceinfoDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<String, List<MsgAllianceinfoDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<String, List<MsgAllianceinfoDO>> getLoader() {
        return null;
    }
}
