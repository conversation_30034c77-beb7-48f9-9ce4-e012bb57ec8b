package com.ctrip.car.market.job.domain.alipay;

import com.ctrip.car.market.job.domain.config.AlipayMessageConfig;
import com.ctrip.car.market.job.domain.constant.CommonConstant;
import com.ctrip.car.market.job.domain.dto.MessageInfo;
import com.ctrip.car.market.job.domain.proxy.AlipayProxy;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.domain.utils.RedisUtil;
import com.ctrip.car.market.job.repository.entity.AlipaySendmessageRecord;
import com.ctrip.car.market.job.repository.entity.AlipaySubscribeTemplateinfo;
import com.ctrip.car.market.job.repository.entity.AlipaySubscribeUserInfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.market.publicplatform.soaclient.AlipayTemplateMessageSendResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class AlipayService {

    private final ILog log = LogManager.getLogger(AlipayService.class);

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private AlipayMessageConfig alipayMessageConfig;

    @Resource
    private AlipayProxy alipayProxy;

    /**
     * 查询模版订阅用户
     */
    public List<String> querySubscribeUser(String templateId) {
        //如果有配置白名单，返回白名单
        List<String> witheList = alipayMessageConfig.getWhiteList();
        if (CollectionUtils.isNotEmpty(witheList)) {
            return witheList.stream().distinct().collect(Collectors.toList());
        }
        try {
            List<AlipaySubscribeUserInfo> result = marketDBService.querySubscribeUser(templateId);
            return result.stream().map(AlipaySubscribeUserInfo::getCtripUid).distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("querySubscribeUser", e);
            return null;
        }
    }

    public boolean sendMessage(MessageInfo messageInfo) {
        AlipayTemplateMessageSendResponse messageSendResponse = alipayProxy.sendMessage(messageInfo);
        if (messageSendResponse != null && Objects.equals(messageSendResponse.getCode(), 0)) {
            //记录消息
            addRecord(messageInfo.getTemplateId(), messageInfo.getUid());
            return true;
        }
        return false;
    }


    /**
     * uid是否在白名单
     */
    public boolean isWitheList(String uid) {
        //为空代表不限制
        if (CollectionUtils.isEmpty(alipayMessageConfig.getWhiteList())) {
            return true;
        }
        return alipayMessageConfig.getWhiteList().stream().anyMatch(l -> StringUtils.equalsIgnoreCase(l, uid));
    }

    public AlipaySubscribeTemplateinfo queryMessageTemplate(Integer type) {
        try {
            AlipaySubscribeTemplateinfo templateInfo = marketDBService.queryMessageTemplate(type);
            if(templateInfo == null) {
                return null;
            }
            //过期
            if (templateInfo.getExpirationTime() != null && templateInfo.getExpirationTime().getTime() <= System.currentTimeMillis()) {
                return null;
            }
            templateInfo.setParamaterList(JsonUtil.parseArray(templateInfo.getTemplateContent(), String.class));
            return templateInfo;
        } catch (Exception e) {
            log.warn("queryMessageTemplate", e);
            return null;
        }
    }

    /**
     * 添加发送记录
     */
    public void addRecord(String templateId, String uid) {
        try {
            marketDBService.insertMessage(uid, templateId);
            String redisKey = String.format(CommonConstant.ALIPAY_MESSAGE_FORMAT, uid, templateId).toLowerCase();
            RedisUtil.set(redisKey, uid, 3600 * alipayMessageConfig.getRecordCacheExpireHour());
        } catch (Exception e) {
            log.warn("addRecord", e);
        }
    }

    /**
     * 查询发放记录
     */
    public String queryRecord(String templateId, String uid) {
        try {
            if (alipayMessageConfig.isRecordCache()) {
                String redisKey = String.format(CommonConstant.ALIPAY_MESSAGE_FORMAT, uid, templateId).toLowerCase();
                return RedisUtil.get(redisKey);
            }
            AlipaySendmessageRecord record = marketDBService.queryMessage(uid, templateId);
            if (record == null) {
                return null;
            }
            long hour = (System.currentTimeMillis() - record.getDatachangeLasttime().getTime()) / 1000 / 3600;
            if (hour >= alipayMessageConfig.getRecordCacheExpireHour()) {
                return null;
            }
            return record.getUid();
        } catch (Exception e) {
            log.warn("queryRecord", e);
            return null;
        }
    }

    public boolean isSendActivity() {
        if (alipayMessageConfig.isSendActivity()) {
            return true;
        }
        return StringUtils.isEmpty(RedisUtil.get(CommonConstant.ALIPAY_ACTIVITY_SEND));
    }
}
