package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.customer.common.util.SharkUtil;
import com.ctrip.car.market.BaseRequest;
import com.ctrip.car.market.KeyValueDTO;
import com.ctrip.car.market.crossrecommend.service.contract.dto.RecommendProductDTO;
import com.ctrip.car.market.crossrecommend.service.contract.servicetype.CarRecommendProductRequestType;
import com.ctrip.car.market.job.domain.config.CommonCityQueryConfig;
import com.ctrip.car.market.job.domain.constant.CommonConstant;
import com.ctrip.car.market.job.domain.proxy.CarCrossRecommendedServiceClientProxy;
import com.ctrip.car.market.job.domain.proxy.DestProxy;
import com.ctrip.car.market.job.domain.utils.DateUtil;
import com.ctrip.car.market.job.repository.dao.CarKalabCityDao;
import com.ctrip.car.market.job.repository.dao.PkgRealtimedataDao;
import com.ctrip.car.market.job.repository.entity.CarKalabCity;
import com.ctrip.car.market.job.repository.entity.PkgRealtimedata;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
public class ProductManagerService {

    private static final ILog log = LogManager.getLogger(ProductManagerService.class);

    @Autowired
    private CarCrossRecommendedServiceClientProxy carCrossRecommendedServiceClientProxy;


    @Autowired
    public PkgRealtimedataDao pkgRealtimedataDao;

    @Autowired
    private CarKalabCityDao carKalabCityDao;

    @Resource
    private DestProxy destProxy;

    public boolean start(List<CommonCityQueryConfig> cityQueryConfigList) {
        try {
            if (CollectionUtils.isEmpty(cityQueryConfigList) || cityQueryConfigList.stream().anyMatch(x -> StringUtils.isBlank(x.getPickUpCityIds()))) {
                return false;
            }
            for (CommonCityQueryConfig item : cityQueryConfigList) {
                List<Long> pCityIds = Arrays.stream(item.getPickUpCityIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
                // 去攻略id查询图片
                Map<Long, String> cityImage = destProxy.getCityImage(pCityIds);
                if (StringUtils.isNotEmpty(item.getReturnCityIds())) {
                    List<Long> rCityIds = Arrays.stream(item.getPickUpCityIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
                    for (Long cityId : pCityIds) {
                        for (Long rCid : rCityIds) {
                            execQuery(item, cityId, rCid, cityImage);
                            if(item.getSleepMillisecond()!=null &&item.getSleepMillisecond()>0)
                            {
                                Thread.sleep(item.getSleepMillisecond());
                            }
                        }
                    }
                } else {
                    for (Long cityId : pCityIds) {
                        execQuery(item, cityId, cityId, cityImage);
                        if(item.getSleepMillisecond()!=null &&item.getSleepMillisecond()>0)
                        {
                            Thread.sleep(item.getSleepMillisecond());
                        }
                    }
                }
            }
        } catch (Exception ex) {
            return false;
        }
        return true;
    }

    private void execQuery(CommonCityQueryConfig item, Long pCid, Long rCid, Map<Long, String> cityImage) {
        CarRecommendProductRequestType requestType = buildCarRecommendProductRequestType(item, pCid, rCid);
        List<RecommendProductDTO> responseType = carCrossRecommendedServiceClientProxy.queryProductByRequest(requestType);
        if (CollectionUtils.isNotEmpty(responseType)) {
            updateDBData(responseType, requestType, cityImage);
            log.info("ProductManagerService", "execQuery is not null:" + pCid + "-" + responseType.size());
        }
    }

    private CarRecommendProductRequestType buildCarRecommendProductRequestType(CommonCityQueryConfig tuAdvanceQueryConfig, Long pCid, Long rCid) {
        CarRecommendProductRequestType carRecommendProductRequestType = new CarRecommendProductRequestType();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setSourceFrom(tuAdvanceQueryConfig.getAppType());
        baseRequest.setHost(tuAdvanceQueryConfig.getHost());
        baseRequest.setSourceCountryId(tuAdvanceQueryConfig.getSourceCountryId());
        baseRequest.setBizType(tuAdvanceQueryConfig.getBusinessType());
        baseRequest.setChannelId(tuAdvanceQueryConfig.getChannelId());
        baseRequest.setBizType(tuAdvanceQueryConfig.getBusinessType());
        baseRequest.setRequestId(UUID.randomUUID().toString());
        baseRequest.setHost(tuAdvanceQueryConfig.getHost());
        baseRequest.setSourceFrom(tuAdvanceQueryConfig.getSourceFrom());
        baseRequest.setLocale(tuAdvanceQueryConfig.getLocale());
        baseRequest.setCurrencyCode(tuAdvanceQueryConfig.getCurrencyCode());
        baseRequest.setHost(tuAdvanceQueryConfig.getHost());
        List<KeyValueDTO> list = new ArrayList<>();
        list.add(new KeyValueDTO() {{
            this.setDisplayName("4");
            this.setKey("orignScenes");
        }});

        if (StringUtils.isNotBlank(tuAdvanceQueryConfig.getCacheMinutes())) {
            list.add(new KeyValueDTO() {{
                this.setDisplayName(tuAdvanceQueryConfig.getCacheMinutes());
                this.setKey("cacheMinutes");
            }});
        }
        baseRequest.setExtList(list);
        carRecommendProductRequestType.setBaseRequest(baseRequest);
        carRecommendProductRequestType.setPickUpCityId(pCid);
        carRecommendProductRequestType.setReturnCityId(rCid);
        carRecommendProductRequestType.setOrderId(tuAdvanceQueryConfig.getOrderId());
        Calendar pickUpDate = null;
        if (StringUtils.isNotEmpty(tuAdvanceQueryConfig.getPickupDate())  ) {
            try {
                pickUpDate = DateUtil.toCalendar(tuAdvanceQueryConfig.getPickupDate());
            } catch (ParseException e) {
                log.error("buildCarRecommendProductRequestType", e);
            }
        }
        Calendar returnDate = null;
        if (tuAdvanceQueryConfig.getRentDays() != null && tuAdvanceQueryConfig.getRentDays() > 0) {
            // qconfig不配置取车时间的话，就用默认取车时间，然后加上租期即可，如果没有租期，就走全流程默认取还车时间 有取车时间必须有租期
            if (pickUpDate == null) {
                if (tuAdvanceQueryConfig.getBusinessType() == 34) {
                    pickUpDate = DateUtil.getOSDDefaultPickupDateCalendar();
                } else {
                    pickUpDate = DateUtil.getDefaultPickupDateCalendar();
                }
            }
            returnDate = Calendar.getInstance();
            returnDate.setTime(pickUpDate.getTime());
            returnDate.add(Calendar.DAY_OF_YEAR, tuAdvanceQueryConfig.getRentDays());
        }
        carRecommendProductRequestType.setPickUpTime(pickUpDate);
        carRecommendProductRequestType.setReturnTime(returnDate);
        carRecommendProductRequestType.setPageSize(tuAdvanceQueryConfig.getPageSize());
        carRecommendProductRequestType.setPageIndex(1);
        carRecommendProductRequestType.setSort(tuAdvanceQueryConfig.getSort());
        carRecommendProductRequestType.setAppType(tuAdvanceQueryConfig.getAppType());
        // 添加vehiclegroupIds，一口价
        if (StringUtils.isNotEmpty(tuAdvanceQueryConfig.getVehicleGroupIds())) {
            carRecommendProductRequestType.setVehiclegroupIds(Arrays.asList(tuAdvanceQueryConfig.getVehicleGroupIds().split(",")));
        }
        return carRecommendProductRequestType;
    }


    private int updateDBData(List<RecommendProductDTO> result, CarRecommendProductRequestType requestType, Map<Long, String> cityImage) {
        try {
            if (CollectionUtils.isNotEmpty(result)) {
                List<PkgRealtimedata> list = new ArrayList<>();
                RecommendProductDTO tmpInfo = result.get(0);
                UpdateCityPrice(requestType.getPickUpCityId(), tmpInfo, cityImage);
                String transName = SharkUtil.getSharkKeyByLanguageWithAppId(CommonConstant.APP_ID_INT, "key.product.trans.auto.name", "");
                try {
                    list.addAll(result.stream().filter(distinctByKey(x -> x.getProductName())).map(x -> {
                        PkgRealtimedata tmp = new PkgRealtimedata();
                        tmp.setDatachangeLasttime(new Timestamp(System.currentTimeMillis()));
                        tmp.setStatus(1);
                        tmp.setAutomatic((StringUtils.isNotEmpty(x.getTrans()) && x.getTrans().contains(transName)) ? 0 : 1);
                        tmp.setBizVendorCode(x.getVendorId() == null ? "" : x.getVendorId() + "");
                        tmp.setBusinessType(requestType.getBaseRequest().getBizType());
                        tmp.setTakeCarWay(x.getTakeWay());
                        tmp.setCityId(requestType.getPickUpCityId().intValue());
                        tmp.setDisplacement(x.getDisplacement());
                        if (requestType.getPickUpTime() != null) {
                            tmp.setStartDate(new Timestamp(requestType.getPickUpTime().getTimeInMillis()));
                        } else {
                            Calendar today = Calendar.getInstance();
                            if (35 == requestType.getBaseRequest().getBizType()) {
                                today.add(Calendar.DATE, 1);
                            } else {
                                today.add(Calendar.DATE, 10);
                            }
                            ;
                            today.set(Calendar.HOUR, 10);
                            today.set(Calendar.MINUTE, 0);
                            today.set(Calendar.SECOND, 0);
                            today.set(Calendar.MILLISECOND, 0);
                            tmp.setStartDate(new Timestamp(today.getTimeInMillis()));
                        }
                        if (requestType.getReturnTime() != null) {
                            tmp.setEndDate(new Timestamp(requestType.getReturnTime().getTimeInMillis()));
                        } else {
                            Calendar afterTomorrow = Calendar.getInstance();
                            if (35 == requestType.getBaseRequest().getBizType()) {
                                afterTomorrow.add(Calendar.DATE, 3);
                            } else {
                                afterTomorrow.add(Calendar.DATE, 17);
                            }
                            afterTomorrow.set(Calendar.HOUR, 10);
                            afterTomorrow.set(Calendar.MINUTE, 0);
                            afterTomorrow.set(Calendar.SECOND, 0);
                            afterTomorrow.set(Calendar.MILLISECOND, 0);
                            tmp.setEndDate(new Timestamp(afterTomorrow.getTimeInMillis()));
                        }

                        tmp.setListPageAppUrl(x.getAppUrl());
                        tmp.setListPageH5Url(x.getH5Url());
                        tmp.setListPagePcUrl(x.getPcUrl());
                        tmp.setVendorLogo(x.getVendorLogo());
                        tmp.setVehicleScore(x.getProductScore() == null ? "" : x.getProductScore() + "");
                        tmp.setStorename(x.getStoreName());
                        tmp.setVehicleName(x.getProductName());
                        tmp.setVehicleGroupName(x.getVehicleGroupName());
                        tmp.setVehicleGroupCode(x.getVehicleGroupCode());
                        tmp.setVehcileCode(x.getProductId());
                        tmp.setStoreCode(x.getStoreId() == null ? "" : x.getStoreId() + "");
                        tmp.setVehicleImgh5(x.getProductImg());
                        tmp.setVehiclePrice(x.getOriginPrice() == null ? "" : x.getOriginPrice() + "");
                        if (Objects.nonNull(x.getLuggageNo())) {
                            tmp.setLuggageCount(new Integer(x.getLuggageNo()));
                        } else {
                            tmp.setLuggageCount(0);
                        }
                        tmp.setProductname(x.getProductName());
                        tmp.setVendorName(x.getVendorName());
                        if (CollectionUtils.isNotEmpty(x.getActivityLabels())) {
                            tmp.setServicelabels(String.join(",", x.getActivityLabels()));
                        }
                        tmp.setVehicleImgPc(x.getProductImg());
                        tmp.setSaleprice(x.getOriginPrice());
                        tmp.setSeating(0);
                        if (StringUtils.isNotBlank(x.getSeat())) {
                            {
                                tmp.setSeating(Integer.valueOf(x.getSeat()));
                            }
                        }
                        if (CollectionUtils.isNotEmpty(x.getServiceLabels())) {
                            tmp.setServicelabels(x.getServiceLabels().get(0));
                        }
                        return tmp;
                    }).collect(Collectors.toList()));
                    pkgRealtimedataDao.batchInsert(list);
                } catch (Exception ex) {
                    log.warn("ProductManagerService.batchInsert", ex);
                }
            }
        } catch (
                Exception ex) {
            log.warn("ProductManagerService.isdUpData", ex);
            return 0;
        }

        return 1;
    }

    static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    public int UpdateCityPrice(Long cityId, RecommendProductDTO info2, Map<Long, String> cityImage) {
        try {
            CarKalabCity item = new CarKalabCity();
            item.setCityId(cityId);
            List<CarKalabCity> list = carKalabCityDao.queryBy(item);
            if (info2 != null && CollectionUtils.isNotEmpty(list)) {
                list.forEach(x -> {
                    if (Objects.nonNull(info2.getOriginPrice())) {
                        x.setMinPrice((info2.getOriginPrice()).intValue());
                    }
                    x.setDatachangeLasttime(new Timestamp(System.currentTimeMillis()));
                    x.setCtripH5Url(info2.getH5Url());
                    x.setCtripAppUrl(info2.getAppUrl());
                    if (cityImage.containsKey(x.getCityId())) {
                        x.setTripCityPictureUrl(cityImage.get(x.getCityId()));
                    }
                });
                return carKalabCityDao.batchUpdate(list).length;
            }
        } catch (Exception ex) {
            log.error("ProductManagerService.UpdateIsdCityPrice", ex);
        }
        return 0;
    }
}
