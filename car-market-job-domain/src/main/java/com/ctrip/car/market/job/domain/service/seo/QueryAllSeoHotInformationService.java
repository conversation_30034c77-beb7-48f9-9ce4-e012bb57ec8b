package com.ctrip.car.market.job.domain.service.seo;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotInformationDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotInformationMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotInformation;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllSeoHotInformationService implements CachePreLoader<String, List<SeoHotInformationDO>>, CacheLoader<String, List<SeoHotInformationDO>> {

    @Resource
    private MarketDBService service;

    @Resource
    private SeoHotInformationMapper mapper;

    private Map<String, List<SeoHotInformationDO>> getAllHotInformation() throws Exception {
        List<SeoHotInformation> data = service.queryALLInformation();
        return data.stream().filter(l -> l.getPoiType() != null && StringUtils.isNotEmpty(l.getPoiCode())).map(mapper::to).collect(Collectors.groupingBy(l -> l.getPoiType() + "-" + l.getPoiCode().toLowerCase()));
    }

    @Override
    public List<SeoHotInformationDO> load(String area, String key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<String, List<SeoHotInformationDO>> loadAll(String area, Set<String> keys) throws Throwable {
        return getAllHotInformation();
    }

    @Override
    public Map<String, List<SeoHotInformationDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllHotInformation();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}