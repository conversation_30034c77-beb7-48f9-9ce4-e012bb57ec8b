package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.CpnPromotionidredirecturlDO;
import com.ctrip.car.market.job.repository.entity.CpnPromotionidredirecturl;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PromotionidredirecturlMapper {

    CpnPromotionidredirecturlDO to(CpnPromotionidredirecturl cpnPromotionidredirecturl);

    List<CpnPromotionidredirecturlDO> to(List<CpnPromotionidredirecturl> cpnPromotionidredirecturls);
}
