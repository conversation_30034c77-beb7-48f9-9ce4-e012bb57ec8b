package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.order.context.query.offline.OrderDetail;
import com.ctrip.car.order.context.query.offline.QueryOrderDetailForOfflineRequestType;
import com.ctrip.car.order.context.query.offline.QueryOrderDetailForOfflineResponseType;
import com.ctrip.model.CarOrderQueryClient;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component
public class OrderQueryServiceProxy {


    private final CarOrderQueryClient carOrderQueryClient = CarOrderQueryClient.getInstance();

    public OrderDetail getOrderDetail(Long orderId) {
        try {
            if (Optional.ofNullable(orderId).orElse(0L) <= 0) {
                return null;
            }
            QueryOrderDetailForOfflineRequestType requestType = new QueryOrderDetailForOfflineRequestType();
            requestType.setOrderId(orderId);
            QueryOrderDetailForOfflineResponseType responseType = carOrderQueryClient.queryOrderDetailForOffline(requestType);
            Map<String, String> tag = Maps.newHashMap();
            tag.put("orderId", orderId.toString());
            CLogUtil.tagInfo("queryOrderDetail", tag, "request", requestType, "response", responseType);
            return Objects.nonNull(responseType) ? responseType.getOrder() : null;
        } catch (Exception e) {
            CLogUtil.warn("getOrderDetail", e);
            return null;
        }
    }


}
