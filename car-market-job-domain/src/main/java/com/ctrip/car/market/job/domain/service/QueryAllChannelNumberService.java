package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.ChannelNumberDO;
import com.ctrip.car.market.job.domain.mapper.ChannelNumberMapper;
import com.ctrip.car.market.job.repository.entity.ChannelNumber;
import com.ctrip.car.market.job.repository.service.LabelService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllChannelNumberService implements CachePreLoader<Long, ChannelNumberDO>, CacheLoader<Long, ChannelNumberDO> {

    @Resource
    private LabelService service;

    @Resource
    private ChannelNumberMapper mapper;

    private Map<Long, ChannelNumberDO> getAllChannelNumber() throws Exception {
        List<ChannelNumber> data = service.queryAllChannelNumber();
        return data.stream().collect(Collectors.toMap(ChannelNumber::getId, mapper::to));
    }

    @Override
    public ChannelNumberDO load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, ChannelNumberDO> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllChannelNumber();
    }

    @Override
    public Map<Long, ChannelNumberDO> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllChannelNumber();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
