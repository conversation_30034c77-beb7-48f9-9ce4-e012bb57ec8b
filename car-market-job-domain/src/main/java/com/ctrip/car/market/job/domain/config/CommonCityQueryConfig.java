package com.ctrip.car.market.job.domain.config;

import java.util.ArrayList;
import java.util.List;

public class CommonCityQueryConfig {
    private String pickUpCityIds;
    private String returnCityIds;
    private String pickupDate;
    private Integer rentDays;
    private Integer businessType;
    private String appType;
    private String appId;
    private String host;
    private Integer channelId;
    private String cacheMinutes;

    private Integer sourceCountryId;

    private String sourceFrom;

    private String locale;
    private String language;
    private String site;

    private String currencyCode;

    private String orderId;

    private Integer pageSize;
    private Integer sort;
    private Integer sleepMillisecond;
    private List<Long> pickUpCityIdList = new ArrayList<>();
    private List<Long> returnCityIdList = new ArrayList<>();

    private String vehicleGroupIds;

    public String getVehicleGroupIds() {
        return vehicleGroupIds;
    }

    public void setVehicleGroupIds(String vehicleGroupIds) {
        this.vehicleGroupIds = vehicleGroupIds;
    }

    public String getPickUpCityIds() {
        return pickUpCityIds;
    }

    public void setPickUpCityIds(String pickUpCityIds) {
        this.pickUpCityIds = pickUpCityIds;
    }

    public String getReturnCityIds() {
        return returnCityIds;
    }

    public void setReturnCityIds(String returnCityIds) {
        this.returnCityIds = returnCityIds;
    }

    public String getPickupDate() {
        return pickupDate;
    }

    public void setPickupDate(String pickupDate) {
        this.pickupDate = pickupDate;
    }

    public Integer getRentDays() {
        return rentDays;
    }

    public void setRentDays(Integer rentDays) {
        this.rentDays = rentDays;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getCacheMinutes() {
        return cacheMinutes;
    }

    public void setCacheMinutes(String cacheMinutes) {
        this.cacheMinutes = cacheMinutes;
    }


    public List<Long> getPickUpCityIdList() {
        return pickUpCityIdList;
    }

    public void setPickUpCityIdList(List<Long> pickUpCityIdList) {
        this.pickUpCityIdList = pickUpCityIdList;
    }

    public List<Long> getReturnCityIdList() {
        return returnCityIdList;
    }

    public void setReturnCityIdList(List<Long> returnCityIdList) {
        this.returnCityIdList = returnCityIdList;
    }

    public Integer getSourceCountryId() {
        return sourceCountryId;
    }

    public void setSourceCountryId(Integer sourceCountryId) {
        this.sourceCountryId = sourceCountryId;
    }

    public String getSourceFrom() {
        return sourceFrom;
    }

    public void setSourceFrom(String sourceFrom) {
        this.sourceFrom = sourceFrom;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getSleepMillisecond() {
        return sleepMillisecond;
    }

    public void setSleepMillisecond(Integer sleepMillisecond) {
        this.sleepMillisecond = sleepMillisecond;
    }
}
