package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.config.AlipayMessageConfig;
import com.ctrip.car.market.job.domain.dto.AlipayUserInfo;
import com.ctrip.car.market.job.domain.dto.ZeusMessageDto;
import com.ctrip.car.market.job.domain.enums.MessageTempEnum;
import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.repository.entity.AlipaySubscribeTemplateinfo;
import com.ctrip.car.market.job.repository.entity.AlipaySubscribeUserInfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.daas.controller.DaasApiRequestType;
import com.ctrip.daas.controller.DaasApiResponseType;
import com.ctrip.daas.controller.Head;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.sysdev.daas.client.DaasClient;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.MockParameter;
import qunar.tc.schedule.Parameter;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class AlipayUserSchedule {

    private final ILog log = LogManager.getLogger(AlipayUserSchedule.class);

    private final DaasClient daasClient = DaasClient.getInstance();

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private AlipayMessageConfig alipayMessageConfig;

    @QSchedule("car.market.alipay.user.sync")
    public void task(Parameter parameter) throws Exception {
        List<AlipayUserInfo> apiList = getApiData();
        //同步优惠券、活动订阅用户
        List<MessageTempEnum> tempList = Lists.newArrayList(MessageTempEnum.Coupon, MessageTempEnum.Activity);
        for (MessageTempEnum value : tempList) {
            AlipaySubscribeTemplateinfo templateInfo = marketDBService.queryMessageTemplate(value.getType());
            if (templateInfo == null) {
                log.warn("AlipayUserSchedule","template is null");
                continue;
            }
            List<AlipayUserInfo> apiTempList = apiList.stream().filter(l -> StringUtils.equalsIgnoreCase(l.getTemplate_id(), templateInfo.getTemplateId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(apiTempList)) {
                continue;
            }
            List<AlipaySubscribeUserInfo> dbList = marketDBService.querySubscribeUser(templateInfo.getTemplateId());
            Map<String, AlipaySubscribeUserInfo> userMap = dbList.stream().collect(Collectors.toMap(l -> l.getCtripUid().toLowerCase(), l -> l, (k1, k2) -> k1));
            for (AlipayUserInfo userInfo : apiTempList) {
                if (StringUtils.isBlank(userInfo.getTemplate_id()) || StringUtils.isBlank(userInfo.getCtrip_uid())) {
                    continue;
                }
                String key = userInfo.getCtrip_uid().toLowerCase();
                if (userMap.containsKey(key)) {
                    continue;
                }
                AlipaySubscribeUserInfo sample = new AlipaySubscribeUserInfo();
                sample.setAlipayUserId(userInfo.getAlipay_userid());
                sample.setCtripUid(userInfo.getCtrip_uid());
                sample.setTemplateId(userInfo.getTemplate_id());
                sample.setStatus(userInfo.getStatus());
                marketDBService.insertSubscribeUser(sample);
                userMap.put(key, sample);
            }
        }

    }

    @QmqConsumer(prefix = "ops.message.qmq.daas.zeus.job.status", consumerGroup = "CarMarketAlipayMessage")
    public void onMessage(Message message) throws Exception {
        String data = message.getStringProperty("data");
        if (StringUtils.isBlank(data)) {
            return;
        }
        ZeusMessageDto zeusMessage = JsonUtil.parseObject(data, ZeusMessageDto.class);
        if (zeusMessage != null && Objects.equals(zeusMessage.getJobId(), alipayMessageConfig.getZeusJobId())) {
            log.info("AlipayUserSchedule","zeus message start");
            task(new MockParameter());
            log.info("AlipayUserSchedule","zeus message end");
        }
    }

    public List<AlipayUserInfo> getApiData() throws Exception {
        long offset = 0;
        long row = 500;
        List<AlipayUserInfo> result = Lists.newArrayList();
        List<AlipayUserInfo> temp = null;
        do {
            DaasApiRequestType requestType = new DaasApiRequestType();
            Map<String, Object> params = new HashMap<>();
            params.put("offset", offset);
            params.put("rows", row);
            requestType.setHead(new Head("JXD6bj82yTo2mn1jtCo1xA==", 100043032, "getEdwLogCarMiniproUsrDetailDi"));
            requestType.setParams(JsonUtil.toJSONString(params));
            DaasApiResponseType responseType = daasClient.invoke2(requestType);
            CLogUtil.info("OneServiceGetData", requestType, responseType);
            if (StringUtils.isNotBlank(responseType.getData())) {
                temp = JsonUtil.parseArray(responseType.getData(), AlipayUserInfo.class);
                if (CollectionUtils.isNotEmpty(temp)) {
                    result.addAll(temp);
                    offset = temp.stream().map(AlipayUserInfo::getMysql_id).max(Comparator.comparingLong(o -> o)).get();
                }
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }
}
