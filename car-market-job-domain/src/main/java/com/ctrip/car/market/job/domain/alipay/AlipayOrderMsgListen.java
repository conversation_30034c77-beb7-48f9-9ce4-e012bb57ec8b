package com.ctrip.car.market.job.domain.alipay;

import com.ctrip.car.market.job.domain.constant.CommonConstant;
import com.ctrip.car.market.job.domain.enums.MessageTempEnum;
import com.ctrip.car.market.job.domain.enums.OrderStatusEnum;
import com.ctrip.car.market.job.domain.enums.PayStatusEnum;
import com.ctrip.car.market.job.domain.proxy.OrderQueryServiceProxy;
import com.ctrip.car.market.job.repository.entity.AlipaySubscribeTemplateinfo;
import com.ctrip.car.order.context.query.offline.OrderDetail;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageProducer;
import qunar.tc.qmq.MessageSendStateListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Component
public class AlipayOrderMsgListen {

    private final ILog log = LogManager.getLogger(AlipayOrderMsgListen.class);

    @Resource
    private OrderQueryServiceProxy queryServiceProxy;

    @Resource
    private CancelService cancelService;

    @Resource
    private PayService payService;

    @Resource
    private MessageProducer messageProducer;

    @Resource
    private AlipayService alipayService;

    @QmqConsumer(prefix = "car.order.status", consumerGroup = "CarMarketAlipayMessage")
    public void onChange(Message message) {
        long orderId = message.getLongProperty("OrderID");
        int orderStatus = message.getIntProperty("OrderStatus");
        int categoryId = message.getIntProperty("CategoryId");
        String sourceFrom = message.getStringProperty("SourceFrom");
        Map<String, String> tag = Maps.newHashMap();
        tag.put("orderId", orderId + "");
        log.info("orderStatus", orderStatus + "", tag);
        //取消订单
        if (Objects.equals(orderStatus, OrderStatusEnum.Cancel.getStatus())) {
            OrderDetail orderDetail = getAlipayOrderDetail(categoryId, orderId, sourceFrom);
            if (orderDetail != null) {
                cancelService.doBusiness(orderId, orderDetail);
            }
        }
        //待支付
        if (OrderStatusEnum.noPay(orderStatus)) {
            OrderDetail orderDetail = getAlipayOrderDetail(categoryId, orderId, sourceFrom);
            if (orderDetail != null && Objects.equals(orderDetail.getPayStatus(), PayStatusEnum.noPay.getStatus())) {
                sendPayMessage(orderId);
            }
        }
    }

    private OrderDetail getAlipayOrderDetail(Integer categoryId, Long orderId, String sourceFrom) {
        //国内根据sourceFrom判断
        if (Objects.equals(categoryId, CommonConstant.ISD) && CommonConstant.ISD_ALIPAY_SOURCE_FROM.equalsIgnoreCase(sourceFrom)) {
            return queryServiceProxy.getOrderDetail(orderId);
        }
        //海外根据订单接口里的sourceFrom判断
        if (Objects.equals(categoryId, CommonConstant.OSD)) {
            OrderDetail orderDetail = queryServiceProxy.getOrderDetail(orderId);
            return orderDetail != null && CommonConstant.OSD_ALIPAY_SOURCE_FROM.equalsIgnoreCase(orderDetail.getSourceFrom()) ? orderDetail : null;
        }
        return null;
    }

    public void sendPayMessage(Long orderId) {
        AlipaySubscribeTemplateinfo templateInfo = alipayService.queryMessageTemplate(MessageTempEnum.Payment.getType());
        if (templateInfo == null) {
            log.warn("AlipayOrderMsg.send", orderId + "pay template null");
            return;
        }
        Message message = messageProducer.generateMessage("car.market.order.pending.payment.alipay.message");
        message.setProperty("OrderID", orderId);
        message.setDelayTime(Optional.ofNullable(templateInfo.getRemainingTime()).orElse(5), TimeUnit.MINUTES);
        messageProducer.sendMessage(message, new MessageSendStateListener() {
            @Override
            public void onSuccess(Message message) {
                log.info("AlipayOrderMsg.send", orderId + "message producer success");
            }

            @Override
            public void onFailed(Message message) {
                log.error("AlipayOrderMsg.send", orderId + "message producer failed");
            }
        });
    }

    @QmqConsumer(prefix = "car.market.order.pending.payment.alipay.message", consumerGroup = "CarMarketAlipayMessage")
    public void onMessage(Message message) {
        long orderId = message.getLongProperty("OrderID");
        OrderDetail orderDetail = queryServiceProxy.getOrderDetail(orderId);
        //判断是否未支付
        if (orderDetail != null && OrderStatusEnum.noPay(orderDetail.getOrderStatus()) && Objects.equals(orderDetail.getPayStatus(), PayStatusEnum.noPay.getStatus())) {
            payService.doBusiness(orderId, orderDetail);
        }
    }

}
