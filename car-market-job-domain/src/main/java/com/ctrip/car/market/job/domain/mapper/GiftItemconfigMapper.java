package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.GiftItemconfigDO;
import com.ctrip.car.market.job.repository.entity.GiftItemconfig;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface GiftItemconfigMapper {

    GiftItemconfigDO to(GiftItemconfig giftItemconfig);

    List<GiftItemconfigDO> to(List<GiftItemconfig> giftItemconfigs);
}
