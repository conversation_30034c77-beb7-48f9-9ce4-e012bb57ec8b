package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.RecommendCityconfigDO;
import com.ctrip.car.market.job.repository.entity.RecommendCityconfig;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RecommendCityConfigMapper {

    RecommendCityconfigDO to(RecommendCityconfig value);

    List<RecommendCityconfigDO> to(List<RecommendCityconfig> values);
}
