package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnProductRedirectUrlDO;
import com.ctrip.car.market.job.domain.message.ProductRedirectUrlMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllProductRedirectUrlService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.PRODUCT_REDIRECT_URL_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.ProductRedirectUrlCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(ProductRedirectUrlCache.Name)
@CreateCacheArea(area = "public")
public class ProductRedirectUrlCache extends BasicCacheAbstract<String, List<CpnProductRedirectUrlDO>> {

    public static final String Name = ProductRedirectUrlCacheName;

    @Resource
    private ProductRedirectUrlMessageConvert convert;

    @Resource
    private QueryAllProductRedirectUrlService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = PRODUCT_REDIRECT_URL_HASH_KEY, remotePreLoadName = Name)
    private Cache<String, List<CpnProductRedirectUrlDO>> cache;


    @Override
    public ConvertMessage<String, List<CpnProductRedirectUrlDO>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<String, List<CpnProductRedirectUrlDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<String, List<CpnProductRedirectUrlDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<String, List<CpnProductRedirectUrlDO>> getLoader() {
        return null;
    }
}
