package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnMergeConditionDO;
import com.ctrip.car.market.job.domain.mapper.MergeConditionMapper;
import com.ctrip.car.market.job.repository.entity.CpnMergeCondition;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllMergeConditionService implements CachePreLoader<String, List<CpnMergeConditionDO>>, CacheLoader<String, List<CpnMergeConditionDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private MergeConditionMapper mapper;

    private Map<String, List<CpnMergeConditionDO>> getAllGroupVersion() throws Exception {
        List<CpnMergeCondition> data = service.queryAllMergeCondition();
        Map<String, List<CpnMergeConditionDO>> map = Maps.newHashMap();
        map.put("ALL", data.stream().map(mapper::to).collect(Collectors.toList()));
        return map;
    }

    @Override
    public List<CpnMergeConditionDO> load(String area, String key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<String, List<CpnMergeConditionDO>> loadAll(String area, Set<String> keys) throws Throwable {
        return getAllGroupVersion();
    }

    @Override
    public Map<String, List<CpnMergeConditionDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllGroupVersion();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
