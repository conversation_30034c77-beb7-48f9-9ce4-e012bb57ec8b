package com.ctrip.car.market.job.domain.utils;

import org.apache.commons.codec.binary.Hex;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

public class UUIDUtil {

    public static  String  getGuid(){
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }

    public static String getContentMd5(byte[] fileBytes) throws NoSuchAlgorithmException {
        MessageDigest m = MessageDigest.getInstance("MD5");
        byte[] digest = m.digest(fileBytes);
        return Hex.encodeHexString(digest);
    }
}
