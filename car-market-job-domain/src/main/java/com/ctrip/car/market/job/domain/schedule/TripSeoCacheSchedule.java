package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.config.JobConfig;
import com.ctrip.car.market.job.domain.config.SeoHotVendorConfig;
import com.ctrip.car.market.job.domain.config.SeoVendorCityPageConfig;
import com.ctrip.car.market.job.domain.dto.CustomerConfigDTO;
import com.ctrip.car.market.job.domain.proxy.CarCommodityVendorProxy;
import com.ctrip.car.market.job.domain.proxy.DestProxy;
import com.ctrip.car.market.job.domain.utils.QConfigUtil;
import com.ctrip.car.market.job.domain.utils.RedisUtil;
import com.ctrip.car.market.job.repository.entity.SeoHotCityinfo;
import com.ctrip.car.market.job.repository.entity.SeoHotDestinatioinfo;
import com.ctrip.car.market.job.repository.entity.SeoHotVendor;
import com.ctrip.car.market.job.repository.entity.SeoHotVendorCity;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.hc.QunarAsyncClient;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class TripSeoCacheSchedule {

    private final ILog log = LogManager.getLogger(TripSeoCacheSchedule.class);

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private CarCommodityVendorProxy carCommodityVendorProxy;

    @Resource
    private JobConfig jobConfig;

    @Resource
    private DestProxy destProxy;

    @Resource
    private SeoHotVendorConfig seoHotVendorConfig;

    private static final QunarAsyncClient client = new QunarAsyncClient();

    @QSchedule("car.market.seo.cache.init")
    public void task() {
        try {
            String localeStr = QConfigUtil.getSeoHotConfigOrDefault("seo.ibu.qmq.local", "en-ID,en-PH,nl-NL,pt-BR,tr-TR,pt-PT,de-DE,fr-FR,th-TH,en-AU,en-SG,es-ES,it-IT,en-CA,en-GB,en-US,en-XX,ja-JP,ko-KR,zh-HK,zh-TW");
            List<String> localeList = Lists.newArrayList(localeStr.split(","));
            List<SeoHotDestinatioinfo> airportList = marketDBService.queryAllHotPoi();
            for (SeoHotDestinatioinfo destinatioinfo : airportList) {
                getUrl(destinatioinfo.getUrl(), localeList);
            }
        } catch (Exception e) {
            log.error("TripSeoCacheSchedule", e);
        }
    }

    @QSchedule("car.market.seo.cache.vendor.page.init")
    public void vendorPageTask() {
        try {
            String localeStr = QConfigUtil.getSeoHotConfigOrDefault("seo.ibu.qmq.local", "en-ID,en-PH,nl-NL,pt-BR,tr-TR,pt-PT,de-DE,fr-FR,th-TH,en-AU,en-SG,es-ES,it-IT,en-CA,en-GB,en-US,en-XX,ja-JP,ko-KR,zh-HK,zh-TW");
            List<String> localeList = Lists.newArrayList(localeStr.split(","));
            List<SeoHotVendorCity> vendorCityList = marketDBService.queryAllHotVendorCity();
            for (SeoHotVendorCity city : vendorCityList) {
                getUrl(city.getUrl(), localeList);
            }
            List<SeoHotVendor> vendorList = marketDBService.queryAllHotVendor();
            for (SeoHotVendor vendor : vendorList) {
                getUrl(vendor.getUrl(), localeList);
            }
        } catch (Exception e) {
            log.error("TripSeoCacheSchedule", e);
        }
    }

    public void getUrl(String url, List<String> localeList) throws Exception {
        for (String locale : localeList) {
            String site = locale.split("-")[1].toLowerCase();
            String webUrl = url.replaceAll("www", site);
            client.get(webUrl);
            Thread.sleep(800);
        }
    }

    @QSchedule("car.market.seo.vendor.logo.cache.init")
    public void vendorLogoTask() {
        if (jobConfig.getSeoHotVendor() == null) {
            return;
        }
        List<String> vendorCodeList = Lists.newArrayList(jobConfig.getSeoHotVendor().getDefaultConfig().split(","));
        if (CollectionUtils.isNotEmpty(jobConfig.getSeoHotVendor().getCustomerConfig())) {
            for (CustomerConfigDTO customerConfig : jobConfig.getSeoHotVendor().getCustomerConfig()) {
                List<String> codeList = Lists.newArrayList(customerConfig.getVendorList().split(","));
                vendorCodeList.addAll(codeList);
            }
        }
        if (CollectionUtils.isNotEmpty(seoHotVendorConfig.getSeoVendorCityPageConfigList())) {
            List<String> vendorPageList = seoHotVendorConfig.getSeoVendorCityPageConfigList().stream().map(SeoVendorCityPageConfig::getVendorCode).distinct().collect(Collectors.toList());
            vendorCodeList.addAll(vendorPageList);
        }
        List<String> list = vendorCodeList.stream().map(String::trim).distinct().collect(Collectors.toList());
        for (String vendorCode : list) {
            String logo = carCommodityVendorProxy.queryVendorLogo(vendorCode);
            if (StringUtils.isNotEmpty(logo)) {
                RedisUtil.set("car.market.seo.vendor.logo." + vendorCode.toLowerCase(), logo, 30 * 24 * 3600);
            }
        }
    }

    @QSchedule("car.market.seo.city.image.init")
    public void cityImageTask() {
        try {
            List<SeoHotCityinfo> cityList = marketDBService.queryAllHotCity();
            for (SeoHotCityinfo seoHotCityinfo : cityList) {
                Map<Long, String> cityMap = destProxy.getCityImage(Lists.newArrayList(seoHotCityinfo.getCityId().longValue()));
                if (cityMap != null && !cityMap.isEmpty() && cityMap.containsKey(seoHotCityinfo.getCityId().longValue())) {
                    seoHotCityinfo.setImage(cityMap.get(seoHotCityinfo.getCityId().longValue()));
                    marketDBService.updateSeoHotCity(seoHotCityinfo);
                    Thread.sleep(100);
                }
            }
        } catch (Exception e) {
            log.error("cityImageTask", e);
        }
    }
}
