package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnUnionPromotionLimitDO;
import com.ctrip.car.market.job.domain.message.UnionPromotionLimitMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllUnionPromotionLimitService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.UNION_PROMOTION_LIMIT_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.UnionPromotionLimitCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(UnionPromotionLimitCache.Name)
@CreateCacheArea(area = "public")
public class UnionPromotionLimitCache extends BasicCacheAbstract<Integer, List<CpnUnionPromotionLimitDO>> {

    public static final String Name = UnionPromotionLimitCacheName;

    @Resource
    private UnionPromotionLimitMessageConvert convert;

    @Resource
    private QueryAllUnionPromotionLimitService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = UNION_PROMOTION_LIMIT_HASH_KEY, remotePreLoadName = Name)
    private Cache<Integer, List<CpnUnionPromotionLimitDO>> cache;


    @Override
    public ConvertMessage<Integer, List<CpnUnionPromotionLimitDO>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Integer, List<CpnUnionPromotionLimitDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, List<CpnUnionPromotionLimitDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, List<CpnUnionPromotionLimitDO>> getLoader() {
        return null;
    }
}
