package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.CpnRestrictedPromotionDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.RestrictedPromotionMapper;
import com.ctrip.car.market.job.repository.entity.CpnRestrictedPromotion;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class RestrictedPromotion_SummaryMessageConvert extends AbstractConvertMessage<Long, List<CpnRestrictedPromotion>, List<CpnRestrictedPromotionDO>> implements ConvertMessage<Long, List<CpnRestrictedPromotionDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private RestrictedPromotionMapper mapper;

    public RestrictedPromotion_SummaryMessageConvert() {
        super(TabelEnum.RestrictedPromotion_Summary);
    }

    @Override
    public Map<Long, List<CpnRestrictedPromotionDO>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Long getKey(String ids) {
        return Long.valueOf(ids);
    }

    @Override
    public List<CpnRestrictedPromotion> getData(Long id) throws Exception {
        List<CpnRestrictedPromotion> data = service.queryRestrictedPromotionBySummary(id);
        return CollectionUtils.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<CpnRestrictedPromotionDO> mapper(List<CpnRestrictedPromotion> value) {
        return mapper.to(value);
    }
}
