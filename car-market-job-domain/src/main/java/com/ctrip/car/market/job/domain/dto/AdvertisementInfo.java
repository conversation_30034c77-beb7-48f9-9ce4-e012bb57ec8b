package com.ctrip.car.market.job.domain.dto;

import java.util.Objects;

public class AdvertisementInfo {

    private String imageURL; //图片地址
    private String clickURL; //点击地址
    private String monitorURL; //监控地址
    private String startDate; //开始时间 YYYY-MM-dd HH:mm:ss
    private String endDate; //结束时间
    private String size; //物料尺寸 1080x2340
    private String filghtId; //去哪儿投放ID
    private String adId; //去哪物料ID
    private String ctripImageURL; //图片地址
    public boolean isEquals(AdvertisementInfo another) {
        if (another == null) return false;
        return
                Objects.equals(this.getImageURL(), another.getImageURL()) &&
                        Objects.equals(this.getClickURL(), another.getClickURL()) &&
                        Objects.equals(this.getMonitorURL(), another.getMonitorURL()) &&
                        Objects.equals(this.getStartDate(), another.getStartDate()) &&
                        Objects.equals(this.getEndDate(), another.getEndDate()) &&
                        Objects.equals(this.getSize(), another.getSize()) &&
                        Objects.equals(this.getFilghtId(), another.getFilghtId()) &&
                        Objects.equals(this.getAdId(), another.getAdId());
    }

    public String getImageURL() {
        return imageURL;
    }

    public void setImageURL(String imageURL) {
        this.imageURL = imageURL;
    }

    public String getClickURL() {
        return clickURL;
    }

    public void setClickURL(String clickURL) {
        this.clickURL = clickURL;
    }

    public String getMonitorURL() {
        return monitorURL;
    }

    public void setMonitorURL(String monitorURL) {
        this.monitorURL = monitorURL;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getFilghtId() {
        return filghtId;
    }

    public void setFilghtId(String filghtId) {
        this.filghtId = filghtId;
    }

    public String getAdId() {
        return adId;
    }

    public void setAdId(String adId) {
        this.adId = adId;
    }


    public String getCtripImageURL() {
        return ctripImageURL;
    }

    public void setCtripImageURL(String ctripImageURL) {
        this.ctripImageURL = ctripImageURL;
    }
}
