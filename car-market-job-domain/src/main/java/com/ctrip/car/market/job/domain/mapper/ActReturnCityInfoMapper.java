package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.ActReturnCityInfoDO;
import com.ctrip.car.market.job.repository.entity.activity.ActReturnCityinfo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ActReturnCityInfoMapper {

    ActReturnCityInfoDO to(ActReturnCityinfo value);

    List<ActReturnCityInfoDO> to(List<ActReturnCityinfo> values);
}
