package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnMergeConditionDO;
import com.ctrip.car.market.job.domain.message.MergeConditionMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllMergeConditionService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.MERGE_CONDITION_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.MergeConditionCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(MergeConditionCache.Name)
@CreateCacheArea(area = "public")
public class MergeConditionCache extends BasicCacheAbstract<String, List<CpnMergeConditionDO>> {

    public static final String Name = MergeConditionCacheName;

    @Resource
    private MergeConditionMessageConvert convert;

    @Resource
    private QueryAllMergeConditionService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = MERGE_CONDITION_HASH_KEY, remotePreLoadName = Name)
    private Cache<String, List<CpnMergeConditionDO>> cache;


    @Override
    public ConvertMessage<String, List<CpnMergeConditionDO>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<String, List<CpnMergeConditionDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<String, List<CpnMergeConditionDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<String, List<CpnMergeConditionDO>> getLoader() {
        return null;
    }
}
