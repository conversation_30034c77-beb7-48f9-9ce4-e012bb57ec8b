package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.ActVendorSkuInfoDO;
import com.ctrip.car.market.job.domain.message.ActivityVendorSkuMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllActivityVendorSkuService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.ACT_VENDOR_SKU_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.IsdActivityVendorSKuCacheName;

@Component(ActivityVendorSkuCache.Name)
@CreateCacheArea(area = "public")
public class ActivityVendorSkuCache extends BasicCacheAbstract<Long, List<ActVendorSkuInfoDO>>{

    public static final String Name = IsdActivityVendorSKuCacheName;

    @Resource
    private QueryAllActivityVendorSkuService service;

    @Resource
    private ActivityVendorSkuMessageConvert convert;

    @IncUpdateConsumerByQmq(subject = "car.market.activity.data.change", convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = ACT_VENDOR_SKU_KEY, remotePreLoadName = Name)
    private Cache<Long, List<ActVendorSkuInfoDO>> cache;

    @Override
    public ConvertMessage<Long, List<ActVendorSkuInfoDO>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Long, List<ActVendorSkuInfoDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, List<ActVendorSkuInfoDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, List<ActVendorSkuInfoDO>> getLoader() {
        return null;
    }
}