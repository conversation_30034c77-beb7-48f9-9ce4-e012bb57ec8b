package com.ctrip.car.market.job.domain.enums;

import java.util.Objects;

public enum OrderStatusEnum {

    //已提交待分配
    Submitted(0),
    //已分配待处理
    Assigned(1),
    //处理中
    Processing(2),
    //取消
    Cancel(4);

    private Integer status;

    OrderStatusEnum(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public static boolean noPay(Integer status) {
        return Objects.equals(OrderStatusEnum.Submitted.getStatus(), status)
                || Objects.equals(OrderStatusEnum.Assigned.getStatus(), status)
                || Objects.equals(OrderStatusEnum.Processing.getStatus(), status);
    }
}
