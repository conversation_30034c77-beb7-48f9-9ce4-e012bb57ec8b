package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.repository.entity.activity.ActCtripactinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class QueryAllActivityTempMappingService implements CachePreLoader<Long, List<Long>>, CacheLoader<Long, List<Long>> {

    private final ILog log = LogManager.getLogger(QueryAllActivityTempIdService.class);

    @Resource
    private ActivityService service;

    private Map<Long, List<Long>> getAllActivityTempMapping() throws Exception {
        List<ActCtripactinfo> data = service.queryAllActivity();
        return data.stream().collect(Collectors.groupingBy(ActCtripactinfo::getTempId, Collectors.mapping(ActCtripactinfo::getId, Collectors.toCollection(ArrayList::new))));
    }

    @Override
    public List<Long> load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, List<Long>> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllActivityTempMapping();
    }

    @Override
    public Map<Long, List<Long>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllActivityTempMapping();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
