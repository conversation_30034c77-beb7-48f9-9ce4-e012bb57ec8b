package com.ctrip.car.market.job.domain.dto;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description:
 * @create 2025/7/18 15:22
 */
@Data
public class RiskVerifyBodyDto {
    private  String clientId;
    private  String openId;
    private  String serverFrom;
    private  String uid;
    private  String userIp;
    private  String rmsToken;
    private  String scene;
    private  String platform;
    private  RiskVerifyBodyExtensionDto extension;

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getServerFrom() {
        return serverFrom;
    }

    public void setServerFrom(String serverFrom) {
        this.serverFrom = serverFrom;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getUserIp() {
        return userIp;
    }

    public void setUserIp(String userIp) {
        this.userIp = userIp;
    }

    public String getRmsToken() {
        return rmsToken;
    }

    public void setRmsToken(String rmsToken) {
        this.rmsToken = rmsToken;
    }

    public RiskVerifyBodyExtensionDto getExtension() {
        return extension;
    }

    public void setExtension(RiskVerifyBodyExtensionDto extension) {
        this.extension = extension;
    }
}
