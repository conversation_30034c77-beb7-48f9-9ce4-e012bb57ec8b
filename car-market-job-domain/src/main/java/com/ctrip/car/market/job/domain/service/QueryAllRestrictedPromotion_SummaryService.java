package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnRestrictedPromotionDO;
import com.ctrip.car.market.job.domain.mapper.RestrictedPromotionMapper;
import com.ctrip.car.market.job.repository.entity.CpnRestrictedPromotion;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllRestrictedPromotion_SummaryService implements CachePreLoader<Long, List<CpnRestrictedPromotionDO>>, CacheLoader<Long, List<CpnRestrictedPromotionDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private RestrictedPromotionMapper mapper;

    private Map<Long, List<CpnRestrictedPromotionDO>> getAllRestrictedPromotion() throws Exception {
        List<CpnRestrictedPromotion> data = service.queryAllRestrictedPromotion().stream().filter(l -> l.getSummaryId() > 0).collect(Collectors.toList());
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(CpnRestrictedPromotionDO::getSummaryId));
    }


    @Override
    public List<CpnRestrictedPromotionDO> load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, List<CpnRestrictedPromotionDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllRestrictedPromotion();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<Long, List<CpnRestrictedPromotionDO>> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllRestrictedPromotion();
    }
}
