package com.ctrip.car.market.job.domain.message;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import qunar.tc.qmq.Message;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

public abstract class AbstractConvertMessage<K, V, T> {

    private final TabelEnum tabelEnum;

    public AbstractConvertMessage(TabelEnum tabelEnum) {
        this.tabelEnum = tabelEnum;
    }

    public Map<K, T> getMap(Message message) throws Exception {
        if (message == null) {
            return Collections.emptyMap();
        }
        String data = message.getStringProperty("dataChange");
        if (StringUtils.isEmpty(data)) {
            return Collections.emptyMap();
        }
        DataChange dataChangeObject = JsonUtil.parseObject(data, DataChange.class);
        if (!StringUtils.equalsIgnoreCase(tabelEnum.getTabelName(), dataChangeObject.getTableName())) {
            return Collections.emptyMap();
        }
        Map<K, T> map = Maps.newHashMap();
        String ids = CollectionUtils.isNotEmpty(dataChangeObject.getAfterColumnList())
                ? dataChangeObject.getAfterColumnValue(tabelEnum.getPrimaryKeyName())
                : dataChangeObject.getBeforeColumnValue(tabelEnum.getPrimaryKeyName());
        K id = getKey(ids);
        V v = getData(id);
        if (Objects.nonNull(v)) {
            map.put(id, mapper(v));
        } else {
            map.put(id, null);
        }
        return map;
    }

    public abstract K getKey(String ids);

    public abstract V getData(K id) throws Exception;

    public abstract T mapper(V value);
}
