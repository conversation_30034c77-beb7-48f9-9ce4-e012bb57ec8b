package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.crossrecommend.service.contract.dto.SeoZone;
import com.ctrip.car.market.job.domain.config.QueryProductConfig;
import com.ctrip.car.market.job.domain.dto.*;
import com.ctrip.car.market.job.domain.proxy.OsdBasicDataProxy;
import com.ctrip.car.market.job.domain.proxy.ProductProxy;
import com.ctrip.car.market.job.domain.utils.*;
import com.ctrip.car.market.job.repository.dao.CarKalabCityDao;
import com.ctrip.car.market.job.repository.dao.carseodb.SeoTripproductDao;
import com.ctrip.car.market.job.repository.entity.CarKalabCity;
import com.ctrip.car.market.job.repository.entity.carseodb.SeoTripproduct;
import com.ctrip.car.osd.shopping.api.entity.*;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctriposs.baiji.rpc.common.types.AllianceInfoDTO;
import com.ctriposs.baiji.rpc.common.types.BaseRequest;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class TripSeoProductService {
    private final ILog log = LogManager.getLogger(getClass());
    private final AtomicInteger totalCount = new AtomicInteger(0);
    private static final ExecutorService productExecutorService = Executors.newFixedThreadPool(20);

    @Resource
    private QueryProductConfig queryProductConfig;
    @Resource
    private CarKalabCityDao carKalabCityDao;
    @Resource
    private SeoTripproductDao seoTripproductDao;
    @Resource
    private OsdBasicDataProxy osdBasicDataProxy;
    @Resource
    private ProductProxy productProxy;
    @Autowired
    private FaqUpdateBusiness faqUpdateBusiness;

    public void updateProduct(int priority) {
        //先去redis里面拿
        try {
            totalCount.set(0);
            long start = System.currentTimeMillis();
            List<Integer> countryIds = new ArrayList<>();
            String sourceCountryIds = QConfigUtil.getConfigOrDefault("sourceCountryIds", "");
            if (StringUtils.isNotEmpty(sourceCountryIds)) {
                countryIds = Arrays.stream(sourceCountryIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
            }
            List<CarKalabCity> resultCitys = getCarKalabCities(priority);
            List<SeoCityDTO> seoCitys = iteration(resultCitys, countryIds);
            exe(seoCitys);
            long end = System.currentTimeMillis();
            log.info("SeoProductJobTime", (end - start) / 1000 + "");
            //请求shopping成功并有返回的数量的埋点
        } catch (Exception e) {
            log.error("updateProduct", e);
        }
    }

    public List<CarKalabCity> getCarKalabCities(int priority) throws Exception {
        List<CarKalabCity> osdValidCitys = getOsdValidALLCitys(priority);
        //港澳台特殊处理 1001 1002 10003
        List<CarKalabCity> allCity = osdValidCitys.stream().map(c -> {
            if (c.getProvinceId() == 32) {
                c.setCountryId(1001L);
            } else if (c.getProvinceId() == 33) {
                c.setCountryId(1002L);
            } else if (c.getProvinceId() == 53) {
                c.setCountryId(1003L);
            }
            return c;
        }).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(item -> item.getCityId()))), ArrayList::new));

        return allCity;
    }

    public List<CarKalabCity> getOsdValidALLCitys(int priority) {
        List<Integer> provinceIds = new ArrayList<>();
        provinceIds.add(32);
        provinceIds.add(33);
        provinceIds.add(53);
        try {
            return carKalabCityDao.queryByParams(provinceIds, priority);
        } catch (Exception e) {
        }
        return null;
    }

    private List<SeoCityDTO> iteration(List<CarKalabCity> citys, List<Integer> sourceCountryIdConfig) {
        List<SeoCityDTO> seoCityDTOS = new ArrayList<>();
        for (CarKalabCity city : citys) {
            for (Integer countryId : sourceCountryIdConfig) {
                seoCityDTOS.add(buildSeoCity(city.getCityId().intValue(), countryId));
            }
            if (!sourceCountryIdConfig.contains(city.getCountryId().intValue())) {
                seoCityDTOS.add(buildSeoCity(city.getCityId().intValue(), city.getCountryId().intValue()));
            }
        }
        return seoCityDTOS;
    }

    private SeoCityDTO buildSeoCity(Integer cityId, Integer countryId) {
        SeoCityDTO seoCityDTO = new SeoCityDTO();
        seoCityDTO.setCityId(cityId);
        seoCityDTO.setCountryId(countryId);
        return seoCityDTO;
    }

    public void exe(List<SeoCityDTO> citys) {
        boolean flag = true;
        long pageIndex = 1L;
        long pageSize = QConfigUtil.getConfigLong("synProductCount", "20");
        Long synProductSleepMillis = QConfigUtil.getConfigLong("synProductSleepMillis", "10");
        long start = System.currentTimeMillis();
        while (flag) {
            List<SeoCityDTO> pageList = citys.stream().skip((pageIndex - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            long itemStart = System.currentTimeMillis();
            supply(pageList);
            log.info("SeoItemCase", "pageSize=" + pageSize + ",pageIndex=" + pageIndex + "time=" + (System.currentTimeMillis() - itemStart) / 1000);
            if (CollectionUtils.isNotEmpty(pageList)) {
                pageIndex++;
                try {
                    Thread.sleep(synProductSleepMillis);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } else {
                flag = false;
            }
        }
        log.info("SeoJobEnd", "pageSize=" + pageSize + ",pageIndex=" + pageIndex + "time=" + (System.currentTimeMillis() - start) / (1000 * 60));
    }

    public void supply(List<SeoCityDTO> citys) {
        if (CollectionUtils.isNotEmpty(citys)) {
            List<CompletableFuture<String>> taskList = Lists.newArrayList();
            citys.forEach(c -> taskList.add(CompletableFuture.supplyAsync(() -> {
                this.task(c);
                return "1";
            }, productExecutorService)));
            CompletableFuture.allOf(taskList.toArray(new CompletableFuture[0])).join();
        }
    }

    public void task(SeoCityDTO city) {
        try {
            List<SeoTripproduct> list = new ArrayList<>();
            QueryProductRequestType request = buildShoppingRequest(city);
//            TraceUtil.build("buildShoppingRequest").addTag("sourceCountryId", city.getCountryId()).addCity(city.getCityId()).addResult(Objects.isNull(request) ? "0" : "1").push();
            if (Objects.isNull(request)) {
                return;
            }
            long start = System.currentTimeMillis();
            QueryProductResponseType responseType = productProxy.queryProduct(request);
            long end = System.currentTimeMillis();
//            TraceData traceData = TraceUtil.build("osd_queryProduct").addTag("language", request.getBaseRequest().getLanguage()).addTag("sourceCountryId", request.getBaseRequest().getSourceCountryId()).addCity(request.getPickupLocation().getCityId()).addChannel(request.getBaseRequest().getChannelId()).addCost(end - start).addResult(responseType == null ? null : responseType.getVehicles());
//            if (responseType != null && Objects.nonNull(responseType.getBaseResponse())) {
//                traceData.addTag("errorCode", responseType.getBaseResponse().getCode()).addTag("errorMsg", responseType.getBaseResponse().getReturnMsg());
//            }
//            traceData.push();
            Map<String, String> metricsTags = new HashMap<>();
            metricsTags.put("language", request.getBaseRequest().getLanguage());
            metricsTags.put("sourceCountryId", String.valueOf(request.getBaseRequest().getSourceCountryId()));
            metricsTags.put("channelid", String.valueOf(request.getBaseRequest().getChannelId()));
            metricsTags.put("cost", String.valueOf(end - start));
            metricsTags.put("result", (responseType == null || CollectionUtils.isEmpty(responseType.getVehicles())) ? "0" : "1");
            if (responseType != null && Objects.nonNull(responseType.getBaseResponse())) {
                metricsTags.put("errorCode", responseType.getBaseResponse().getCode());
                metricsTags.put("errorMsg", responseType.getBaseResponse().getReturnMsg());
            }
            if (responseType != null && CollectionUtils.isNotEmpty(responseType.getVehicles())) {
                totalCount.incrementAndGet();
                final int klbVersion = Integer.parseInt(responseType.getExtras() != null ? responseType.getExtras().getOrDefault("klbVersion", "0") : "0");
                responseType.getVehicles().stream().forEach(vehicleInfo -> {
                    SeoTripproduct productEntry = new SeoTripproduct();
                    productEntry.setCityId(city.getCityId().intValue());
                    productEntry.setProductName(vehicleInfo.getName());
                    productEntry.setProductImg(vehicleInfo.getDefaultImageUrl());
                    if (StringUtils.isEmpty(vehicleInfo.getGroupName())) {
                        productEntry.setGroupName("");
                    } else {
                        productEntry.setGroupName(vehicleInfo.getGroupName());
                    }
                    if (StringUtils.isEmpty(vehicleInfo.getGroupEName())) {
                        productEntry.setGroupEname("");
                    } else {
                        productEntry.setGroupEname(vehicleInfo.getGroupEName());
                    }
                    productEntry.setBrandEName(vehicleInfo.getBrandEName());
                    productEntry.setBrandId(Long.valueOf(vehicleInfo.getBrandId()));
                    productEntry.setBrandName(vehicleInfo.getBrandName());
                    productEntry.setProductEnName(vehicleInfo.getEname());
                    productEntry.setDoorNo(vehicleInfo.getDoorNo());
                    productEntry.setLuggageNo(vehicleInfo.getLuggageNo());
                    productEntry.setSeat(vehicleInfo.getPassengerNo());
                    productEntry.setTransmissionType(vehicleInfo.getTransmissionType());
                    productEntry.setVehicleId(vehicleInfo.getId().intValue());
                    productEntry.setSourceCountryId(city.getCountryId());
                    productEntry.setActive(true);
                    //链接参数
                    String urlParams = skipBuild(request, vehicleInfo.getVehicleCode(), klbVersion);
                    productEntry.setPcListUrl(urlParams);
                    productEntry.setH5ListUrl(urlParams);
                    List<VehicleInfoDTO> vendors = responseType.getVendors().stream()
                            .filter(ven -> ven.getProductInfoDtoList().stream().anyMatch(pi -> pi.getVendorVehicle().getVehicleCode().equalsIgnoreCase(vehicleInfo.getVehicleCode())))
                            .map(y -> {
                                VehicleInfoDTO vehicleInfoDTO = new VehicleInfoDTO();
                                vehicleInfoDTO.setVendorImageUrl(y.getVendorImageUrl());
                                if (CollectionUtils.isNotEmpty(y.getStoreList())) {
                                    vehicleInfoDTO.setStores(convertStore(y.getStoreList()));
                                }
                                Optional<ProductInfo> productInfo = y.getProductInfoDtoList().stream().filter(k -> k.getVendorVehicle().getVehicleCode().equalsIgnoreCase(vehicleInfo.getVehicleCode())).findFirst();
                                if (productInfo.isPresent()) {
                                    PriceInfo priceInfo = productInfo.get().getPriceInfoDtoList().stream().min(Comparator.comparing(PriceInfo::getCurrentDailyPrice)).orElse(null);
                                    if (priceInfo != null) {
                                        vehicleInfoDTO.setVendorCode(y.getBizVendorCode());
                                        vehicleInfoDTO.setPrice(priceInfo.getCurrentDailyPrice());
                                    }
                                }
                                return vehicleInfoDTO;
                            }).filter(ven -> Objects.nonNull(ven) && Objects.nonNull(ven.getPrice())).sorted(Comparator.comparing(VehicleInfoDTO::getPrice)).limit(5).collect(Collectors.toList());
                    productEntry.setDailyPrice(vendors.stream().filter(Objects::nonNull).min(Comparator.comparing(VehicleInfoDTO::getPrice)).map(VehicleInfoDTO::getPrice).orElse(null));
                    try {
                        productEntry.setVendorContent(JsonUtil.toJSONString(vendors));
                    } catch (Exception e) {
                        log.error("VendorContentError", e);
                    }
                    BigDecimal price = vendors.stream().min(Comparator.comparing(VehicleInfoDTO::getPrice)).map(v -> v.getPrice()).orElse(BigDecimal.ZERO);
                    productEntry.setDailyPrice(price);
                    list.add(productEntry);
                    faqUpdateBusiness.adddOrUpdateFaq(responseType, new Long(city.getCityId()),city.getCountryId());
                });
            }
            if (responseType != null) {
                SeoTripproduct query = new SeoTripproduct();
                query.setActive(true);
                query.setCityId(city.getCityId().intValue());
                query.setSourceCountryId(city.getCountryId());
                List<SeoTripproduct> seoTripproducts = seoTripproductDao.queryBy(query, new DalHints());
                if (CollectionUtils.isNotEmpty(seoTripproducts)) {
                    seoTripproductDao.batchDelete(new DalHints(), seoTripproducts);
                    log.info("seoProductBatchDelete_", +city.getCityId() + "_" + city.getCountryId());
                }
            }
            List<SeoTripproduct> collect = list.stream().sorted(Comparator.comparing(SeoTripproduct::getDailyPrice)).limit(21).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                seoTripproductDao.batchInsert(new DalHints(), collect);
                log.info("seoProductBatchInsert_", +city.getCityId() + "_" + city.getCountryId());
            } else {
                log.warn("TripSeoNoData_" + city.getCityId() + "_" + city.getCountryId(), JsonUtil.toJSONString(request) + "==response" + JsonUtil.toJSONString(responseType));
            }
        } catch (Exception e) {
            log.error("SeoProductItemFail", e);
        }
    }

    public QueryProductRequestType buildShoppingRequest(SeoCityDTO city) {
        SeoZone zone = osdBasicDataProxy.getZone(city.getCityId().intValue(), "zh-CN");
        if (Objects.isNull(zone)) {
            log.info("NoZone", city.getCityId() + "");
            return null;
        }

        QueryProductRequestConfig config = queryProductConfig.getConfig();
        QueryProductRequestType requestType = new QueryProductRequestType();
        requestType.setVendorGroup(config.getVendorGroup());
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setAllianceInfo(new AllianceInfoDTO() {{
            this.setAllianceId(config.getAllianceId());
            this.setSid(config.getSid());
        }});
        baseRequest.setChannelId(config.getChannelId());
        baseRequest.setCurrencyCode(config.getCurrencyCode());
        baseRequest.setLanguage(config.getLanguage());
        baseRequest.setLocale(config.getLocale());
        baseRequest.setSite(config.getSite());
        baseRequest.setSourceCountryId(city.getCountryId());
        baseRequest.setSourceFrom(config.getSourceFrom());
        baseRequest.setChannelType(config.getChannelType());
        baseRequest.setInvokeFrom(config.getInvokeFrom());

        baseRequest.setExtraTags(config.getTags());
        baseRequest.setRequestId(UUID.randomUUID().toString());
        requestType.setBaseRequest(baseRequest);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, config.getHour());
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.add(Calendar.DAY_OF_YEAR, config.getPickupTimeAddDays());
        Calendar calendarTomorrow = Calendar.getInstance();
        calendarTomorrow.set(Calendar.HOUR_OF_DAY, config.getHour());
        calendarTomorrow.set(Calendar.MINUTE, 0);
        calendarTomorrow.set(Calendar.SECOND, 0);
        calendarTomorrow.add(Calendar.DAY_OF_YEAR, (config.getPickupTimeAddDays() + config.getRentalDays()));

        LocationRequestInfo pickLocation = new LocationRequestInfo();
        pickLocation.setCityId(city.getCityId().intValue());
        if (StringUtils.isNotEmpty(zone.getAirportCode())) {
            pickLocation.setLocationType(1);
        } else {
            pickLocation.setLocationType(2);
        }
        pickLocation.setLocationCode(zone.getAirportCode());
        pickLocation.setLocationName(zone.getZoneName());
        PoiInfo poiInfo = new PoiInfo();
        poiInfo.setLatitude(zone.getLatitude());
        poiInfo.setLongitude(zone.getLongitude());
        pickLocation.setPoi(poiInfo);
        pickLocation.setDate(calendar);
        requestType.setPickupLocation(pickLocation);
        LocationRequestInfo returnLocation = new LocationRequestInfo();
        returnLocation.setCityId(city.getCityId().intValue());
        returnLocation.setLocationType(pickLocation.getLocationType());
        returnLocation.setLocationCode(zone.getAirportCode());
        returnLocation.setLocationName(zone.getZoneName());
        returnLocation.setPoi(pickLocation.getPoi());
        returnLocation.setDate(calendarTomorrow);
        requestType.setReturnLocation(returnLocation);
        requestType.setWithCache(true);
//        requestType.setWithPrice(true);
        requestType.setAge(30);
        return requestType;
    }

    public String skipBuild(QueryProductRequestType request, String vehicleCode, int klbVersion) {
        StringBuffer sb = new StringBuffer();
        sb.append("pcity=");
        sb.append(request.getPickupLocation().getCityId());
        sb.append("&rcity=");
        sb.append(request.getReturnLocation().getCityId());
        sb.append("&ptime=");
        sb.append(DateTimeFormat.format(request.getPickupLocation().getDate(), "yyyy/MM/dd HH:mm"));
        sb.append("&rtime=");
        sb.append(DateTimeFormat.format(request.getReturnLocation().getDate(), "yyyy/MM/dd HH:mm"));
        sb.append("&pcode=");
        sb.append(request.getPickupLocation().getLocationCode());
        sb.append("&rcode=");
        sb.append(request.getReturnLocation().getLocationCode());
        sb.append("&scountry=");
        sb.append(request.getBaseRequest().getSourceCountryId());
        sb.append("&age=");
        sb.append(request.getAge());
        sb.append("&vehicleCode=");
        sb.append(vehicleCode);
        sb.append("&klbVersion=");
        sb.append(klbVersion);
        return sb.toString();
    }

    public List<SeoStoreInfoDTO> convertStore(List<StoreInfo> storeInfos) {
        List<SeoStoreInfoDTO> seoStoreInfoDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(storeInfos)) {
            storeInfos.stream().filter(s -> s != null && s.getCommentInfoDto() != null && s.getCommentInfoDto().getOverallRating() != null).forEach(e -> {
                SeoStoreInfoDTO seoStoreInfoDTO = new SeoStoreInfoDTO();
                seoStoreInfoDTO.setOverallRating(e.getCommentInfoDto().getOverallRating());
                seoStoreInfoDTOS.add(seoStoreInfoDTO);
            });
        }
        return seoStoreInfoDTOS;
    }
}
