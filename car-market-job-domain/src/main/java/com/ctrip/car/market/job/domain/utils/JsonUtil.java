package com.ctrip.car.market.job.domain.utils;

import com.ctrip.soa.caravan.util.serializer.ssjson.GregorianCalendarDeserializer;
import com.ctrip.soa.caravan.util.serializer.ssjson.SSJsonSerializerConfig;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.google.gson.Gson;

import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.*;

public class JsonUtil {

    private static final ObjectMapper MAPPER = new ObjectMapper();
    private static final ObjectMapper MAPPER_WITHOUT_SCHEMA = new ObjectMapper();
    private static final Gson GSON = new Gson();
    private static final String DYNC_FILTER = "DYNC_FILTER";
    private static final String DYNC_INCLUDE = "DYNC_INCLUDE";
    private static final String LABEL_PRO_FILTER = "schema,sLabels,tLabels,vLabels,yLabels,StoreLabels,VehicleLabels";
    private static final com.google.gson.JsonParser parser = new com.google.gson.JsonParser();

    static {
        MAPPER.configure(JsonGenerator.Feature.IGNORE_UNKNOWN, true);
        // 单引号
        MAPPER.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        // 特殊字符
        MAPPER.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
        // 在反序列化时忽略在 json 中存在但 Java 对象不存在的属性
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 忽略大小写
        MAPPER.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
        // 在序列化时忽略值为 null 的属性
        MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // 日期格式化
        MAPPER.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        // 时区设置
        MAPPER.setTimeZone(TimeZone.getDefault());

        MAPPER_WITHOUT_SCHEMA.configure(JsonGenerator.Feature.IGNORE_UNKNOWN, true);
        // 单引号
        MAPPER_WITHOUT_SCHEMA.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        // 特殊字符
        MAPPER_WITHOUT_SCHEMA.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
        // 忽略大小写
        MAPPER_WITHOUT_SCHEMA.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
        // 在序列化时忽略值为 null 的属性
        MAPPER_WITHOUT_SCHEMA.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // 日期格式化
        MAPPER_WITHOUT_SCHEMA.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        // 时区设置
        MAPPER_WITHOUT_SCHEMA.setTimeZone(TimeZone.getDefault());
        // 忽略schema
        MAPPER_WITHOUT_SCHEMA.setFilterProvider(new SimpleFilterProvider().addFilter("Schema", SimpleBeanPropertyFilter.serializeAllExcept("Schema")));

        SimpleModule module = new SimpleModule();
        module.addAbstractTypeMapping(Calendar.class, GregorianCalendar.class);
        module.addDeserializer(GregorianCalendar.class, new GregorianCalendarDeserializer(new ArrayList<>(SSJsonSerializerConfig.DEFAULT_CALENDAR_DESERIALIZERS)));
        MAPPER.registerModule(module);
        MAPPER_WITHOUT_SCHEMA.registerModule(module);
    }

    public static <T> T parseObject(String json, Class<T> clazz) {
        if (json == null) {
            return null;
        }
        try {
            return MAPPER.readValue(json, clazz);
        } catch (IOException e) {
            try {
                return GSON.fromJson(json, clazz);
            } catch (Exception e2) {
                return null;
            }
        }
    }

    public static <T> List<T> parseArray(String json, Class<?> clazz) {
        if (json == null) {
            return null;
        }
        try {
            return GSON.fromJson(json, new ParameterizedTypeImpl(clazz));
        } catch (Exception e) {
            return null;
        }
    }

    private static class ParameterizedTypeImpl implements ParameterizedType {
        Class<?> clazz;

        public ParameterizedTypeImpl(Class<?> clz) {
            clazz = clz;
        }

        @Override
        public Type[] getActualTypeArguments() {
            return new Type[]{clazz};
        }

        @Override
        public Type getRawType() {
            return List.class;
        }

        @Override
        public Type getOwnerType() {
            return null;
        }
    }

    public static String toJSONString(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            try {
                return GSON.toJson(object);
            } catch (Exception e2) {
                return null;
            }
        }
    }

    public static String toJSONStringExcludeSchema(Object object) {
        try {
            return MAPPER_WITHOUT_SCHEMA.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            return toJSONString(object);
        }
    }

    public static <T> T toJson(String json, TypeReference<T> typeReference) {
        if (json == null) {
            return null;
        }
        try {
            return MAPPER.readValue(json, typeReference);
        } catch (IOException e) {
            return null;
        }
    }
}
