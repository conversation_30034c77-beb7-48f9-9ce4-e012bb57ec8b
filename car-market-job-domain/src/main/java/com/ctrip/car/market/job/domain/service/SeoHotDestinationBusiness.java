package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.customer.common.util.SharkUtil;
import com.ctrip.car.market.job.domain.config.SeoVendorCityPageConfig;
import com.ctrip.car.market.job.domain.constant.CommonConstant;
import com.ctrip.car.market.job.domain.constant.SeoConstant;
import com.ctrip.car.market.job.domain.dto.*;
import com.ctrip.car.market.job.domain.enums.SeoHotDestinationEnum;
import com.ctrip.car.market.job.domain.enums.SeoHotStatusEnums;
import com.ctrip.car.market.job.domain.proxy.GlobalPoiJavaProxy;
import com.ctrip.car.market.job.repository.dao.*;
import com.ctrip.car.market.job.repository.entity.*;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.geo.domain.value.Country;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.gs.globalpoi.soa.contract.BizCodeEnum;
import com.ctrip.platform.dal.dao.DalHints;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import static com.ctrip.car.market.job.domain.utils.GlobalVariable.*;
/**
 * <AUTHOR>
 */
@Component
public class SeoHotDestinationBusiness {

    private final ILog LOG = LogManager.getLogger(SeoHotDestinationBusiness.class);

    @Resource
    private SeoHotCountryinfoDao seoHotCountryinfoDao;

    @Resource
    private SeoHotDestinatioinfoDao seoHotDestinatioinfoDao;

    @Resource
    private SeoHotCityinfoDao seoHotCityinfoDao;

    @Resource
    private GlobalPoiJavaProxy globalPoiJavaProxy;

    @Resource
    private SeoHotInformationDao seoHotInformationDao;

    @Resource
    private SeoHotVendorDao seoHotVendorDao;

    @Resource
    private SeoHotVendorCityDao seoHotVendorCityDao;

    @Resource
    private CityRepository cityRepository;

    /**
     * 获得所有的热门城市列表
     */
    public List<SeoHotCityinfo> getSeoHotCityinfoList(boolean active)  throws SQLException {
        String sql = active ? "select * from seo_hot_cityinfo where status = 0 order by id limit ?,?"
                : "select * from seo_hot_cityinfo order by id limit ?,?";
        List<SeoHotCityinfo> seoHotCityinfoList = new ArrayList<>();
        List<SeoHotCityinfo> tempList;
        int no = 0;
        int size = SQL_QUERY_BATCH_SIZE;
        do {
            tempList = seoHotCityinfoDao.query(sql, new DalHints(), no * size, size);
            if (CollectionUtils.isNotEmpty(tempList)) {
                seoHotCityinfoList.addAll(tempList);
            }
            no++;
        } while (CollectionUtils.isNotEmpty(tempList));
        return seoHotCityinfoList;
    }

    /**
     * 获得所有的热门国家列表
     */
    public List<SeoHotCountryinfo> getSeoHotCountryinfoList(boolean active) throws SQLException {
        String sql = active ? "select * from seo_hot_countryinfo where status = 0 order by id limit ?,?"
                : "select * from seo_hot_countryinfo order by id limit ?,?";
        List<SeoHotCountryinfo> seoHotCountryinfoList = new ArrayList<>();
        List<SeoHotCountryinfo> tempList;
        int no = 0;
        int size = SQL_QUERY_BATCH_SIZE;
        do {
            tempList = seoHotCountryinfoDao.query(sql, new DalHints(), no * size, size);
            if (CollectionUtils.isNotEmpty(tempList)) {
                seoHotCountryinfoList.addAll(tempList);
            }
            no++;
        } while (CollectionUtils.isNotEmpty(tempList));
        return seoHotCountryinfoList;
    }


    /**
     * 获得所有的热门机场车站列表
     */
    public List<SeoHotDestinatioinfo> getSeoHotDestinationinfoList(boolean active) throws SQLException {
        String sql = active ? "select * from seo_hot_destinatioinfo where status = 0 order by id limit ?,?"
                : "select * from seo_hot_destinatioinfo order by id limit ?,?";
        List<SeoHotDestinatioinfo> seoHotDestinatioinfoList = new ArrayList<>();
        List<SeoHotDestinatioinfo> tempList;
        int no = 0;
        int size = SQL_QUERY_BATCH_SIZE;
        do {
            tempList = seoHotDestinatioinfoDao.query(sql, new DalHints(), no * size, size);
            if (CollectionUtils.isNotEmpty(tempList)) {
                seoHotDestinatioinfoList.addAll(tempList);
            }
            no++;
        } while (CollectionUtils.isNotEmpty(tempList));
        return seoHotDestinatioinfoList;
    }

    /**
     * 获得所有的租赁信息
     */
    public List<SeoHotInformation> getSeoHotInformation(boolean active) throws SQLException {
        String sql = active ? "select * from seo_hot_information where status = 0 order by id limit ?,?"
                : "select * from seo_hot_information order by id limit ?,?";
        List<SeoHotInformation> seoHotInformationArrayList = new ArrayList<>();
        List<SeoHotInformation> tempList;
        int no = 0;
        int size = SQL_QUERY_BATCH_SIZE;
        do {
            tempList = seoHotInformationDao.query(sql, new DalHints(), no * size, size);
            if (CollectionUtils.isNotEmpty(tempList)) {
                seoHotInformationArrayList.addAll(tempList);
            }
            no++;
        } while (CollectionUtils.isNotEmpty(tempList));
        return seoHotInformationArrayList;
    }

    public List<SeoHotVendor> getSeoHotVendorInfoList(boolean active) throws SQLException {
        String sql = active ? "select * from seo_hot_vendor where status = 0 order by id limit ?,?"
                : "select * from seo_hot_vendor order by id limit ?,?";
        List<SeoHotVendor> seoHotVendorList = new ArrayList<>();
        List<SeoHotVendor> tempList;
        int no = 0;
        int size = SQL_QUERY_BATCH_SIZE;
        do {
            tempList = seoHotVendorDao.query(sql, new DalHints(), no * size, size);
            if (CollectionUtils.isNotEmpty(tempList)) {
                seoHotVendorList.addAll(tempList);
            }
            no++;
        } while (CollectionUtils.isNotEmpty(tempList));
        return seoHotVendorList;
    }

    public List<SeoHotVendorCity> getSeoHotVendorCityInfoList(boolean active)  throws SQLException {
        String sql = active ? "select * from seo_hot_vendor_city where status = 0 order by id limit ?,?"
                : "select * from seo_hot_vendor_city order by id limit ?,?";
        List<SeoHotVendorCity> seoHotVendorCityList = new ArrayList<>();
        List<SeoHotVendorCity> tempList;
        int no = 0;
        int size = SQL_QUERY_BATCH_SIZE;
        do {
            tempList = seoHotVendorCityDao.query(sql, new DalHints(), no * size, size);
            if (CollectionUtils.isNotEmpty(tempList)) {
                seoHotVendorCityList.addAll(tempList);
            }
            no++;
        } while (CollectionUtils.isNotEmpty(tempList));
        return seoHotVendorCityList;
    }

    /**
     * 分批插入城市表
     */
    public int batchInsertIntoCity(List<SeoHotCityinfo> hotCityinfoList) throws SQLException {
        //分批插入
        int offset = 0, size = hotCityinfoList.size();
        while (offset < size) {
            seoHotCityinfoDao.batchInsert(new DalHints(), hotCityinfoList.stream().skip(offset).limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset).collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }

    /**
     * 分批插入国家表
     */
    public int batchInsertIntoCountry(List<SeoHotCountryinfo> hotCountryinfoList) throws SQLException {
        //分批插入
        int offset = 0, size = hotCountryinfoList.size();
        while (offset < size) {
            seoHotCountryinfoDao.batchInsert(new DalHints(), hotCountryinfoList.stream().skip(offset).limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset).collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }

    /**
     * 分批插入机场火车表
     */
    public int batchInsertIntoDestination(List<SeoHotDestinatioinfo> seoHotDestinatioinfoList) throws SQLException {
        //分批插入
        int offset = 0, size = seoHotDestinatioinfoList.size();
        while (offset < size) {
            seoHotDestinatioinfoDao.batchInsert(new DalHints(), seoHotDestinatioinfoList.stream().skip(offset).limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset).collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }

    /**
     * 分批插入信息表
     */
    public int batchInsertIntoInformation(List<SeoHotInformation> seoHotInformationList) throws SQLException {
        //分批插入
        int offset = 0, size = seoHotInformationList.size();
        while (offset < size) {
            seoHotInformationDao.batchInsert(new DalHints(), seoHotInformationList.stream().skip(offset).limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset).collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }

    /**
     * 分批插入热门供应商表
     */
    public int batchInsertIntoVendor(List<SeoHotVendor> seoHotVendorList) throws SQLException {
        //分批插入
        int offset = 0, size = seoHotVendorList.size();
        while (offset < size) {
            seoHotVendorDao.batchInsert(new DalHints(), seoHotVendorList.stream().skip(offset).limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset).collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }

    /**
     * 分批插入热门供应商城市表
     */
    public int batchInsertIntoVendorCity(List<SeoHotVendorCity> seoHotVendorCityList) throws SQLException {
        //分批插入
        int offset = 0, size = seoHotVendorCityList.size();
        while (offset < size) {
            seoHotVendorCityDao.batchInsert(new DalHints(), seoHotVendorCityList.stream().skip(offset).limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset).collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }


    /**
     * 分批更新机场火车表
     */
    public int batchUpdateDestination(List<SeoHotDestinatioinfo> seoHotDestinatioinfoList) throws SQLException {
        //分批更新
        int offset = 0, size = seoHotDestinatioinfoList.size();
        while (offset < size) {
            seoHotDestinatioinfoDao.batchUpdate(new DalHints(), seoHotDestinatioinfoList.stream().skip(offset).limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset).collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }

    /**
     * 分批更新城市表
     */
    public int batchUpdateCity(List<SeoHotCityinfo> seoHotCityinfoList) throws SQLException {
        //分批更新
        int offset = 0, size = seoHotCityinfoList.size();
        while (offset < size) {
            seoHotCityinfoDao.batchUpdate(new DalHints(), seoHotCityinfoList.stream().skip(offset).limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset).collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }

    /**
     * 分批更新国家表
     */
    public int batchUpdateCountry(List<SeoHotCountryinfo> seoHotCountryinfoList) throws SQLException {
        //分批更新
        int offset = 0, size = seoHotCountryinfoList.size();
        while (offset < size) {
            seoHotCountryinfoDao.batchUpdate(new DalHints(), seoHotCountryinfoList.stream().skip(offset).limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset).collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }

    /**
     * 分批更新信息表
     */
    public int batchUpdateInformation(List<SeoHotInformation> seoHotInformationList) throws SQLException {
        //分批更新
        int offset = 0, size = seoHotInformationList.size();
        while (offset < size) {
            seoHotInformationDao.batchUpdate(new DalHints(), seoHotInformationList.stream().skip(offset).limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset).collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }

    /**
     * 分批更新热门供应商表
     */
    public int batchUpdateVendor(List<SeoHotVendor> seoHotVendorList) throws SQLException {
        //分批更新
        int offset = 0, size = seoHotVendorList.size();
        while (offset < size) {
            seoHotVendorDao.batchUpdate(new DalHints(), seoHotVendorList.stream().skip(offset).limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset).collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }

    /**
     * 分批更新热门供应商城市表
     */
    public int batchUpdateVendorCity(List<SeoHotVendorCity> seoHotVendorCities) throws SQLException {
        //分批更新
        int offset = 0, size = seoHotVendorCities.size();
        while (offset < size) {
            seoHotVendorCityDao.batchUpdate(new DalHints(), seoHotVendorCities.stream().skip(offset).limit(Math.min(offset + SQL_BATCH_SIZE, size) - offset).collect(Collectors.toList()));
            offset += SQL_BATCH_SIZE;
        }
        return offset;
    }

    /**
     * 构建国家表info
     */
    public void buildNewCountry(Map<Long, Country> countryMap, Set<Long> requestInsertCountry) throws SQLException {
        List<SeoHotCountryinfo> hotCountryinfoList = new ArrayList<>();
        for(Long countryId : requestInsertCountry){
            try {
                String englishName = countryMap.get(countryId).getEnglishName();
                if (countryId.equals(TURKEY_ID)) {
                    englishName = TURKEY;
                }
                SeoHotCountryinfo seoHotCountryinfo = new SeoHotCountryinfo();
                seoHotCountryinfo.setCountryId(countryId.intValue());
                seoHotCountryinfo.setStatus(SeoHotStatusEnums.ACTIVE.getCode());
                seoHotCountryinfo.setCountryName(englishName);
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(PRE_URL).append("to-").append(englishName.toLowerCase().replace(" ", "-"))
                        .append("-").append(countryId).append("/");
                seoHotCountryinfo.setUrl(stringBuilder.toString());
                hotCountryinfoList.add(seoHotCountryinfo);
            }catch (Exception e) {
                LOG.error("SEO build new country error !", e);
            }
        }
        batchInsertIntoCountry(hotCountryinfoList);
    }

    /**
     * 构建城市表info
     */
    public void buildNewCity(Map<Long, City> cityMap, Map<Long, Country> countryMap, Map<Long, GetHostDestinationInfo> requestInsertCity, Map<Integer, SeoHotCountryinfo> countryinfoMap) throws SQLException {
        List<SeoHotCityinfo> hotCityinfoList = new ArrayList<>();
        for(Long cityId : requestInsertCity.keySet()){
            try {
                String englishName = cityMap.get(cityId).getEnglishName();
                String pickupCountryId = requestInsertCity.get(cityId).getPickupcountryid();
                Long countryId = Long.parseLong(pickupCountryId);
                SeoHotCityinfo seoHotCityinfo = new SeoHotCityinfo();
                seoHotCityinfo.setCityId(cityId.intValue());
                seoHotCityinfo.setCityName(englishName);
                seoHotCityinfo.setStatus(SeoHotStatusEnums.ACTIVE.getCode());
                seoHotCityinfo.setCountryId(countryId.intValue());
                StringBuilder stringBuilder = new StringBuilder();
                String countryName = countryMap.get(countryId) != null ? countryMap.get(countryId).getEnglishName() : countryinfoMap.get(countryId.intValue()).getCountryName();
                if (countryId.equals(TURKEY_ID)) {
                    countryName = TURKEY;
                }
                stringBuilder.append(PRE_URL)
                        .append("to-").append(countryName.toLowerCase().replace(" ", "-")).append("-").append(countryId).append("/")
                        .append(englishName.toLowerCase().replace(" ", "-")).append("-").append(cityId).append("/");
                seoHotCityinfo.setUrl(stringBuilder.toString());
                hotCityinfoList.add(seoHotCityinfo);
            }catch (Exception e) {
                LOG.error("SEO build new city error !", e);
            }
        }
        batchInsertIntoCity(hotCityinfoList);

    }

    /**
     * 构建机场表info
     */
    public void buildNewDestination(Map<String, GetHostDestinationInfo> requestCodeList, Map<Long, Country> countryMap, Map<Long, City> cityMap) throws SQLException {
        ArrayList<String> poiCodeList = new ArrayList<>(requestCodeList.keySet());
        // 拿机场的poiId
        Map<String, Long> poiIdByPoiCode = globalPoiJavaProxy.getPoiIdByPoiCode(poiCodeList, BizCodeEnum.THREECODE_AIRPORT);
        List<SeoHotDestinatioinfo> hotDestinatioinfoList = new ArrayList<>();
        for (Map.Entry<String, GetHostDestinationInfo> info : requestCodeList.entrySet()) {
            try {
                Long countryId = Long.valueOf(info.getValue().getPickupcountryid());
                Long cityId = Long.valueOf(info.getValue().getPickupcityid());
                String countryName = countryMap.get(countryId).getEnglishName().replace(" ", "-");
                String cityName = cityMap.get(cityId).getEnglishName().toLowerCase().replace(" ", "-");
                SeoHotDestinatioinfo seoHotDestinatioinfo = new SeoHotDestinatioinfo();
                seoHotDestinatioinfo.setCityId(cityId.intValue());
                seoHotDestinatioinfo.setStatus(SeoHotStatusEnums.ACTIVE.getCode());
                StringBuilder stringBuilder = new StringBuilder();
                if (countryId.equals(TURKEY_ID)) {
                    countryName = TURKEY;
                }
                stringBuilder.append(PRE_URL)
                        .append("to-")
                        .append(countryName.toLowerCase()).append("-").append(countryId).append("/")
                        .append(cityName).append("-")
                        .append(cityId)
                        .append("/")
                        .append(info.getValue().getPickuplocationname().toLowerCase().replace(" ", "-"))
                        .append("-").append(info.getValue().getPickuplocationcode().toLowerCase())
                        .append("/");
                seoHotDestinatioinfo.setUrl(stringBuilder.toString());
                seoHotDestinatioinfo.setPoiType(AIRPORT_CODE);
                seoHotDestinatioinfo.setPoiId(poiIdByPoiCode.get(info.getValue().getPickuplocationcode()) == null ? 0 : poiIdByPoiCode.get(info.getValue().getPickuplocationcode()));
                GetHostDestinationInfo getHostDestinationInfo = requestCodeList.get(info.getValue().getPickuplocationcode());
                int num = getHostDestinationInfo != null && getHostDestinationInfo.getAdvanceorder_cnt() != null ? getHostDestinationInfo.getAdvanceorder_cnt().intValue() : 0;
                seoHotDestinatioinfo.setOrderNum(num);
                seoHotDestinatioinfo.setPoiName(info.getValue().getPickuplocationname());
                seoHotDestinatioinfo.setCountryId(countryId.intValue());
                seoHotDestinatioinfo.setPoiCode(info.getValue().getPickuplocationcode().toUpperCase());
                hotDestinatioinfoList.add(seoHotDestinatioinfo);
            }catch (Exception e){
                LOG.error("SEO build new destination error !", e);
            }
        }
        // 插入机场火车表
        batchInsertIntoDestination(hotDestinatioinfoList);
    }

    public void buildSeoHotInformation(GetCarSeoRentalInfo info, SeoHotInformation seoHotInformation) {
        seoHotInformation.setCityId(Integer.valueOf(info.getPickcityid()));
        seoHotInformation.setCountryId(Integer.valueOf(info.getPickcountryid()));
        seoHotInformation.setPoiType(AIRPORT_CODE);
        seoHotInformation.setPoiCode(info.getPickuplocationcode());
        seoHotInformation.setVendorId(Integer.valueOf(info.getVendorid()));
        seoHotInformation.setVendorName(info.getVendorname());
        seoHotInformation.setVehicleGroupId(Integer.valueOf(info.getVehivlegroupid()));
        seoHotInformation.setVehicleGroupName(info.getVehivlegroupname());
        seoHotInformation.setTenancy(Integer.valueOf(info.getCommon_period()));
        seoHotInformation.setStatus(SeoHotStatusEnums.ACTIVE.getCode());
    }

    /**
     * 构建推送 ibu 实体
     */
    public List<HotDestinationMassageDTO> buildHotDestinationMassageDTO(MessageParams params) {
        List<HotDestinationMassageDTO> list = new ArrayList<>();
        String url = params.getUrl();
        try{
            if (StringUtils.isEmpty(LOCALE)) {
                return list;
            }
            String[] locales = LOCALE.split(",");
            for (String locale : locales) {
                HotDestinationMassageDTO hotDestinationMassageDTO = new HotDestinationMassageDTO();
                hotDestinationMassageDTO.setSource(SOURCE);
                hotDestinationMassageDTO.setChannel(CHANNEL);
                hotDestinationMassageDTO.setPageType(params.getSeoHotDestinationEnum().getPageType());
                hotDestinationMassageDTO.setIsHot(0);
                hotDestinationMassageDTO.setState(1);

                String[] split = locale.split("-");
                if ("xx".equalsIgnoreCase(split[1])) {
                    hotDestinationMassageDTO.setUrl(url);
                    hotDestinationMassageDTO.setSite(split[0].toUpperCase());
                } else if ("GB".equalsIgnoreCase(split[1])) {
                    hotDestinationMassageDTO.setUrl(url.replace("www", "uk"));
                    hotDestinationMassageDTO.setSite(split[1].toUpperCase());
                } else {
                    hotDestinationMassageDTO.setUrl(url.replace("www", split[1].toLowerCase()));
                    hotDestinationMassageDTO.setSite(split[1].toUpperCase());
                }
                hotDestinationMassageDTO.setProducts(buildSiteMapProductDTO(params, split[1]));
                String sharkByDefault = SharkUtil.getSharkKeyByLanguageWithAppId(CommonConstant.SEO_SHARK_APP_ID_INT, params.getSeoHotDestinationEnum().getSharkKey(), locale);
                if (params.getSeoHotDestinationEnum() == SeoHotDestinationEnum.SUPPLIER) {
                    hotDestinationMassageDTO.setKeyword(sharkByDefault.replace("${0}", params.getVendorName()));
                    list.add(hotDestinationMassageDTO);
                } else if (params.getSeoHotDestinationEnum() == SeoHotDestinationEnum.SUPPLIER_CITY) {
                    sharkByDefault = sharkByDefault.replace("${0}", params.getVendorName());
                    // 设置keyword值
                    String productValue = getProductValue(params);
                    if (params.getNameMap().containsKey(locale) && params.getNameMap().get(locale).containsKey(productValue) && StringUtils.isNotBlank(params.getNameMap().get(locale).get(productValue))) {
                        String translationName = params.getNameMap().get(locale).get(productValue);
                        hotDestinationMassageDTO.setKeyword(sharkByDefault.replace("${1}", translationName));
                        list.add(hotDestinationMassageDTO);
                    }
                } else {
                    // 设置keyword值
                    String productValue = getProductValue(params);
                    if (params.getNameMap().containsKey(locale) && params.getNameMap().get(locale).containsKey(productValue) && StringUtils.isNotBlank(params.getNameMap().get(locale).get(productValue))) {
                        String translationName = params.getNameMap().get(locale).get(productValue);
                        hotDestinationMassageDTO.setKeyword(sharkByDefault.replace("{0}", translationName));
                        list.add(hotDestinationMassageDTO);
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("SEO buildHotDestinationMassageDTO error !", e);
        }
        return list;
    }

    private String getProductValue(MessageParams params) {
        switch (params.getSeoHotDestinationEnum()){
            case CITY:
                return params.getCityId().toString();
            case COUNTRY:
                return params.getCountryId().toString();
            case AIRPORT:
                return params.getPoiCode();
            case SUPPLIER:
                return params.getVendorCode();
            case SUPPLIER_CITY:
                return params.getVendorCityId().toString();
            default:
                return null;
        }
    }

    private List<ProductDTO> buildSiteMapProductDTO(MessageParams params, String site) {
        List<ProductDTO> result = new ArrayList<>();
        switch (params.getSeoHotDestinationEnum()){
            case CITY:
                result.add(new ProductDTO(SeoConstant.CITY_ID_SITEMAP, params.getCityId().toString()));
                result.add(new ProductDTO(SeoConstant.COUNTRY_ID_SITEMAP, params.getCountryId().toString()));
                if ("TW".equalsIgnoreCase(site) && TW_CITY_IDS.contains(params.getCityId())) {
                    result.add(new ProductDTO(SeoConstant.PROVINCE_ID_SITEMAP, params.getProvinceId()));
                }
                break;
            case COUNTRY:
                result.add(new ProductDTO(SeoConstant.COUNTRY_ID_SITEMAP, params.getCountryId().toString()));
                break;
            case AIRPORT:
                result.add(new ProductDTO(SeoConstant.AIRPORT_CODE_SITEMAP, params.getPoiCode()));
                if (params.getPoiId() != null) {
                    result.add(new ProductDTO(SeoConstant.POI_ID_SITEMAP, params.getPoiId().toString()));
                }
                if ("TW".equalsIgnoreCase(site) && TW_CITY_IDS.contains(params.getCityId())) {
                    result.add(new ProductDTO(SeoConstant.PROVINCE_ID_SITEMAP, params.getProvinceId()));
                }
                result.add(new ProductDTO(SeoConstant.COUNTRY_ID_SITEMAP, params.getCountryId().toString()));
                break;
            case SUPPLIER:
                result.add(new ProductDTO(SeoConstant.VENDOR_ID_SITEMAP, params.getVendorCode()));
                break;
            case SUPPLIER_CITY:
                result.add(new ProductDTO(SeoConstant.VENDOR_ID_SITEMAP, params.getVendorCode()));
                result.add(new ProductDTO(SeoConstant.VENDOR_CITY_ID_SITEMAP, params.getVendorCityId().toString()));
                break;
        }
        return result;
    }

    public SeoHotVendor buildSeoHotVendor(SeoVendorCityPageConfig seoVendorCityPageConfig, SeoHotVendor seoHotVendor) {
        seoHotVendor.setVendorId(seoVendorCityPageConfig.getVendorCode());
        seoHotVendor.setVendorName(seoVendorCityPageConfig.getVendorName());
        seoHotVendor.setStatus(0);
        // 设置供应商页面url
        String stringBuilder = PRE_URL
                + "supplier/"
                + seoVendorCityPageConfig.getVendorName().toLowerCase().replace(" ", "-")
                + "-"
                + seoVendorCityPageConfig.getVendorCode().toLowerCase().replace(" ", "-")
                + "/";
        seoHotVendor.setUrl(stringBuilder);
        return seoHotVendor;
    }

    public SeoHotVendorCity buildSeoHotVendorCity(SeoVendorCityPageConfig seoVendorCityPageConfig, SeoHotVendorCity seoHotVendorCity, Integer cityId) {
        try {
            seoHotVendorCity.setVendorId(seoVendorCityPageConfig.getVendorCode());
            seoHotVendorCity.setVendorName(seoVendorCityPageConfig.getVendorName());
            seoHotVendorCity.setStatus(0);
            seoHotVendorCity.setCityId(cityId);
            seoHotVendorCity.setCityName("");
            seoHotVendorCity.setUrl("");
            City city = cityRepository.findOne(Long.valueOf(cityId));
            if (city != null  && StringUtils.isNotBlank(city.getEnglishName())) {
                seoHotVendorCity.setCityName(city.getEnglishName());
                String stringBuilder = PRE_URL
                        + seoHotVendorCity.getCityName().toLowerCase().replace(" ", "-")
                        + "-"
                        + seoHotVendorCity.getCityId()
                        + "/supplier/"
                        + seoHotVendorCity.getVendorName().toLowerCase().replace(" ", "-")
                        + "-"
                        + seoHotVendorCity.getVendorId().toLowerCase().replace(" ", "-")
                        + "/";
                seoHotVendorCity.setUrl(stringBuilder);
                return seoHotVendorCity;
            }
        } catch (Exception e) {
            LOG.error("buildSeoHotVendorCity error", e);
        }
        return seoHotVendorCity;
    }
}

