package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.CpnProductRedirectUrlDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.ProductRedirectUrlMapper;
import com.ctrip.car.market.job.repository.entity.CpnProductRedirectUrl;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class ProductRedirectUrlMessageConvert extends AbstractConvertMessage<String, List<CpnProductRedirectUrl>, List<CpnProductRedirectUrlDO>> implements ConvertMessage<String, List<CpnProductRedirectUrlDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private ProductRedirectUrlMapper mapper;

    public ProductRedirectUrlMessageConvert() {
        super(TabelEnum.ProductRedirectUrl);
    }

    @Override
    public Map<String, List<CpnProductRedirectUrlDO>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public String getKey(String ids) {
        return ids;
    }

    @Override
    public List<CpnProductRedirectUrl> getData(String id) throws Exception {
        List<CpnProductRedirectUrl> data = service.queryProductRedirectUrlByUnionType(id);
        return CollectionUtils.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<CpnProductRedirectUrlDO> mapper(List<CpnProductRedirectUrl> value) {
        return mapper.to(value);
    }
}
