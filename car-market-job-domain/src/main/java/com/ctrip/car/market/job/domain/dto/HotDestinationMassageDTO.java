package com.ctrip.car.market.job.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class HotDestinationMassageDTO {
    private String site;
    private String url;
    // 当前仅google
    private String source;
    // carhire
    private String channel;
    // 页面类型 COUNTRY CITY AIRPORT
    private String pageType;
    // 传0
    private Integer isHot;
    // 内链展示的锚文本
    private String keyword;
    // keyword 搜索量
    private Integer sv;
    //  1:online， 0: offline
    private Integer state;
    //
    private String path;
    // 是否有价/可售：1-有价/可售；0-无价/可售
    private Integer canSale;
    // 可售数量 -1无数据
    private Integer saleableQuantity;
    // 是否中国境内 1 在，0不在，-1 无数据
    private Integer inChina;
    // 是否有301跳转， 1 是
    private Integer is301Jump;
    // 是否有图片， 1 有
    private Integer hasPicture;
    // 图片
    private List<String> pictureUrls;
    // 标签
    private List<String> tags;
    // 产品信息
    private List<ProductDTO> products;
}