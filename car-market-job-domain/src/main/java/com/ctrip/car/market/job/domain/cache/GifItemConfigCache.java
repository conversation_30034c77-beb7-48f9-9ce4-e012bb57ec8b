package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.GiftItemconfigDO;
import com.ctrip.car.market.job.domain.message.GifItemConfigMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllGifItemConfigService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.GIF_ITEM_CONFIG_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.GifItemConfigCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(GifItemConfigCache.Name)
@CreateCacheArea(area = "public")
public class GifItemConfigCache extends BasicCacheAbstract<String, List<GiftItemconfigDO>> {

    public static final String Name = GifItemConfigCacheName;

    @Resource
    private GifItemConfigMessageConvert convert;

    @Resource
    private QueryAllGifItemConfigService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = GIF_ITEM_CONFIG_HASH_KEY, remotePreLoadName = Name)
    private Cache<String, List<GiftItemconfigDO>> cache;


    @Override
    public ConvertMessage<String, List<GiftItemconfigDO>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<String, List<GiftItemconfigDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<String, List<GiftItemconfigDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<String, List<GiftItemconfigDO>> getLoader() {
        return null;
    }
}
