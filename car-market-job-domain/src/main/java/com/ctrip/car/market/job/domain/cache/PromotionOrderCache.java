package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnPromotionOrderDO;
import com.ctrip.car.market.job.domain.message.PromotionOrderMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllPromotionOrderService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.PROMOTION_ORDER_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.PromotionOrderCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(PromotionOrderCache.Name)
@CreateCacheArea(area = "public")
public class PromotionOrderCache extends BasicCacheAbstract<Long, CpnPromotionOrderDO>{

    public static final String Name = PromotionOrderCacheName;

    @Resource
    private PromotionOrderMessageConvert convert;

    @Resource
    private QueryAllPromotionOrderService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = PROMOTION_ORDER_HASH_KEY, remotePreLoadName = Name)
    private Cache<Long, CpnPromotionOrderDO> cache;


    @Override
    public ConvertMessage<Long, CpnPromotionOrderDO> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Long, CpnPromotionOrderDO> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, CpnPromotionOrderDO> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, CpnPromotionOrderDO> getLoader() {
        return null;
    }
}
