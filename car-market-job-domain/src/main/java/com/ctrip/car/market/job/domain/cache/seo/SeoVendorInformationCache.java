package com.ctrip.car.market.job.domain.cache.seo;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO;
import com.ctrip.car.market.job.domain.cache.BasicCacheAbstract;
import com.ctrip.car.market.job.domain.service.seo.QueryAllSeoHotVendorInformationService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.SEO_VENDOR_INFORMATION_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.SeoVendorInformationCacheName;

@Component(SeoVendorInformationCache.Name)
@CreateCacheArea(area = "public")
public class SeoVendorInformationCache extends BasicCacheAbstract<String, List<SeoHotVendorInformationDO>> {

    public static final String Name = SeoVendorInformationCacheName;

    @Resource
    private QueryAllSeoHotVendorInformationService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = SEO_VENDOR_INFORMATION_KEY, remotePreLoadName = Name)
    private Cache<String, List<SeoHotVendorInformationDO>> cache;

    @Override
    public ConvertMessage<String, List<SeoHotVendorInformationDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<String, List<SeoHotVendorInformationDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<String, List<SeoHotVendorInformationDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<String, List<SeoHotVendorInformationDO>> getLoader() {
        return null;
    }
}

