package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.ActReturnCityInfoDO;
import com.ctrip.car.market.job.domain.message.ActivityReturnCityMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllActivityReturnCityService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.ACT_RETURN_CITY_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.IsdActivityReturnCityCacheName;

@Component(ActivityReturnCityCache.Name)
@CreateCacheArea(area = "public")
public class ActivityReturnCityCache extends BasicCacheAbstract<Long, List<ActReturnCityInfoDO>>{

    public static final String Name = IsdActivityReturnCityCacheName;

    @Resource
    private QueryAllActivityReturnCityService service;

    @Resource
    private ActivityReturnCityMessageConvert convert;

    @IncUpdateConsumerByQmq(subject = "car.market.activity.data.change", convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = ACT_RETURN_CITY_KEY, remotePreLoadName = Name)
    private Cache<Long, List<ActReturnCityInfoDO>> cache;

    @Override
    public ConvertMessage<Long, List<ActReturnCityInfoDO>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Long, List<ActReturnCityInfoDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, List<ActReturnCityInfoDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, List<ActReturnCityInfoDO>> getLoader() {
        return null;
    }
}