package com.ctrip.car.market.job.domain.dto;

import java.util.Optional;

public class BiVendorInformationDto {

    private Long mysql_id;

    private String vendorcode;

    private String brand_cn_name;

    private String pickupcityid;

    private String pickupcityname;

    private String pickuplocationcode;

    private String pickuplocationname;

    private String pickuplocationtype;

    private Integer requestid_cnt;

    private String hot_vehiclecode;

    private String car_model_group_id;

    private Integer hot_usedays;

    private Integer storecnt;

    public Long getMysql_id() {
        return mysql_id;
    }

    public void setMysql_id(Long mysql_id) {
        this.mysql_id = mysql_id;
    }

    public String getVendorcode() {
        return vendorcode;
    }

    public void setVendorcode(String vendorcode) {
        this.vendorcode = vendorcode;
    }

    public String getBrand_cn_name() {
        return brand_cn_name;
    }

    public void setBrand_cn_name(String brand_cn_name) {
        this.brand_cn_name = brand_cn_name;
    }

    public String getPickupcityid() {
        return pickupcityid;
    }

    public void setPickupcityid(String pickupcityid) {
        this.pickupcityid = pickupcityid;
    }

    public String getPickupcityname() {
        return pickupcityname;
    }

    public void setPickupcityname(String pickupcityname) {
        this.pickupcityname = pickupcityname;
    }

    public String getPickuplocationcode() {
        return pickuplocationcode;
    }

    public void setPickuplocationcode(String pickuplocationcode) {
        this.pickuplocationcode = pickuplocationcode;
    }

    public String getPickuplocationname() {
        return pickuplocationname;
    }

    public void setPickuplocationname(String pickuplocationname) {
        this.pickuplocationname = pickuplocationname;
    }

    public String getPickuplocationtype() {
        return pickuplocationtype;
    }

    public void setPickuplocationtype(String pickuplocationtype) {
        this.pickuplocationtype = pickuplocationtype;
    }

    public Integer getRequestid_cnt() {
        return Optional.ofNullable(requestid_cnt).orElse(0);
    }

    public void setRequestid_cnt(Integer requestid_cnt) {
        this.requestid_cnt = requestid_cnt;
    }

    public String getHot_vehiclecode() {
        return hot_vehiclecode;
    }

    public void setHot_vehiclecode(String hot_vehiclecode) {
        this.hot_vehiclecode = hot_vehiclecode;
    }

    public String getCar_model_group_id() {
        return car_model_group_id;
    }

    public void setCar_model_group_id(String car_model_group_id) {
        this.car_model_group_id = car_model_group_id;
    }

    public Integer getHot_usedays() {
        return Optional.ofNullable(hot_usedays).orElse(0);
    }

    public void setHot_usedays(Integer hot_usedays) {
        this.hot_usedays = hot_usedays;
    }

    public Integer getStorecnt() {
        return Optional.ofNullable(storecnt).orElse(0);
    }

    public void setStorecnt(Integer storecnt) {
        this.storecnt = storecnt;
    }
}
