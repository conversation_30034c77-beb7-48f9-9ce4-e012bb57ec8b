package com.ctrip.car.market.job.domain.service;

import com.ctrip.car.market.job.domain.dto.FaqVendorDTO;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.car.market.job.repository.dao.carseodb.SeoFaqDao;
import com.ctrip.car.market.job.repository.entity.carseodb.SeoFaq;
import com.ctrip.car.osd.shopping.api.entity.PriceInfo;
import com.ctrip.car.osd.shopping.api.entity.QueryProductResponseType;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class FaqUpdateBusiness {
    @Autowired
    private SeoFaqDao seoFaqDao;
    private final ILog log = LogManager.getLogger(getClass());

    public boolean adddOrUpdateFaq(QueryProductResponseType responseType, Long cityID, int sourceCountry) {
        if (1 != sourceCountry) return false;
        try {
            if (responseType != null && !CollectionUtils.isEmpty(responseType.getVehicles())) {
                List<FaqVendorDTO> faqVendorDTOS = new ArrayList<>();
                responseType.getVendors().stream()
                        .forEach(y -> {
                            FaqVendorDTO faqVendorDTO = new FaqVendorDTO();
                            faqVendorDTO.setVendorName(y.getVendorName());
                            List<PriceInfo> collect = y.getProductInfoDtoList().stream().map(p -> {
                                PriceInfo priceInfo = p.getPriceInfoDtoList().stream().min(Comparator.comparing(PriceInfo::getCurrentDailyPrice)).orElse(null);
                                return priceInfo;
                            }).filter(Objects::nonNull).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(collect)) {
                                PriceInfo priceInfo = collect.stream().min(Comparator.comparing(PriceInfo::getCurrentDailyPrice)).orElse(null);
                                if (priceInfo != null) {
                                    faqVendorDTO.setPrice(priceInfo.getCurrentDailyPrice());
                                }
                            }
                            faqVendorDTOS.add(faqVendorDTO);

                        });
                SeoFaq seoFaq = new SeoFaq();
                List<FaqVendorDTO> collect = faqVendorDTOS.stream().sorted(Comparator.comparing(FaqVendorDTO::getPrice)).limit(3).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    String content = JsonUtil.toJSONString(collect);
                    seoFaq.setVendorsContent(content);
                    seoFaq.setHotVendorName(collect.get(0).getVendorName());
                    seoFaq.setGroupEName(responseType.getVehicles().get(0).getGroupEName());
                    seoFaq.setHotGroupName(responseType.getVehicles().get(0).getGroupName());
                    seoFaq.setCityId(cityID);
                    seoFaq.setActive(true);

                }
                SeoFaq query = new SeoFaq();
                query.setCityId(cityID);
                query.setActive(true);
                List<SeoFaq> seoFaqs = seoFaqDao.queryBy(query);
                if (CollectionUtils.isNotEmpty(seoFaqs)) {
                    if (CollectionUtils.isNotEmpty(collect)) {
                        seoFaqDao.batchDelete(seoFaqs);
                        seoFaqDao.insert(seoFaq);
                    }
                    log.info("adddOrUpdateFaq1", JsonUtil.toJSONString(seoFaq));
                } else {
                    if (CollectionUtils.isNotEmpty(collect)) {
                        log.info("adddOrUpdateFaq2", JsonUtil.toJSONString(seoFaq));
                        seoFaqDao.insert(seoFaq);
                    }
                }
            } else {
                log.warn("adddOrUpdateFaq", JsonUtil.toJSONString(responseType));
                return false;
            }
        } catch (Exception ex) {
            log.error("adddOrUpdateFaq", ex);
            return false;
        }
        return true;
    }
}
