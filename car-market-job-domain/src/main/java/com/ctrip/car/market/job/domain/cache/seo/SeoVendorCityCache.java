package com.ctrip.car.market.job.domain.cache.seo;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorCityDO;
import com.ctrip.car.market.job.domain.cache.BasicCacheAbstract;
import com.ctrip.car.market.job.domain.service.seo.QueryAllHotVendorCityService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.SEO_VENDOR_CITY_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.SeoVendorCityCacheName;

@Component(SeoVendorCityCache.Name)
@CreateCacheArea(area = "public")
public class SeoVendorCityCache extends BasicCacheAbstract<String, List<SeoHotVendorCityDO>> {

    public static final String Name = SeoVendorCityCacheName;

    @Resource
    private QueryAllHotVendorCityService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = SEO_VENDOR_CITY_KEY, remotePreLoadName = Name)
    private Cache<String, List<SeoHotVendorCityDO>> cache;

    @Override
    public ConvertMessage<String, List<SeoHotVendorCityDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<String, List<SeoHotVendorCityDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<String, List<SeoHotVendorCityDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<String, List<SeoHotVendorCityDO>> getLoader() {
        return null;
    }
}
