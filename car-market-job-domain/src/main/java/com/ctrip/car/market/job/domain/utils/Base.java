package com.ctrip.car.market.job.domain.utils;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.framework.foundation.Foundation;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @Description:
 * @create 2025/7/16 21:12
 */
public final class Base {
    private final ILog log = LogManager.getLogger(Base.class);

    private static ThreadLocal<MessageDigest> md5er = new ThreadLocal<MessageDigest>() {
        @Override
        protected MessageDigest initialValue() {
            try {
                return MessageDigest.getInstance("MD5");
            } catch (NoSuchAlgorithmException e) {
                return null;
            }
        }
    };

    private Base() {
    }

    public static boolean isPrd() {
        String envName = getEnv();
        return "prd".equals(envName) || "pro".equals(envName) || "prod".equals(envName);
    }

    public static boolean isNotPrd(){
        return !isPrd();
    }

    public static String getEnv() {
        if (Foundation.server().getEnv() != null) {
            return Foundation.server().getEnv().getName().toLowerCase();
        } else {
            return "local";
        }
    }

    public static String getAppId() {
        return Foundation.app().getAppId();
    }

    public static String getMD5(String plainText) {
        byte[] input;
        try {
            input = plainText.getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            return null;
        }
        return getMD5(input);
    }

    public static String getMD5(byte[] input) {
        md5er.get().update(input);
        byte[] data = md5er.get().digest();
        // 字节数组转换成十六进制字符串，形成最终的密文
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < data.length; i++) {
            int v = data[i] & 0xff;
            if (v < 16) {
                sb.append(0);
            }
            sb.append(Integer.toHexString(v));
        }
        return sb.toString();
    }


    public static <T> T ifNull(T obj, T ifNullValue) {
        if (obj == null) {
            return ifNullValue;
        }
        return obj;
    }

}
