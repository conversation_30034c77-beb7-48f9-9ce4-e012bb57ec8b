package com.ctrip.car.market.job.domain.service.seo;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoVendorCommentScoreDO;
import com.ctrip.car.market.job.domain.mapper.SeoVendorCommentScoreMapper;
import com.ctrip.car.market.job.repository.entity.SeoVendorCommentScore;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllSeoVendorCommentScoreService implements CachePreLoader<String, List<SeoVendorCommentScoreDO>>, CacheLoader<String, List<SeoVendorCommentScoreDO>> {

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private SeoVendorCommentScoreMapper mapper;

    private Map<String, List<SeoVendorCommentScoreDO>> getAllVendorScore() throws Exception {
        List<SeoVendorCommentScore> data = marketDBService.queryAllVendorScore();
        return data.stream().filter(l -> StringUtils.isNotEmpty(l.getVendorCode())).map(mapper::to).collect(Collectors.groupingBy(l -> l.getVendorCode().toLowerCase()));
    }

    @Override
    public List<SeoVendorCommentScoreDO> load(String area, String key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<String, List<SeoVendorCommentScoreDO>> loadAll(String area, Set<String> keys) throws Throwable {
        return getAllVendorScore();
    }

    @Override
    public Map<String, List<SeoVendorCommentScoreDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllVendorScore();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
