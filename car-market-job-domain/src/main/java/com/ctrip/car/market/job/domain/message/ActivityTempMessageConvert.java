package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.common.entity.act.ActivityTempContent;
import com.ctrip.car.market.job.common.entity.ActTempInfoDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.ActTempInfoMapper;
import com.ctrip.car.market.job.domain.utils.JsonUtils;
import com.ctrip.car.market.job.repository.entity.activity.ActCtriptempinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@Component
public class ActivityTempMessageConvert extends AbstractConvertMessage<Long, ActCtriptempinfo, ActTempInfoDO> implements ConvertMessage<Long, ActTempInfoDO> {

    @Resource
    private ActivityService service;

    @Resource
    private ActTempInfoMapper mapper;

    public ActivityTempMessageConvert() {
        super(TabelEnum.ActivityTemp);
    }

    @Override
    public Map<Long, ActTempInfoDO> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Long getKey(String ids) {
        return Long.valueOf(ids);
    }

    @Override
    public ActCtriptempinfo getData(Long id) throws Exception {
        ActCtriptempinfo data = service.queryByTempId(id);
        if (data != null && Objects.equals(data.getStatus(), 1)) {
            data.setContent(JsonUtils.toObject(data.getTempContent(), ActivityTempContent.class));
            if (data.getContent() == null) {
                return null;
            }
            return data;
        }
        return null;
    }

    @Override
    public ActTempInfoDO mapper(ActCtriptempinfo value) {
        return mapper.to(value);
    }
}
