package com.ctrip.car.market.job.domain.mq;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.car.market.job.domain.config.JobConfig;
import com.ctrip.car.market.job.domain.dto.LimitingConditions;
import com.ctrip.car.market.job.domain.dto.UseStationDto;
import com.ctrip.car.market.job.domain.proxy.PromotionProxy;
import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.market.job.domain.utils.EmailUtil;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.car.market.job.repository.entity.CpnRestrictedCondition;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.ctrip.soa.platform.account.promocodeservice.data.v1.PromotionStrategyItem;
import com.ctrip.soa.platform.account.promocodeservice.message.v1.GetPromotionStrategyResponseType;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class PromotionInspect {

    @Resource
    private RestrictedService restrictedService;

    @Resource
    private PromotionProxy promotionProxy;

    @Resource
    private JobConfig jobConfig;

    @QmqConsumer(prefix = "car.marketing.promotion.data.change", consumerGroup = "CarMarketPromotionInspect")
    public void changeMessage(Message message) {
        String data = message.getStringProperty("dataChange");
        if (Strings.isNullOrEmpty(data)) {
            return;
        }
        try {
            DataChange otterObject = JsonUtil.parseObject(data, DataChange.class);
            if (otterObject.isInsert() && otterObject.getTableName().equalsIgnoreCase("cpn_restricted_condition")) {
                Integer conditionId = Integer.valueOf(otterObject.getAfterColumnValue("conditionId"));
                CpnRestrictedCondition cpnRestrictedCondition = restrictedService.queryRestrictedCondition(conditionId);
                if (cpnRestrictedCondition == null || !Objects.equals(cpnRestrictedCondition.getIsValid(), 1)
                        || !StringUtils.equalsIgnoreCase(cpnRestrictedCondition.getConditionName(), "NEW")) {
                    return;
                }
                LimitingConditions conditions = JsonUtil.parseObject(cpnRestrictedCondition.getContent(), LimitingConditions.class);
                if (conditions == null) {
                    return;
                }
                if (!conditions.getIsISD() && !conditions.getIsOSD()) {
                    return;
                }
                //查询优惠策略信息
                GetPromotionStrategyResponseType promotionResponse = promotionProxy.getPromotionStrategy(cpnRestrictedCondition.getPromotionId());
                if (promotionResponse == null || promotionResponse.getPromotionStrategy() == null) {
                    CLogUtil.warn("PromotionInspect", "promotion strategy is null");
                    burialPoint(cpnRestrictedCondition.getPromotionId(), "noPromotionStrategy");
                    return;
                }
                //未配置用车产线
                if (StringUtils.isNotEmpty(promotionResponse.getPromotionStrategy().getUserProductLineIDs())
                        && !promotionResponse.getPromotionStrategy().getUserProductLineIDs().contains("18")) {
                    burialPoint(cpnRestrictedCondition.getPromotionId(), "noCarLine");
                    return;
                }
                sendEmail(promotionResponse);
            }
        } catch (Exception e) {
            CLogUtil.error("promotionChangeMessage", e);
        }
    }

    private void burialPoint(Integer promotionId, String errorType) {
        Metrics.build().withTag("promotionId", promotionId.toString())
                .withTag("result", "0")
                .withTag("errorType", errorType)
                .recordOne("PromotionInspect");
    }

    private void sendEmail(GetPromotionStrategyResponseType promotionResponse) {
        if (!jobConfig.isSendEmail()) {
            return;
        }
        PromotionStrategyItem promotionStrategy = promotionResponse.getPromotionStrategy();
        Map<Integer, UseStationDto> useStationMap = jobConfig.getUseStationList().stream().collect(Collectors.toMap(UseStationDto::getUseStation, l -> l, (k1, k2) -> k1));
        StringBuilder sb = new StringBuilder();
        sb.append(jobConfig.getPromotionId()).append(": ").append(promotionStrategy.getPromotionID());
        sb.append("<br/>");
        sb.append(jobConfig.getPromotionName()).append(": ").append(promotionStrategy.getName());
        sb.append("<br/>");
        sb.append(jobConfig.getPromotionDisplayName()).append(": ").append(promotionStrategy.getDisplayName());
        sb.append("<br/>");
        sb.append(jobConfig.getUseProductLine()).append(": ").append(promotionStrategy.getUserProductLineIDsName());
        sb.append("<br/>");
        List<Integer> stationList = Lists.newArrayList(promotionStrategy.getUseStation().split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
        List<String> stationNameList = stationList.stream().map(l -> {
            if (useStationMap.containsKey(l)) {
                return useStationMap.get(l).getStationName();
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        sb.append(jobConfig.getUseStation()).append(": ").append(StringUtils.join(stationNameList, ","));
        EmailUtil.sendEmail(jobConfig.getPromotionEmailSubject(), jobConfig.getEmailAcceptors(), Lists.newArrayList(), sb.toString());
    }
}
