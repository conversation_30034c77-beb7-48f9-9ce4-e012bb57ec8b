package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.ActProductDO;
import com.ctrip.car.market.job.repository.entity.activity.ActProductids;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ActProductInfoMapper {

    ActProductDO to(ActProductids value);

    List<ActProductDO> to(List<ActProductids> values);
}
