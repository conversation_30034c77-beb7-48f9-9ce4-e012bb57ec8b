package com.ctrip.car.market.job.domain.service.seo;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotCountryinfoDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotCountryinfoMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotCountryinfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllSeoHotCountryService implements CachePreLoader<Integer, List<SeoHotCountryinfoDO>>, CacheLoader<Integer, List<SeoHotCountryinfoDO>> {

    @Resource
    private MarketDBService service;

    @Resource
    private SeoHotCountryinfoMapper mapper;

    private Map<Integer, List<SeoHotCountryinfoDO>> getALlHotCountry() throws Exception {
        List<SeoHotCountryinfo> data = service.queryAllHotCountry();
        return data.stream().filter(l -> l.getCountryId() != null).map(mapper::to).collect(Collectors.groupingBy(SeoHotCountryinfoDO::getCountryId));
    }

    @Override
    public List<SeoHotCountryinfoDO> load(String area, Integer key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Integer, List<SeoHotCountryinfoDO>> loadAll(String area, Set<Integer> keys) throws Throwable {
        return getALlHotCountry();
    }

    @Override
    public Map<Integer, List<SeoHotCountryinfoDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getALlHotCountry();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
