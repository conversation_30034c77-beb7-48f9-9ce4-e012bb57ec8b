package com.ctrip.car.market.job.domain.dto;

public class ChannelNumberDTO {

    private Long channelNumberId;
    private String channelNumberName;
    private Long tertiaryChannelId;
    private String cooperationId;
    private String cooperationName;
    private String cooperationModeId;
    private String cooperationModeName;
    private String pageLocationId;
    private String pageLocationName;
    private Long channelMarketId;
    private String remark;
    private String operatorId;
    private String operator;
    private String createTime;
    private String updateTime;

    public Long getChannelNumberId() {
        return channelNumberId;
    }

    public void setChannelNumberId(Long channelNumberId) {
        this.channelNumberId = channelNumberId;
    }

    public String getChannelNumberName() {
        return channelNumberName;
    }

    public void setChannelNumberName(String channelNumberName) {
        this.channelNumberName = channelNumberName;
    }

    public Long getTertiaryChannelId() {
        return tertiaryChannelId;
    }

    public void setTertiaryChannelId(Long tertiaryChannelId) {
        this.tertiaryChannelId = tertiaryChannelId;
    }

    public String getCooperationId() {
        return cooperationId;
    }

    public void setCooperationId(String cooperationId) {
        this.cooperationId = cooperationId;
    }

    public String getCooperationName() {
        return cooperationName;
    }

    public void setCooperationName(String cooperationName) {
        this.cooperationName = cooperationName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getCooperationModeId() {
        return cooperationModeId;
    }

    public void setCooperationModeId(String cooperationModeId) {
        this.cooperationModeId = cooperationModeId;
    }

    public String getCooperationModeName() {
        return cooperationModeName;
    }

    public void setCooperationModeName(String cooperationModeName) {
        this.cooperationModeName = cooperationModeName;
    }

    public String getPageLocationId() {
        return pageLocationId;
    }

    public void setPageLocationId(String pageLocationId) {
        this.pageLocationId = pageLocationId;
    }

    public String getPageLocationName() {
        return pageLocationName;
    }

    public void setPageLocationName(String pageLocationName) {
        this.pageLocationName = pageLocationName;
    }

    public Long getChannelMarketId() {
        return channelMarketId;
    }

    public void setChannelMarketId(Long channelMarketId) {
        this.channelMarketId = channelMarketId;
    }
}
