package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.MsgAllianceinfoDO;
import com.ctrip.car.market.job.domain.mapper.MsgAllianceinfoMapper;
import com.ctrip.car.market.job.repository.entity.MsgAllianceinfo;
import com.ctrip.car.market.job.repository.service.LogDBService;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllMsgAllianceinfoService implements CachePreLoader<String, List<MsgAllianceinfoDO>>, CacheLoader<String, List<MsgAllianceinfoDO>> {

    @Resource
    private LogDBService service;

    @Resource
    private MsgAllianceinfoMapper mapper;

    private Map<String, List<MsgAllianceinfoDO>> getAll() throws Exception {
        List<MsgAllianceinfo> data = service.queryAllMsgAllianceinfo();
        Map<String, List<MsgAllianceinfoDO>> map = Maps.newHashMap();
        map.put("ALL", data.stream().map(mapper::to).collect(Collectors.toList()));
        return map;
    }

    @Override
    public List<MsgAllianceinfoDO> load(String area, String key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<String, List<MsgAllianceinfoDO>> loadAll(String area, Set<String> keys) throws Throwable {
        return getAll();
    }

    @Override
    public Map<String, List<MsgAllianceinfoDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAll();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
