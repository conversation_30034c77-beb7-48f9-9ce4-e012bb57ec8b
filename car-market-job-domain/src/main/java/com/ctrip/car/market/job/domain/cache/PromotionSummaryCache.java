package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnPromotionSummaryDO;
import com.ctrip.car.market.job.domain.message.PromotionSummaryMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllPromotionSummaryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.PROMOTION_SUMMARY_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.PromotionSummaryCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(PromotionSummaryCache.Name)
@CreateCacheArea(area = "public")
public class PromotionSummaryCache extends BasicCacheAbstract<Integer, List<CpnPromotionSummaryDO>> {

    public static final String Name = PromotionSummaryCacheName;

    @Resource
    private PromotionSummaryMessageConvert convert;

    @Resource
    private QueryAllPromotionSummaryService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = PROMOTION_SUMMARY_HASH_KEY, remotePreLoadName = Name)
    private Cache<Integer, List<CpnPromotionSummaryDO>> cache;


    @Override
    public ConvertMessage<Integer, List<CpnPromotionSummaryDO>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Integer, List<CpnPromotionSummaryDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, List<CpnPromotionSummaryDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, List<CpnPromotionSummaryDO>> getLoader() {
        return null;
    }
}
