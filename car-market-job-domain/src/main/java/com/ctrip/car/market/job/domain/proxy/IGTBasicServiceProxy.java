package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.igt.RequestHeader;
import com.ctrip.igt.basicservice.interfaces.IGTBasicServiceClient;
import com.ctrip.igt.basicservice.interfaces.dto.BasicAirportDTO;
import com.ctrip.igt.basicservice.interfaces.message.BasicAirportRequestType;
import com.ctrip.igt.basicservice.interfaces.message.BasicAirportResponseType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Component
public class IGTBasicServiceProxy {
    private final ILog log = LogManager.getLogger(IGTBasicServiceProxy.class);

    private IGTBasicServiceClient client = IGTBasicServiceClient.getInstance();

    private static final Integer MAX_NUMBER = 500;

    public List<BasicAirportDTO> basicAirportQuery(List<String> airportCode){
        List<BasicAirportDTO> result = new ArrayList<>();
        try {
            int limit = (airportCode.size() + MAX_NUMBER - 1) / MAX_NUMBER;
            Stream.iterate(0, n -> n + 1).limit(limit).forEach(i -> {
                BasicAirportRequestType basicAirportRequestType = new BasicAirportRequestType();
                basicAirportRequestType.setCodeList(airportCode.stream().skip((long) i * MAX_NUMBER).limit(MAX_NUMBER).collect(Collectors.toList()));
                basicAirportRequestType.setVersion(1);
                basicAirportRequestType.setRequestHeader(new RequestHeader());
                basicAirportRequestType.getRequestHeader().setLocale("en-us");
                BasicAirportResponseType responseType = new BasicAirportResponseType();
                try {
                    responseType = client.basicAirportQuery(basicAirportRequestType);
                } catch (Exception e) {
                    log.warn("query poiId failed", e);
                }
                if (responseType != null && !responseType.getResult().isEmpty()) {
                    result.addAll(responseType.getResult());
                }
            });
            return result;
        } catch (Exception e) {
            log.warn("query poiId failed", e);
            return result;
        }
    }
}
