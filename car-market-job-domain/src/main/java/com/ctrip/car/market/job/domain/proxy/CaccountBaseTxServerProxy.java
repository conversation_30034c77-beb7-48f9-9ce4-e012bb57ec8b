package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.payment.soa.caccount.base.tx.server.CaccountBaseTxServerClient;
import com.ctrip.payment.soa.caccount.base.tx.server.model.PayInReq;
import com.ctrip.payment.soa.caccount.base.tx.server.model.PayInRsp;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description:
 * @create 2025/7/8 11:49
 */
@Component
public class CaccountBaseTxServerProxy {
    private final CaccountBaseTxServerClient client = CaccountBaseTxServerClient.getInstance();

    public PayInRsp payIn(PayInReq requestType) {
        try {
            PayInRsp responseType = client.payIn(requestType);
            Map<String, String> tag = Maps.newHashMap();
            tag.put("uid", requestType.getUid());
            CLogUtil.tagInfo("CaccountBaseTxServerClient::payIn", tag,"request", requestType, "response", responseType);
            return responseType;
        } catch (Exception e) {
            CLogUtil.warn("payIn", e.getMessage());
            return null;
        }
    }

}
