package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.tour.ai.one.service.GetDataRequestType;
import com.ctrip.tour.ai.one.service.TourAIOneServiceClient;
import com.ctriposs.baiji.rpc.client.RequestInfo;
import com.ctriposs.baiji.rpc.client.RequestRouteStrategy;

public class TourAIOneServiceClientForGroup {

    private static final TourAIOneServiceClient client;
    private static final String GROUP_1 = "custom-offline_group";

    static {
        client = TourAIOneServiceClient.getInstance();
        // some other config
        client.addRequestRouteStrategy(new RequestRouteStrategy() {
            @Override
            public String routeStrategyId() {
                return "my_route";
            }

            @Override
            public String apply(RequestInfo requestInfo) {
                GetDataRequestType requestObject = (GetDataRequestType) requestInfo.getRequestObject();
                String apiName = requestObject.getApiName();
                if ("getDimPrdCarStoreCooperatedList".equalsIgnoreCase(apiName) || "getEdwOrdCarPopularAirportDf".equalsIgnoreCase(apiName)) {
                    return GROUP_1;
                }
                return null;
            }
        });
    }

    public static TourAIOneServiceClient getClient() {
        return client;
    }
}
