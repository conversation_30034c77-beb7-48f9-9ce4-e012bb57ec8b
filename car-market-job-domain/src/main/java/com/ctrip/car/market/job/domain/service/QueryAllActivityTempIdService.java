package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.repository.entity.activity.ActCtriptempinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllActivityTempIdService implements CachePreLoader<String, List<Long>>, CacheLoader<String, List<Long>> {

    private final ILog log = LogManager.getLogger(QueryAllActivityTempIdService.class);

    @Resource
    private ActivityService service;

    private Map<String, List<Long>> getAllActivityTempId() throws Exception {
        List<ActCtriptempinfo> data = service.queryAllTemp();
        Map<String, List<Long>> map = Maps.newHashMap();
        map.put("ALL", data.stream().map(ActCtriptempinfo::getTmpId).distinct().collect(Collectors.toList()));
        return map;
    }

    @Override
    public List<Long> load(String area, String key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<String, List<Long>> loadAll(String area, Set<String> keys) throws Throwable {
        return getAllActivityTempId();
    }

    @Override
    public Map<String, List<Long>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllActivityTempId();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
