package com.ctrip.car.market.job.domain.mq;

import com.ctrip.car.market.job.domain.dto.ChannelNumberDTO;
import com.ctrip.car.market.job.domain.dto.ChannelNumberMsgDTO;
import com.ctrip.car.market.job.domain.enums.ChannelEventTypeEnum;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.repository.entity.ChannelNumber;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

@Component
public class ChannelNumberMsgListen {

    private final ILog log = LogManager.getLogger(ChannelNumberMsgListen.class);

    @Resource
    private MarketDBService marketDBService;

    @QmqConsumer(prefix = "dcs.car.channel.number.changed", consumerGroup = "100011922-task-watch")
    public void channelNumberConsumer(Message msg) {
        try {
            log.info("ChannelNumberMsgListenMessage", JsonUtil.toJSONString(msg));
            String eventType = msg.getStringProperty("eventType");
            String channelNumberId = msg.getStringProperty("channelNumberId");
            String data = msg.getStringProperty("data");
            if (StringUtils.isEmpty(eventType)) {
                return;
            }
            ChannelNumberMsgDTO numberMsg = null;
            if (!ChannelEventTypeEnum.DELETE.getType().equals(eventType)) {
                numberMsg = JsonUtil.parseObject(data,ChannelNumberMsgDTO.class);
            }
            List<ChannelNumber> channelNumbers = null;
            ChannelNumber sample = new ChannelNumber();
            if (StringUtils.isNotEmpty(channelNumberId)) {
                sample.setId(Long.valueOf(channelNumberId));
                channelNumbers = marketDBService.queryChannelNumber(sample);
            }
            if (ChannelEventTypeEnum.DELETE.getType().equals(eventType)) {
                if (CollectionUtils.isEmpty(channelNumbers)) {
                    log.warn("ChannelNumberMsgListen", "not find channel munber_" + eventType + "_" + channelNumberId);
                    return;
                }
                if (sample.getId() != null) {
                    int delete = marketDBService.deleteChannelNumber(sample);
                    setLog(delete, msg);
                }
            } else if (ChannelEventTypeEnum.INSERT.getType().equals(eventType)) {
                if (CollectionUtils.isNotEmpty(channelNumbers)) {
                    log.warn("ChannelNumberMsgListen", "channel munber have exit_" + eventType + "_" + channelNumberId);
                    return;
                }
                ChannelNumber channelNumber = getChannelByMessage(numberMsg.getChannelNumber());
                channelNumber.setId(Long.valueOf(channelNumberId));
                int insert = marketDBService.insertChannelNumberWithId(channelNumber);
                setLog(insert, msg);
            } else if (ChannelEventTypeEnum.UPDATE.getType().equals(eventType)) {
                if (CollectionUtils.isEmpty(channelNumbers)) {
                    log.warn("ChannelNumberMsgListen", "not find channel munber_" + eventType + "_" + channelNumberId);
                    ChannelNumber channelNumber = getChannelByMessage(numberMsg.getChannelNumber());
                    channelNumber.setId(Long.valueOf(channelNumberId));
                    int insert = marketDBService.insertChannelNumberWithId(channelNumber);
                    setLog(insert, msg);
                    return;
                }
                ChannelNumber channelByMessage = getChannelByMessage(numberMsg.getChannelNumber());
                channelByMessage.setId(Long.valueOf(channelNumberId));
                int update = marketDBService.updateChannelNumber(channelByMessage);
                setLog(update, msg);
            }
        } catch (Exception e) {
            log.error("ChannelNumberMsgListen", e);
        }

    }

    private ChannelNumber getChannelByMessage(ChannelNumberDTO msg) {
        ChannelNumber channelNumber = new ChannelNumber();
        channelNumber.setId(msg.getChannelNumberId());
        channelNumber.setChannelName(msg.getChannelNumberName());
        channelNumber.setTertiaryChannelId(msg.getTertiaryChannelId());
        channelNumber.setCooperationId(msg.getCooperationId());
        channelNumber.setCooperationName(msg.getCooperationName());
        channelNumber.setRemark(msg.getRemark());
        channelNumber.setOperatorId(msg.getOperatorId());
        channelNumber.setOperator(msg.getOperator());
        channelNumber.setDatachangeCreatetime(stringToTimestamp(msg.getCreateTime()));
        channelNumber.setDatachangeLasttime(stringToTimestamp(msg.getUpdateTime()));

        channelNumber.setPageLocationId(msg.getPageLocationId());
        channelNumber.setPageLocationName(msg.getPageLocationName());
        channelNumber.setChannelMarketId(msg.getChannelMarketId());
        channelNumber.setCooperationModeId(msg.getCooperationModeId());
        channelNumber.setCooperationModeName(msg.getCooperationModeName());
        return channelNumber;
    }

    private void setLog(int isSuccess, Message msg) {
        if (isSuccess > 0) {
            log.info("ChannelNumberMsgListen", JsonUtil.toJSONString(msg));
        } else {
            log.warn("ChannelNumberMsgListen", JsonUtil.toJSONString(msg));
        }
    }

    private Timestamp stringToTimestamp(String str) {
        Timestamp ts = new Timestamp(System.currentTimeMillis());
        try {
            ts = Timestamp.valueOf(str);
            System.out.println(ts);
        } catch (Exception e) {
            log.error("stringToTimestamp",e);
        }
        return ts;
    }
}
