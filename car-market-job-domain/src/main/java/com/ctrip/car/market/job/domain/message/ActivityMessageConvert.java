package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.common.entity.act.CustomContent;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.ActInfoMapper;
import com.ctrip.car.market.job.domain.utils.JsonUtils;
import com.ctrip.car.market.job.repository.entity.activity.ActCtripactinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@Component
public class ActivityMessageConvert extends AbstractConvertMessage<Long, ActCtripactinfo, ActInfoDO> implements ConvertMessage<Long, ActInfoDO> {

    @Resource
    private ActivityService service;

    @Resource
    private ActInfoMapper mapper;

    public ActivityMessageConvert() {
        super(TabelEnum.Activity);
    }

    @Override
    public Map<Long, ActInfoDO> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Long getKey(String ids) {
        return Long.valueOf(ids);
    }

    @Override
    public ActCtripactinfo getData(Long id) throws Exception {
        ActCtripactinfo data = service.queryByPk(id);
        if (data != null && Objects.equals(data.getStatus(), 1)) {
            data.setContent(JsonUtils.toObject(data.getCustomContent(), CustomContent.class));
            return data;
        }
        return null;
    }

    @Override
    public ActInfoDO mapper(ActCtripactinfo value) {
        return mapper.to(value);
    }
}
