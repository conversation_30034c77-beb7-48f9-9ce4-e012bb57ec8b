package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.config.AllCityQueryConfig;
import com.ctrip.car.market.job.domain.service.ProductManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;



@Component
public class IsdPkgSchedule {



    @Autowired
    private AllCityQueryConfig allCityQueryConfig;

    @Autowired
    private ProductManagerService productManagerService;

    @QSchedule("IsdOtherCityQuery")
    public void isdOtherCityQuery(Parameter parameter) {
        productManagerService.start(allCityQueryConfig.getIsdOtherCityQueryConfig());
    }


    @QSchedule("IsdCoreCityQuery")
    public void IsdCoreCityQuery(Parameter parameter) {
        productManagerService.start(allCityQueryConfig.getIsdCoreCityQueryConfig());
    }



}
