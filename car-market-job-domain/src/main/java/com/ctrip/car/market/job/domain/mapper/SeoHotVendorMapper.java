package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO;
import com.ctrip.car.market.job.repository.entity.SeoHotVendor;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SeoHotVendorMapper {

    SeoHotVendorDO to(SeoHotVendor value);

    List<SeoHotVendorDO> to(List<SeoHotVendor> values);
}
