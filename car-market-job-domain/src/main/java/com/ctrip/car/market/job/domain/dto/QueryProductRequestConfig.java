package com.ctrip.car.market.job.domain.dto;
import java.util.Map;

public class QueryProductRequestConfig {
    private Integer vendorGroup;
    private Integer allianceId;
    private Integer sid;
    private Integer channelId;
    private String currencyCode;
    private String language;
    private String locale;
    private String site;
    private String sourceFrom;
    private Integer channelType;
    private Integer InvokeFrom;
    private Integer age;
    private Integer pickupTimeAddDays;
    private Integer rentalDays;
    private Integer hour;
    private Map<String, String> tags;

     public Integer getVendorGroup() {
          return vendorGroup;
     }

     public void setVendorGroup(Integer vendorGroup) {
          this.vendorGroup = vendorGroup;
     }

     public Integer getAllianceId() {
          return allianceId;
     }

     public void setAllianceId(Integer allianceId) {
          this.allianceId = allianceId;
     }

     public Integer getSid() {
          return sid;
     }

     public void setSid(Integer sid) {
          this.sid = sid;
     }

     public Integer getChannelId() {
          return channelId;
     }

     public void setChannelId(Integer channelId) {
          this.channelId = channelId;
     }

     public String getCurrencyCode() {
          return currencyCode;
     }

     public void setCurrencyCode(String currencyCode) {
          this.currencyCode = currencyCode;
     }

     public String getLanguage() {
          return language;
     }

     public void setLanguage(String language) {
          this.language = language;
     }

     public String getLocale() {
          return locale;
     }

     public void setLocale(String locale) {
          this.locale = locale;
     }

     public String getSite() {
          return site;
     }

     public void setSite(String site) {
          this.site = site;
     }

     public String getSourceFrom() {
          return sourceFrom;
     }

     public void setSourceFrom(String sourceFrom) {
          this.sourceFrom = sourceFrom;
     }

     public Integer getChannelType() {
          return channelType;
     }

     public void setChannelType(Integer channelType) {
          this.channelType = channelType;
     }

     public Integer getInvokeFrom() {
          return InvokeFrom;
     }

     public void setInvokeFrom(Integer invokeFrom) {
          InvokeFrom = invokeFrom;
     }

     public Integer getAge() {
          return age;
     }

     public void setAge(Integer age) {
          this.age = age;
     }

     public Map<String, String> getTags() {
          return tags;
     }

     public void setTags(Map<String, String> tags) {
          this.tags = tags;
     }

    public Integer getPickupTimeAddDays() {
        return pickupTimeAddDays;
    }

    public void setPickupTimeAddDays(Integer pickupTimeAddDays) {
        this.pickupTimeAddDays = pickupTimeAddDays;
    }

    public Integer getRentalDays() {
        return rentalDays;
    }

    public void setRentalDays(Integer rentalDays) {
        this.rentalDays = rentalDays;
    }

    public Integer getHour() {
        return hour;
    }

    public void setHour(Integer hour) {
        this.hour = hour;
    }
}
