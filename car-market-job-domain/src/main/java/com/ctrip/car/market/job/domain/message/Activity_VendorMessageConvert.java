package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.common.entity.act.CustomContent;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.ActInfoMapper;
import com.ctrip.car.market.job.domain.utils.JsonUtils;
import com.ctrip.car.market.job.repository.entity.activity.ActCtripactinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class Activity_VendorMessageConvert extends AbstractConvertMessage<Long, List<ActCtripactinfo>, List<ActInfoDO>> implements ConvertMessage<Long, List<ActInfoDO>> {

    private final ILog log = LogManager.getLogger(Activity_VendorMessageConvert.class);

    @Resource
    private ActivityService service;

    @Resource
    private ActInfoMapper mapper;

    public Activity_VendorMessageConvert() {
        super(TabelEnum.Activity_Vendor);
    }

    @Override
    public Map<Long, List<ActInfoDO>> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Long getKey(String ids) {
        return Long.valueOf(ids);
    }

    @Override
    public List<ActCtripactinfo> getData(Long id) throws Exception {
        List<ActCtripactinfo> data = service.queryActivityByVendor(id);
        for (ActCtripactinfo item : data) {
            try {
                item.setContent(JsonUtils.toObject(item.getCustomContent(), CustomContent.class));
            } catch (Exception e) {
                log.warn("getData", e);
            }
        }
        return CollectionUtils.isNotEmpty(data) ? data : null;
    }

    @Override
    public List<ActInfoDO> mapper(List<ActCtripactinfo> value) {
        return mapper.to(value);
    }
}
