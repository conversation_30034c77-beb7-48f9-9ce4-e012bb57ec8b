package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.risk.RiskUnifiedAccessService.RiskUnifiedAccessServiceClient;
import com.ctrip.risk.access.RiskUnifiedAccessService.UnifiedRiskVerifyRequest;
import com.ctrip.risk.access.RiskUnifiedAccessService.UnifiedRiskVerifyResponse;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description:
 * @create 2025/7/18 10:36
 */
@Component
public class RiskUnifiedAccessServiceProxy {
    private final RiskUnifiedAccessServiceClient client = RiskUnifiedAccessServiceClient.getInstance();

    public UnifiedRiskVerifyResponse riskVerify(UnifiedRiskVerifyRequest requestType) {
        try {
            UnifiedRiskVerifyResponse responseType = client.riskVerify(requestType);
            CLogUtil.tagInfo("RiskUnifiedAccessServiceClient::riskVerify", new HashMap<>(),"request", requestType, "response", responseType);
            if (responseType != null && Objects.equals(responseType.getCode(),"0")) {
                return responseType;
            }
            return responseType;
        } catch (Exception e) {
            CLogUtil.warn("riskVerify", e.getMessage());
            return null;
        }
    }
}
