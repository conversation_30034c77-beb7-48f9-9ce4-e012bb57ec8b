package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO;
import com.ctrip.car.market.job.repository.entity.SeoHotVendorInformation;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SeoHotVendorInformationMapper {

    SeoHotVendorInformationDO to(SeoHotVendorInformation value);

    List<SeoHotVendorInformationDO> to(List<SeoHotVendorInformation> values);
}
