package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnLabelDO;
import com.ctrip.car.market.job.domain.mapper.LabelMapper;
import com.ctrip.car.market.job.repository.entity.CpnLabel;
import com.ctrip.car.market.job.repository.service.LabelService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllLabelService implements CachePreLoader<Long, CpnLabelDO>, CacheLoader<Long, CpnLabelDO> {

    @Resource
    private LabelService service;

    @Resource
    private LabelMapper mapper;

    private Map<Long, CpnLabelDO> getAllLabel() throws Exception {
        List<CpnLabel> data = service.queryAllLabel();
        return data.stream().collect(Collectors.toMap(CpnLabel::getCode, mapper::to));
    }

    @Override
    public CpnLabelDO load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, CpnLabelDO> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllLabel();
    }

    @Override
    public Map<Long, CpnLabelDO> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllLabel();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
