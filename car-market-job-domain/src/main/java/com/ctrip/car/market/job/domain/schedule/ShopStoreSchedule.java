package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.config.ShopOpenConfig;
import com.ctrip.car.market.job.domain.dto.GetDataParam;
import com.ctrip.car.market.job.domain.dto.GetDataResponseInfo;
import com.ctrip.car.market.job.domain.dto.StoreItem;
import com.ctrip.car.market.job.domain.proxy.TourAIOneServiceClientForGroup;
import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.repository.entity.ShopStore;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.tour.ai.one.service.GetDataRequestType;
import com.ctrip.tour.ai.one.service.GetDataResponseType;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class ShopStoreSchedule {

    private final ILog log = LogManager.getLogger(ShopStoreSchedule.class);

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private ShopOpenConfig shopOpenConfig;

    @QSchedule("car.market.shop.store.sync")
    public void task() throws Exception {
        List<GetDataResponseInfo> apiDataList = getApiData();
        if (CollectionUtils.isEmpty(apiDataList)) {
            log.warn("apiData no data");
            return;
        }
        log.info("start");
        List<ShopStore> shopStoreList = getAllShopStore();
        Set<Long> storeIdSet = shopStoreList.stream().map(ShopStore::getStoreId).collect(Collectors.toSet());
        Set<Long> supplierIdSet = shopStoreList.stream().map(ShopStore::getSupplierId).collect(Collectors.toSet());
        for (GetDataResponseInfo dataInfo : apiDataList) {
            if (dataInfo == null || dataInfo.getSupplier_id() == null) {
                continue;
            }
            //开通店铺场景
            if (dataInfo.getStore_id() == null && dataInfo.getVendor_id() == null) {
                if (!supplierIdSet.contains(dataInfo.getSupplier_id())) {
                    addShopStore(dataInfo.getSupplier_id(), null, null);
                }
            } else {
                //新增门店
                if (!storeIdSet.contains(dataInfo.getStore_id())) {
                    addShopStore(dataInfo.getSupplier_id(), dataInfo.getVendor_id(), dataInfo.getStore_id());
                }
            }

        }
        log.info("end");
    }

    @QSchedule("car.market.shop.store.init")
    public void init() throws Exception {
        if (CollectionUtils.isNotEmpty(getAllShopStore())) {
            return;
        }
        Set<Long> storeIdSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(shopOpenConfig.getStoreList())) {
            for (StoreItem storeItem : shopOpenConfig.getStoreList()) {
                if (storeItem.getSupplierId() == null || storeItem.getVendorId() == null) {
                    continue;
                }
                for (Long storeId : storeItem.getStoreIdList()) {
                    if (storeId == null || storeIdSet.contains(storeId)) {
                        continue;
                    }
                    storeIdSet.add(storeId);
                    addShopStore(storeItem.getSupplierId(), storeItem.getVendorId(), storeId);
                }
            }
        }
    }


    private void addShopStore(Long supplierId, Long vendorId, Long storeId) throws Exception {
        ShopStore shopStore = new ShopStore();
        shopStore.setSupplierId(supplierId);
        shopStore.setStoreId(storeId);
        shopStore.setVendorId(vendorId);
        shopStore.setIsActive(true);
        marketDBService.insertShopStore(shopStore);
    }


    public List<ShopStore> getAllShopStore() throws Exception {
        List<ShopStore> shopStoreList = Lists.newArrayList();
        List<ShopStore> temp;
        int no = 0;
        int size = 1000;
        do {
            temp = marketDBService.queryShopStoreByPage(no * size, size);
            if (CollectionUtils.isNotEmpty(temp)) {
                shopStoreList.addAll(temp);
            }
            no++;
        } while (CollectionUtils.isNotEmpty(temp));
        return shopStoreList;
    }

    public List<GetDataResponseInfo> getApiData() throws Exception {
        long offset = 0;
        long row = 500;
        List<GetDataResponseInfo> result = Lists.newArrayList();
        List<GetDataResponseInfo> temp = null;
        do {
            GetDataParam params = new GetDataParam();
            params.setRows(row);
            params.setOffset(offset);
            GetDataRequestType soaReq = new GetDataRequestType();
            soaReq.setApiName("getDimPrdCarStoreCooperatedList");
            soaReq.setToken("Wo4PANIo-1450-1713779601760");
            soaReq.setParams(JsonUtil.toJSONString(params));
            GetDataResponseType soaRes = TourAIOneServiceClientForGroup.getClient().getData(soaReq);
            CLogUtil.info("OneServiceGetData", soaReq, soaRes);
            CLogUtil.esLog("OneServiceGetData", null, soaReq, soaRes);
            if (StringUtils.isNotBlank(soaRes.getBacks())) {
                temp = JsonUtil.parseArray(soaRes.getBacks(), GetDataResponseInfo.class);
                if (CollectionUtils.isNotEmpty(temp)) {
                    result.addAll(temp);
                    offset = temp.stream().map(GetDataResponseInfo::getMysql_id).max(Comparator.comparingLong(o -> o)).get();
                }
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }
}
