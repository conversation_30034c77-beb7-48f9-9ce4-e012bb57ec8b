package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.config.JobConfig;
import com.ctrip.car.market.job.domain.dto.SeoInfMsg;
import com.ctrip.car.market.job.domain.proxy.OsdBasicDataProxy;
import com.ctrip.car.market.job.domain.proxy.SeoPlatformProxy;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.domain.utils.QConfigUtil;
import com.ctrip.car.market.job.domain.utils.RedisUtil;
import com.ctrip.car.market.job.repository.entity.CarKalabCity;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.car.osd.basicdataservice.dto.Airport;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.repository.CountryRepository;
import com.ctrip.dcs.geo.domain.repository.ProvinceRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.geo.domain.value.Country;
import com.ctrip.dcs.geo.domain.value.Province;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageProducer;
import qunar.tc.qmq.MessageSendStateListener;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Component
public class SeoInfoPushSchedule {

    private final ILog log = LogManager.getLogger(SeoInfoPushSchedule.class);

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private CountryRepository countryRepository;

    @Resource
    private ProvinceRepository provinceRepository;

    @Resource
    private CityRepository cityRepository;

    @Resource
    private OsdBasicDataProxy osdBasicDataProxy;

    @Resource
    private MessageProducer messageProducer;

    @Resource
    private SeoPlatformProxy seoPlatformProxy;
    @Autowired
    private JobConfig jobConfig;

    @QSchedule("car.market.seo.platform.data.push")
    public void push() throws Exception {
        List<CarKalabCity> cityList = marketDBService.queryAllOsdCity();
        Map<Long, Integer> cityOrderMap = cityList.stream().filter(l -> l.getOrderVolume() != null).collect(Collectors.toMap(CarKalabCity::getCityId, CarKalabCity::getOrderVolume, (k1, k2) -> k1));
        List<Long> cityIdList = cityList.stream().map(CarKalabCity::getCityId).distinct().collect(Collectors.toList());
        List<Long> countryIdList = cityList.stream().map(CarKalabCity::getCountryId).distinct().collect(Collectors.toList());
        List<Long> provinceIdList = cityList.stream().map(CarKalabCity::getProvinceId).distinct().collect(Collectors.toList());
        List<String> airportCodeList = cityList.stream().map(CarKalabCity::getAirportCode).distinct().collect(Collectors.toList());
        List<String> localeList = getLocale();
        countryPush(countryIdList, localeList);
        provincePush(provinceIdList, localeList);
        cityPush(cityIdList, localeList, cityOrderMap);
        airportPush(airportCodeList, localeList);
    }

    @QSchedule("car.market.seo.platform.data.init.cache")
    public void initCache() throws Exception {
        List<CarKalabCity> cityList = marketDBService.queryAllOsdCity();
        List<Long> cityIdList = cityList.stream().map(CarKalabCity::getCityId).distinct().collect(Collectors.toList());
        List<Long> countryIdList = cityList.stream().map(CarKalabCity::getCountryId).distinct().collect(Collectors.toList());
        List<String> airportCodeList = cityList.stream().map(CarKalabCity::getAirportCode).distinct().collect(Collectors.toList());
        List<String> localeList = getLocale();
        countryIdList.forEach(li -> buildCache(1, li.toString(), localeList));
        cityIdList.forEach(li -> buildCache(3, li.toString(), localeList));
        airportCodeList.forEach(li -> buildCache(4, li, localeList));
    }

    private List<String> getLocale() {
        String localeStr = QConfigUtil.getSeoHotConfigOrDefault("seo.ibu.qmq.local", "en-ID,en-PH,nl-NL,pt-BR,tr-TR,pt-PT,de-DE,fr-FR,th-TH,en-AU,en-SG,es-ES,it-IT,en-CA,en-GB,en-US,en-XX,ja-JP,ko-KR,zh-HK,zh-TW");
        return Lists.newArrayList(localeStr.split(","));
    }

    private void countryPush(List<Long> countryIdList, List<String> localeList) throws Exception {
        for (String locale : localeList) {
            for (Long countryId : countryIdList) {
                Country country = countryRepository.findOne(countryId, locale);
                if (country == null || StringUtils.isEmpty(country.getTranslationName())) {
                    continue;
                }
                SeoInfMsg infMsg = buildMsg(0, locale, countryId.toString(), country.getTranslationName(), null);
                sendMassage(infMsg);
            }
        }
    }

    private void provincePush(List<Long> provinceIdList, List<String> localeList) throws Exception {
        for (String locale : localeList) {
            for (Long provinceId : provinceIdList) {
                Province province = provinceRepository.findOne(provinceId, locale);
                if (province == null || StringUtils.isEmpty(province.getTranslationName())) {
                    continue;
                }
                SeoInfMsg infMsg = buildMsg(1, locale, provinceId.toString(), province.getTranslationName(), null);
                sendMassage(infMsg);
            }
        }
    }

    private void cityPush(List<Long> cityIdList, List<String> localeList, Map<Long, Integer> cityOrderMap) throws Exception {
        for (String locale : localeList) {
            for (Long cityId : cityIdList) {
                City city = cityRepository.findOne(cityId, locale);
                if (city == null || StringUtils.isEmpty(city.getTranslationName())) {
                    continue;
                }
                SeoInfMsg infMsg = buildMsg(2, locale, cityId.toString(), city.getTranslationName(), cityOrderMap.get(cityId));
                sendMassage(infMsg);
            }
        }
    }

    private void airportPush(List<String> airportCodeList, List<String> localeList) throws Exception {
        for (String locale : localeList) {
            for (String airportCode : airportCodeList) {
                List<Airport> airports = osdBasicDataProxy.getAirportNoCityId(airportCode, locale);
                if (CollectionUtils.isEmpty(airports)) {
                    continue;
                }
                SeoInfMsg infMsg = buildMsg(5, locale, airportCode, airports.get(0).getAirportName(), null);
                sendMassage(infMsg);
            }
        }
    }

    private SeoInfMsg buildMsg(int type, String locale, String code, String name, Integer score) {
        SeoInfMsg infMsg = new SeoInfMsg();
        infMsg.setLocale(locale);
        infMsg.setChannel("carhire");
        infMsg.setType(type);
        infMsg.setInfCode(code);
        infMsg.setOriginName(name);
        infMsg.setScore(score);
        return infMsg;
    }

    private void sendMassage(SeoInfMsg msg) {
        try {
            String data = JsonUtil.toJSONString(msg);
            if (StringUtils.isNotEmpty(data)) {
                Message message = messageProducer.generateMessage("ibu.seo.plt.seo.basic.inf");
                message.setProperty("data", data);
                message.addTag("carhire");
                Map<String, String> tag = Maps.newHashMap();
                tag.put("locale", msg.getLocale());
                tag.put("code", msg.getInfCode());
                messageProducer.sendMessage(message, new MessageSendStateListener() {
                    @Override
                    public void onSuccess(Message message) {
                        log.info("sendMessage_seo_info", data, tag);
                    }

                    @Override
                    public void onFailed(Message message) {
                        log.error("sendMessage_seo_info", data, tag);
                    }
                });
            }
        } catch (Exception e) {
            log.error("sendMessage_seo_info", e);
        }
    }

    private void buildCache(int type, String code, List<String> localeList) {
        for (String locale : localeList) {
            try {
                String seoName = seoPlatformProxy.querySeoName(type, locale, code);
                if (StringUtils.isEmpty(seoName)) {
                    continue;
                }
                String key = String.format("car.market.seo.platform.data.cache.%s.%s.%s", type, locale, code).toLowerCase();
                RedisUtil.set(key, seoName, jobConfig.getSeoPlatformCacheExpired() * 24 * 3600);
                Thread.sleep(60);
            } catch (Exception e) {
                log.warn("buildCache", e);
            }
        }
    }
}
