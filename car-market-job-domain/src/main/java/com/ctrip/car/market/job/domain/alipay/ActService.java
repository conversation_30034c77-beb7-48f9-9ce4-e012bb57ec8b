package com.ctrip.car.market.job.domain.alipay;

import com.ctrip.car.market.job.domain.config.AlipayMessageConfig;
import com.ctrip.car.market.job.domain.constant.CommonConstant;
import com.ctrip.car.market.job.domain.dto.MessageData;
import com.ctrip.car.market.job.domain.dto.MessageInfo;
import com.ctrip.car.market.job.domain.enums.MessageTempEnum;
import com.ctrip.car.market.job.domain.utils.Metrics;
import com.ctrip.car.market.job.domain.utils.RedisUtil;
import com.ctrip.car.market.job.repository.entity.AlipaySubscribeTemplateinfo;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Component
public class ActService {

    private final ILog log = LogManager.getLogger(ActService.class);

    @Resource
    private AlipayService service;

    @Resource
    private AlipayMessageConfig alipayMessageConfig;

    @QSchedule("car.market.alipay.message.activity.send")
    public void task(Parameter parameter) {
        AlipaySubscribeTemplateinfo templateInfo = service.queryMessageTemplate(MessageTempEnum.Activity.getType());
        if (templateInfo == null || CollectionUtils.isEmpty(templateInfo.getParamaterList())) {
            log.error("ActService", "template is null");
            Metrics.build().withTag("type", String.valueOf(MessageTempEnum.Activity.getType())).recordOne("alipayMessageTemplateError");
            return;
        }
        if (CollectionUtils.isEmpty(templateInfo.getParamaterList()) || templateInfo.getParamaterList().size() < 4) {
            log.error("ActService", "parameter error");
            Metrics.build().withTag("type", String.valueOf(MessageTempEnum.Coupon.getType())).recordOne("alipayMessageTemplateError");
            return;
        }
        if (!service.isSendActivity()) {
            log.warn("ActService", "frequency limit");
            return;
        }
        List<String> uidList = service.querySubscribeUser(templateInfo.getTemplateId());
        List<List<String>> uidListPar = Lists.partition(uidList, alipayMessageConfig.getActTaskCount());
        CompletableFuture.allOf(uidListPar.stream().map(l -> CompletableFuture.runAsync(() -> this.send(l, templateInfo), CommonConstant.actExecutorService)).toArray(CompletableFuture[]::new)).join();
        RedisUtil.set(CommonConstant.ALIPAY_ACTIVITY_SEND, "1", 10 * 24 * 3600);
    }

    public void send(List<String> uidList, AlipaySubscribeTemplateinfo templateInfo) {
        for (String uid : uidList) {
            //判断是否满足发送频次
            if (service.queryRecord(templateInfo.getTemplateId(), uid) != null) {
                Map<String, String> tag = Maps.newHashMap();
                tag.put("uid", uid);
                log.warn("ActService", "recordAlreadyExists_" + uid, tag);
                Metrics.build().withTag("result","0").withTag("errorType","record").recordOne("alipayMessageAct");
                continue;
            }
            MessageInfo messageInfo = buildMessage(uid, templateInfo);
            service.sendMessage(messageInfo);
            Metrics.build().withTag("result","1").withTag("errorType","success").recordOne("alipayMessageAct");
        }
    }

    private MessageInfo buildMessage(String uid, AlipaySubscribeTemplateinfo templateInfo) {
        MessageInfo messageInfo = new MessageInfo();
        messageInfo.setData(new MessageData());
        messageInfo.setUid(uid);
        messageInfo.setTemplateId(templateInfo.getTemplateId());
        messageInfo.setPageUrl(templateInfo.getIsdUrl());
        messageInfo.getData().getKeyword1().setValue(templateInfo.getParamaterList().get(0));
        messageInfo.getData().getKeyword2().setValue(templateInfo.getParamaterList().get(1));
        messageInfo.getData().getKeyword3().setValue(templateInfo.getParamaterList().get(2));
        messageInfo.getData().getKeyword4().setValue(templateInfo.getParamaterList().get(3));
        return messageInfo;
    }
}
