package com.ctrip.car.market.job.domain.cache.seo;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotInformationDO;
import com.ctrip.car.market.job.domain.cache.BasicCacheAbstract;
import com.ctrip.car.market.job.domain.service.seo.QueryAllSeoHotInformationCountryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.SEO_INFORMATION_COUNTY_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.SeoInformationCountryCacheName;

@Component(SeoInformationCountryCache.Name)
@CreateCacheArea(area = "public")
public class SeoInformationCountryCache extends BasicCacheAbstract<Integer, List<SeoHotInformationDO>> {

    public static final String Name = SeoInformationCountryCacheName;

    @Resource
    private QueryAllSeoHotInformationCountryService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = SEO_INFORMATION_COUNTY_KEY, remotePreLoadName = Name)
    private Cache<Integer, List<SeoHotInformationDO>> cache;

    @Override
    public ConvertMessage<Integer, List<SeoHotInformationDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<Integer, List<SeoHotInformationDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, List<SeoHotInformationDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, List<SeoHotInformationDO>> getLoader() {
        return null;
    }
}
