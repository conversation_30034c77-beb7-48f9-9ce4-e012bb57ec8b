package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.UnionEnforcementconfigurationDO;
import com.ctrip.car.market.job.domain.mapper.UnionEnforcementConfigurationMapper;
import com.ctrip.car.market.job.repository.entity.UnionEnforcementconfiguration;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllUnionEnforcementConfigurationService implements CachePreLoader<Long, UnionEnforcementconfigurationDO>, CacheLoader<Long, UnionEnforcementconfigurationDO> {

    @Resource
    private RestrictedService service;

    @Resource
    private UnionEnforcementConfigurationMapper mapper;

    private Map<Long, UnionEnforcementconfigurationDO> getAllUnionEnforcementConfiguration() throws Exception {
        List<UnionEnforcementconfiguration> data = service.queryAllUnionEnforcementConfiguration();
        return data.stream().collect(Collectors.toMap(UnionEnforcementconfiguration::getEFID, mapper::to));
    }

    @Override
    public UnionEnforcementconfigurationDO load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, UnionEnforcementconfigurationDO> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllUnionEnforcementConfiguration();
    }

    @Override
    public Map<Long, UnionEnforcementconfigurationDO> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllUnionEnforcementConfiguration();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
