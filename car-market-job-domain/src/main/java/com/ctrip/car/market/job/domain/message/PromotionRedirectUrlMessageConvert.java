package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.CpnPromotionidredirecturlDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.PromotionidredirecturlMapper;
import com.ctrip.car.market.job.repository.entity.CpnPromotionidredirecturl;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

@Component
public class PromotionRedirectUrlMessageConvert extends AbstractConvertMessage<Long, CpnPromotionidredirecturl, CpnPromotionidredirecturlDO> implements ConvertMessage<Long, CpnPromotionidredirecturlDO> {

    @Resource
    private RestrictedService service;

    @Resource
    private PromotionidredirecturlMapper mapper;

    public PromotionRedirectUrlMessageConvert() {
        super(TabelEnum.PromotionRedirectUrl);
    }

    @Override
    public Map<Long, CpnPromotionidredirecturlDO> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Long getKey(String ids) {
        return Long.valueOf(ids);
    }

    @Override
    public CpnPromotionidredirecturl getData(Long id) throws Exception {
        return service.queryPromotionRedirectUrlByPromotionId(id);
    }

    @Override
    public CpnPromotionidredirecturlDO mapper(CpnPromotionidredirecturl value) {
        return mapper.to(value);
    }
}
