package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.CpnRestrictedPromotionDO;
import com.ctrip.car.market.job.repository.entity.CpnRestrictedPromotion;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RestrictedPromotionMapper {

    List<CpnRestrictedPromotionDO> to(List<CpnRestrictedPromotion> cpnRestrictedPromotions);

    CpnRestrictedPromotionDO to(CpnRestrictedPromotion cpnRestrictedPromotion);
}
