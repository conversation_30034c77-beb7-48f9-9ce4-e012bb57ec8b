package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.dto.GetDataParam;
import com.ctrip.car.market.job.domain.dto.GetHostDestinationInfo;
import com.ctrip.car.market.job.domain.proxy.TourAIOneServiceClientForGroup;
import com.ctrip.car.market.job.domain.service.SeoHotDestinationBusiness;
import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.repository.entity.SeoHotDestinatioinfo;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.tour.ai.one.service.GetDataRequestType;
import com.ctrip.tour.ai.one.service.GetDataResponseType;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.ctrip.car.market.job.domain.utils.GlobalVariable.BI_API_NAME;
import static com.ctrip.car.market.job.domain.utils.GlobalVariable.CAR_POPULAR_AIRPORT_TOKEN;

@Component
public class TripSeoAirportOrderSchedule {

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private SeoHotDestinationBusiness seoHotDestinationBusiness;

    @QSchedule("car.market.seo.airport.order.num.update")
    public void task(Parameter parameter) throws Exception {
        List<GetHostDestinationInfo> apiList = getHotDestinationData();
        List<SeoHotDestinatioinfo> dbPoiList = marketDBService.queryAllHotPoi();
        List<SeoHotDestinatioinfo> upadteList = Lists.newArrayList();
        for (SeoHotDestinatioinfo destinatioinfo : dbPoiList) {
            long orderNum = 0L;
            List<GetHostDestinationInfo> apiItem = apiList.stream().filter(l -> StringUtils.equalsIgnoreCase(l.getPickuplocationcode(), destinatioinfo.getPoiCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(apiItem)) {
                orderNum = apiItem.stream().map(GetHostDestinationInfo::getAdvanceorder_cnt).filter(Objects::nonNull).max(Long::compareTo).orElse(0L);
            }
            if (orderNum > 0 && destinatioinfo.getOrderNum() != orderNum) {
                destinatioinfo.setOrderNum(Long.valueOf(orderNum).intValue());
                upadteList.add(destinatioinfo);
            }
        }
        if (CollectionUtils.isNotEmpty(upadteList)) {
            seoHotDestinationBusiness.batchUpdateDestination(upadteList);
        }
    }

    /**
     * bi 获取热门目的地数据
     */
    private List<GetHostDestinationInfo> getHotDestinationData() throws Exception {
        long offset = 0;
        long row = 500;
        List<GetHostDestinationInfo> result = new ArrayList<>();
        List<GetHostDestinationInfo> temp;
        do {
            // 构建请求参数
            GetDataRequestType soaReq = getGetDataRequestType(row, offset, CAR_POPULAR_AIRPORT_TOKEN);
            GetDataResponseType soaRes = TourAIOneServiceClientForGroup.getClient().getData(soaReq);
            CLogUtil.info("HotDestinationGetData", soaReq, soaRes);
            CLogUtil.esLog("HotDestinationGetData", null, soaReq, soaRes);
            if (StringUtils.isEmpty(soaRes.getBacks())) {
                break;
            }
            temp = JsonUtil.parseArray(soaRes.getBacks(), GetHostDestinationInfo.class);
            result.addAll(temp);
            Optional<Long> max = temp.stream().map(GetHostDestinationInfo::getMysql_id).max(Comparator.comparingLong(o -> o));
            if (max.isPresent()) {
                offset = max.get();
            }
        } while (org.apache.commons.collections.CollectionUtils.isNotEmpty(temp));
        return result;
    }

    /**
     * 构建 bi 请求参数
     */
    private GetDataRequestType getGetDataRequestType(long row, long offset, String token) {
        GetDataParam params = new GetDataParam();
        params.setRows(row);
        params.setOffset(offset);
        GetDataRequestType soaReq = new GetDataRequestType();
        soaReq.setApiName(BI_API_NAME);
        soaReq.setToken(token);
        soaReq.setParams(JsonUtil.toJSONString(params));
        return soaReq;
    }
}
