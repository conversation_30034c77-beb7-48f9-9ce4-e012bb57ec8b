package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.basebiz.geolocation.service.*;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.gs.dest.content.contract.DestContentInfoType;
import com.ctrip.gs.dest.content.contract.DestinationContentServiceClient;
import com.ctrip.gs.dest.content.contract.GetDestContentListRequestType;
import com.ctrip.gs.dest.content.contract.GetDestContentListResponseType;
import com.ctrip.gs.dest.content.contract.common.RequestHeadType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Component
public class DestProxy {

    private final ILog logger = LogManager.getLogger(DestProxy.class);

    private DestinationContentServiceClient destinationContentServiceClient = DestinationContentServiceClient.getInstance();

    private GeoLocationServiceClient geoLocationServiceClient = GeoLocationServiceClient.getInstance();

    public static final int BATCH_SIZE_CITY_ID = 50;

    public Map<Long, String> getCityImage(List<Long> cityIdList) {
        //酒店city转攻略districtId
        Map<Long, Long> cityMap = Maps.newHashMap();
        Map<Long, Long> temp;
        int offset = 0, size = cityIdList.size();
        while (offset < size) {
            temp = getCityMap(cityIdList.stream().skip(offset).limit(Math.min(offset + BATCH_SIZE_CITY_ID, size) - offset).collect(Collectors.toList()));
            if (org.apache.commons.collections.CollectionUtils.isEmpty(temp.values())) {
                break;
            }
            cityMap.putAll(temp);
            offset += BATCH_SIZE_CITY_ID;
        }

        if (cityMap.isEmpty()) {
            return Maps.newHashMap();
        }
        GetDestContentListRequestType requestType = new GetDestContentListRequestType();
        requestType.setHead(new RequestHeadType());
        requestType.getHead().setPlatform("100000");
        requestType.getHead().setSource("100043032_tripPPC");
        requestType.getHead().setLocale("en-US");
        requestType.setDistrictIdList(Lists.newArrayList(cityMap.values()));
        try {
            GetDestContentListResponseType responseType = destinationContentServiceClient.getDestContentList(requestType);
            if (responseType == null || CollectionUtils.isEmpty(responseType.getDestContentInfoList())) {
                return Maps.newHashMap();
            }
            Map<Long, String> districtMap = Maps.newHashMap();
            for (DestContentInfoType destItem : responseType.getDestContentInfoList()) {
                if (destItem.getDistrict() == null) {
                    continue;
                }
                districtMap.put(destItem.getDistrict().getId(), destItem.getDistrict().getImageUrl());
            }
            //转换成cityId-url返回
            Map<Long, String> result = Maps.newHashMap();
            for (Long cityId : cityIdList) {
                if (cityMap.containsKey(cityId)) {
                    String url = districtMap.get(cityMap.get(cityId));
                    if (StringUtils.isNotEmpty(url)) {
                        result.put(cityId, url);
                    }
                }
            }
            return result;
        } catch (Exception e) {
            logger.warn("getCityImage", e);
            return Maps.newHashMap();
        }
    }

    private Map<Long, Long> getCityMap(List<Long> cityIdList) {
        if (CollectionUtils.isEmpty(cityIdList)) {
            return Maps.newHashMap();
        }
        MapsRequestType requestType = new MapsRequestType();
        requestType.setKeys(cityIdList.stream().distinct().map(l -> {
            LocationKey item = new LocationKey();
            item.setType("base");
            item.setGeocategoryid(3);
            item.setGlobalid(l);
            return item;
        }).collect(Collectors.toList()));
        try {
            MapsResponseType responseType = geoLocationServiceClient.maps(requestType);
            if (responseType == null || CollectionUtils.isEmpty(responseType.getLocationMaps())) {
                return Maps.newHashMap();
            }
            Map<Long, Long> result = Maps.newHashMap();
            for (LocationMap location : responseType.getLocationMaps()) {
                if (CollectionUtils.isEmpty(location.getLocations()) || location.getKey() == null) {
                    continue;
                }
                Location loc = location.getLocations().stream().filter(l -> StringUtils.equalsIgnoreCase(l.getType(), "gs_district")).findFirst().orElse(null);
                if (loc == null) {
                    continue;
                }
                result.put(location.getKey().getGlobalid(), loc.getGlobalid());
            }
            return result;
        } catch (Exception e) {
            logger.warn("getCityMap", e);
            return Maps.newHashMap();
        }
    }
}
