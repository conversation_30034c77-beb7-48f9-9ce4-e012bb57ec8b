package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.service.TripSeoProductService;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.Resource;

@Component
public class SynTripSeoProductSchedule {

    @Resource
    private TripSeoProductService tripSeoProductService;

    @QSchedule("car.market.trip.seo.synTripSeoProduct.job.hot")
    public void hot() throws Exception {
        tripSeoProductService.updateProduct(1);
    }

    @QSchedule("car.market.trip.seo.synTripSeoProduct.job.other")
    public void other() throws Exception {
        tripSeoProductService.updateProduct(2);
    }

    @QSchedule("car.market.trip.seo.synTripSeoProduct.job.priority.zero")
    public void zero() throws Exception {
        tripSeoProductService.updateProduct(0);
    }
}
