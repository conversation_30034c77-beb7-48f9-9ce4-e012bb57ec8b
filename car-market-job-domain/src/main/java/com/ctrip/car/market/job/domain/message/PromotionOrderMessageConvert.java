package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.CpnPromotionOrderDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.PromotionOrderMapper;
import com.ctrip.car.market.job.repository.entity.CpnPromotionOrder;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@Component
public class PromotionOrderMessageConvert extends AbstractConvertMessage<Long, CpnPromotionOrder, CpnPromotionOrderDO> implements ConvertMessage<Long, CpnPromotionOrderDO> {

    @Resource
    private RestrictedService service;

    @Resource
    private PromotionOrderMapper mapper;

    public PromotionOrderMessageConvert() {
        super(TabelEnum.PromotionOrder);
    }

    @Override
    public Map<Long, CpnPromotionOrderDO> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Long getKey(String ids) {
        return Long.valueOf(ids);
    }

    @Override
    public CpnPromotionOrder getData(Long id) throws Exception {
        CpnPromotionOrder obj = service.queryPromotionOrder(id);
        long ts = System.currentTimeMillis();
        if (obj == null || !Objects.equals(obj.getStatus(), 1) || !(obj.getEndTime().getTime() >= ts && obj.getStartTime().getTime() <= ts)) {
            return null;
        }
        return obj;
    }

    @Override
    public CpnPromotionOrderDO mapper(CpnPromotionOrder value) {
        return mapper.to(value);
    }
}
