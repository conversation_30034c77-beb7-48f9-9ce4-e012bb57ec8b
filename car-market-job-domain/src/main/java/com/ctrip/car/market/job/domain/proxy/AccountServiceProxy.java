package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.basebiz.account.service.soa.AccountInfoResponseType;
import com.ctrip.basebiz.account.service.soa.AccountServiceClient;
import com.ctrip.basebiz.account.service.soa.ExpectedInfo;
import com.ctrip.basebiz.account.service.soa.GetAccountByMobilePhoneRequestType;
import com.ctrip.car.customer.common.service.BaseService;
import com.ctrip.car.market.job.common.CommonResult;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;

@Component
public class AccountServiceProxy extends BaseService {
    private static final ILog log = LogManager.getLogger(AccountServiceProxy.class);
    AccountServiceClient accountServiceClient = AccountServiceClient.getInstance();

    public CommonResult<String> getUidByMobile(String mobilePhone) {
        GetAccountByMobilePhoneRequestType requestType = new GetAccountByMobilePhoneRequestType();
        requestType.setSubSystemId(90001);
        requestType.setCountryCode("86");
        requestType.setPhoneNumber(mobilePhone);
        requestType.setExpect(Collections.singletonList(ExpectedInfo.ACCOUNT_META_INFO));

        AccountInfoResponseType responseType = null;
        try {
            responseType = accountServiceClient.getAccountByMobilePhone(requestType);
        } catch (Exception e) {
            log.warn("AccountServiceClient::getAccountByMobilePhone failed", e);
            return CommonResult.failed("RPCException", "AccountServiceClient::getAccountByMobilePhone failed");
        } finally {
            log.info("AccountServiceClient::getAccountByMobilePhone", String.format("requestType=%s\nresponseType=%s", JsonUtil.toJSONString(requestType), JsonUtil.toJSONString(responseType)));
        }

        if (responseType.getReturnCode() == 0 && StringUtils.isNotBlank(responseType.getUid())) {
            return CommonResult.success(responseType.getUid());
        }
        return CommonResult.failed(String.valueOf(responseType.getReturnCode()), responseType.getMessage());
    }

}