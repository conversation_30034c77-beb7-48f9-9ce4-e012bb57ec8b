package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.market.lat.open.soa.ImpInfo;
import com.ctrip.market.lat.open.soa.QueryImpInfoRequestType;
import com.ctrip.market.lat.open.soa.QueryImpInfoResponseType;
import com.ctrip.market.lat.open.soa.TripOpenServiceClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;

@Component
public class LatTripOpenServiceProxy {

    private static final ILog log = LogManager.getLogger("LatTripOpenServiceProxy");

    private final TripOpenServiceClient client = TripOpenServiceClient.getInstance();

    public ImpInfo queryImpInfoBy(String impId) {
        if (StringUtils.isBlank(impId)) return null;
        QueryImpInfoRequestType queryImpInfoRequestType = new QueryImpInfoRequestType();
        queryImpInfoRequestType.setImpids(Collections.singletonList(impId));
        try {
            QueryImpInfoResponseType queryImpInfoResponseType = client.queryImpInfo(queryImpInfoRequestType);
            if (queryImpInfoResponseType != null && queryImpInfoResponseType.getCode() == 0) {
                return queryImpInfoResponseType.getResults().get(0);
            }
        } catch (Exception e) {
            log.error("LatTripOpenServiceProxy.queryImpInfoBy", e);
        }
        return null;
    }
}
