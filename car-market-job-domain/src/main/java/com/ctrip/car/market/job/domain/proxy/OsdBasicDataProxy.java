package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.car.market.common.cache.annotations.NCache;
import com.ctrip.car.market.crossrecommend.service.contract.dto.SeoZone;
import com.ctrip.car.osd.basicdataservice.dto.*;
import com.ctrip.car.osd.basicdataservice.methodtype.CarosdbasicdataserviceClient;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctriposs.baiji.rpc.common.types.BaseRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class OsdBasicDataProxy {
    private final ILog log = LogManager.getLogger(OsdBasicDataProxy.class);
    CarosdbasicdataserviceClient client = CarosdbasicdataserviceClient.getInstance();

    @NCache(holdMinute = 60 * 36, log = true, reHoldMinute = 30, ignoreNull = false)
    public SeoZone getZone(Integer cityId, String locale) {
        List<SeoZone> seoZones = getAirport(cityId, null, locale);
        if (CollectionUtils.isNotEmpty(seoZones)) {
            return seoZones.get(0);
        } else {
            seoZones = getZones(cityId, 2, locale);
        }
        if (CollectionUtils.isNotEmpty(seoZones)) {
            return seoZones.get(0);
        }
        return null;
    }

    @NCache(holdMinute = 60, log = true, reHoldMinute = 30, ignoreNull = false)
    public List<SeoZone> getZones(Integer cityId, Integer type, String locale) {
        GetZonesRequestType requestType = new GetZonesRequestType();
        requestType.setTypeId(type);
        requestType.setCityId(cityId);
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setLocale(locale);
        requestType.setBaseRequest(baseRequest);
        requestType.setDataSource(DataSource.OCH);
        try {
            GetZonesResponseType zones = client.getZones(requestType);
            if (CollectionUtils.isNotEmpty(zones.getZones())) {
                return zones.getZones().stream().map(e -> {
                    SeoZone seoZone = new SeoZone();
                    seoZone.setZoneName(e.getZoneName());
                    seoZone.setZoneEName(e.getZoneEName());
                    seoZone.setLatitude(e.getLatitude());
                    seoZone.setLongitude(e.getLongitude());
                    seoZone.setType(Integer.valueOf(e.getZoneType()));
                    seoZone.setCityId(e.getCityId());
                    return seoZone;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.info("getZones", e.toString());
        }
        return null;
    }

    @NCache(holdMinute = 60, log = true, reHoldMinute = 30, ignoreNull = false)
    public List<SeoZone> getAirport(Integer cityId, String airportCode, String locale) {
        GetAirportsRequestType requestType = new GetAirportsRequestType();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setLocale(locale);
        requestType.setBaseRequest(baseRequest);
        requestType.setCityId(cityId);
        requestType.setAirportCode(airportCode);
        requestType.setDataSource(DataSource.OCH);
        try {
            GetAirportsResponseType responseType = client.getAirports(requestType);
            if (CollectionUtils.isNotEmpty(responseType.getAirports())) {
                return responseType.getAirports().stream().map(e -> {
                    SeoZone seoZone = new SeoZone();
                    seoZone.setZoneName(e.getAirportName());
                    seoZone.setLatitude(e.getLatitude());
                    seoZone.setLongitude(e.getLongitude());
                    seoZone.setAirportCode(e.getAirportCode());
                    seoZone.setType(1);
                    seoZone.setZoneEName(e.getAirportEName());
                    seoZone.setCityId(e.getCityId());
                    return seoZone;

                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.info("getAirport", e.toString());
        }
        return null;
    }

    public List<Airport> getAirportNoCityId(String airportCode, String locale) {
        GetAirportsRequestType requestType = new GetAirportsRequestType();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setLocale(locale);
        requestType.setBaseRequest(baseRequest);
        requestType.setAirportCode(airportCode);
        requestType.setDataSource(DataSource.OCH);
        try {
            GetAirportsResponseType responseType = client.getAirports(requestType);
            return responseType.getAirports();
        } catch (Exception e) {
            log.info("getAirport", e.toString());
        }
        return null;
    }

    public Airport getCityAirport(Integer cityId, String locale) {
        GetAirportsRequestType requestType = new GetAirportsRequestType();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setLocale(locale);
        requestType.setBaseRequest(baseRequest);
        requestType.setCityId(cityId);
        requestType.setDataSource(DataSource.OCH);
        try {
            GetAirportsResponseType responseType = client.getAirports(requestType);
            if (responseType == null || CollectionUtils.isEmpty(responseType.getAirports())) {
                return null;
            }
            return responseType.getAirports().stream().filter(l -> StringUtils.isNotEmpty(l.getAirportCode()) && Objects.equals(l.getCityId(), cityId)).findFirst().orElse(null);
        } catch (Exception e) {
            log.warn("getCityAirport", e.toString());
        }
        return null;
    }

    public Airport getAirportByCode(String code, String locale) {
        GetAirportsRequestType requestType = new GetAirportsRequestType();
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setLocale(locale);
        requestType.setBaseRequest(baseRequest);
        requestType.setAirportCode(code);
        requestType.setDataSource(DataSource.OCH);
        try {
            GetAirportsResponseType responseType = client.getAirports(requestType);
            if (CollectionUtils.isNotEmpty(responseType.getAirports())) {
                return responseType.getAirports().stream().filter(l -> StringUtils.isNotEmpty(l.getAirportCode())).findFirst().orElse(null);
            }
        } catch (Exception e) {
            log.info("getAirport", e.toString());
        }
        return null;
    }
}
