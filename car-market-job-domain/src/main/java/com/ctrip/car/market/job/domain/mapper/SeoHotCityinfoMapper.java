package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.seo.SeoHotCityinfoDO;
import com.ctrip.car.market.job.repository.entity.SeoHotCityinfo;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SeoHotCityinfoMapper {

    SeoHotCityinfoDO to(SeoHotCityinfo value);

    List<SeoHotCityinfoDO> to(List<SeoHotCityinfo> value);
}
