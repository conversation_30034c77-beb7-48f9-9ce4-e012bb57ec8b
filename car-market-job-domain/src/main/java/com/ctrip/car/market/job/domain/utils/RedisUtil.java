package com.ctrip.car.market.job.domain.utils;

import credis.java.client.CacheProvider;
import credis.java.client.util.CacheFactory;

import java.util.HashMap;
import java.util.Map;

public class RedisUtil {

    private static final CacheProvider seoProvider = CacheFactory.getProvider("car_market_cache");

    public static String getCache(String key) {
        return seoProvider.get(key);
    }

    public static void setCache(String key, String value, long second) {
        seoProvider.set(key, value);
        seoProvider.expire(key, second);
    }

    public static boolean setNx(String key, long seconds, String value) throws Exception {
        try {
            boolean result = seoProvider.setnx(key, value);
            seoProvider.expire(key, seconds);
            return result;
        } catch (Exception e) {
            return true;
        }
    }

    public static boolean setByte(String key, byte[] bytes, long seconds) {
        try {
            seoProvider.setbyte(key, bytes);
            seoProvider.expire(key, seconds);
        } catch (Exception e) {
            return false;
        }
        return true;
    }


    public static byte[] getByte(String key) {
        try {
            return seoProvider.getbyte(key);
        } catch (Exception e) {
            return null;
        }
    }

    public static void set(String key, String value, int seconds) {
        seoProvider.setex(key, seconds, value);
    }

    public static String get(String key) {
        return seoProvider.get(key);
    }

    public static Boolean del(String key) {
        return seoProvider.del(key);
    }
}
