package com.ctrip.car.market.job.domain.dto;

public class AccessTokenData {

    private Account account;
    private String access_token;
    private String refresh_token;
    private Long access_token_expires_in;
    private Long refresh_token_expires_in;

    public Account getAccount() {
        return account;
    }

    public void setAccount(Account account) {
        this.account = account;
    }

    public String getAccess_token() {
        return access_token;
    }

    public void setAccess_token(String access_token) {
        this.access_token = access_token;
    }

    public String getRefresh_token() {
        return refresh_token;
    }

    public void setRefresh_token(String refresh_token) {
        this.refresh_token = refresh_token;
    }

    public Long getAccess_token_expires_in() {
        return access_token_expires_in;
    }

    public void setAccess_token_expires_in(Long access_token_expires_in) {
        this.access_token_expires_in = access_token_expires_in;
    }

    public Long getRefresh_token_expires_in() {
        return refresh_token_expires_in;
    }

    public void setRefresh_token_expires_in(Long refresh_token_expires_in) {
        this.refresh_token_expires_in = refresh_token_expires_in;
    }
}
