package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnRestrictedConditionDO;
import com.ctrip.car.market.job.domain.message.RestrictedCondition_PromotionMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllRestrictedCondition_PromotionService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.RESTRICTED_CONDITION_PROMOTION_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.RestrictedCondition_PromotionCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(RestrictedCondition_PromotionCache.Name)
@CreateCacheArea(area = "public")
public class RestrictedCondition_PromotionCache extends BasicCacheAbstract<Integer, List<CpnRestrictedConditionDO>> {

    public static final String Name = RestrictedCondition_PromotionCacheName;

    @Resource
    private RestrictedCondition_PromotionMessageConvert convert;

    @Resource
    private QueryAllRestrictedCondition_PromotionService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = RESTRICTED_CONDITION_PROMOTION_HASH_KEY, remotePreLoadName = Name)
    private Cache<Integer, List<CpnRestrictedConditionDO>> cache;


    @Override
    public ConvertMessage<Integer, List<CpnRestrictedConditionDO>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Integer, List<CpnRestrictedConditionDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Integer, List<CpnRestrictedConditionDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Integer, List<CpnRestrictedConditionDO>> getLoader() {
        return null;
    }
}
