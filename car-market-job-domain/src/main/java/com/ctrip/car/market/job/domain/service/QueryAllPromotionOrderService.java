package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnPromotionOrderDO;
import com.ctrip.car.market.job.domain.mapper.PromotionOrderMapper;
import com.ctrip.car.market.job.repository.entity.CpnPromotionOrder;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllPromotionOrderService implements CachePreLoader<Long, CpnPromotionOrderDO>, CacheLoader<Long, CpnPromotionOrderDO> {

    @Resource
    private RestrictedService service;

    @Resource
    private PromotionOrderMapper mapper;

    private Map<Long, CpnPromotionOrderDO> getAllPromotionOrder() throws Exception {
        List<CpnPromotionOrder> data = service.queryAllPromotionOrder();
        if (CollectionUtils.isEmpty(data)) {
            Map<Long, CpnPromotionOrderDO> empty = Maps.newHashMap();
            empty.put(0L, null);
            return empty;
        }
        return data.stream().collect(Collectors.toMap(CpnPromotionOrder::getId, mapper::to));
    }

    @Override
    public CpnPromotionOrderDO load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, CpnPromotionOrderDO> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllPromotionOrder();
    }

    @Override
    public Map<Long, CpnPromotionOrderDO> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllPromotionOrder();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
