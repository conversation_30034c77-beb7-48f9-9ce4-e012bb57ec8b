package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.maiar.mq.MaiarMessage;
import com.ctrip.car.market.job.domain.constant.SeoConstant;
import com.ctrip.car.market.job.domain.dto.*;
import com.ctrip.car.market.job.domain.enums.SeoHotDestinationEnum;
import com.ctrip.car.market.job.domain.enums.SeoHotStatusEnums;
import com.ctrip.car.market.job.domain.proxy.OsdBasicDataProxy;
import com.ctrip.car.market.job.domain.proxy.TourAIOneServiceClientForGroup;
import com.ctrip.car.market.job.domain.service.SeoHotDestinationBusiness;
import com.ctrip.car.market.job.domain.utils.CLogUtil;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.domain.utils.QConfigUtil;
import com.ctrip.car.market.job.repository.entity.*;
import com.ctrip.car.osd.basicdataservice.dto.Airport;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.repository.CountryRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.geo.domain.value.Country;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.ibu.seo.platform.admin.contract.Product;
import com.ctrip.tour.ai.one.service.GetDataRequestType;
import com.ctrip.tour.ai.one.service.GetDataResponseType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageProducer;
import qunar.tc.qmq.MessageSendStateListener;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ctrip.car.market.job.domain.utils.GlobalVariable.*;

/**
 * <AUTHOR>
 */
@Component
public class SeoHotDestinationSchedule {

    private final ILog log = LogManager.getLogger(SeoHotDestinationSchedule.class);

    @Resource
    private MessageProducer messageProducer;

    @Resource
    private SeoHotDestinationBusiness seoHotDestinationBusiness;

    @Resource
    private CountryRepository countryRepository;

    @Resource
    private CityRepository cityRepository;

    @Resource
    private OsdBasicDataProxy osdBasicDataProxy;


    /**
     * 从bi拉取热门目的地数据代码
     */
    @QSchedule("car.market.geo.pull.hot.destination.sync")
    public void pullFromBi() {
        try {
            List<GetHostDestinationInfo> getHostDestinationInfoList = getHotDestinationData();
            saveDestination(getHostDestinationInfoList);
        } catch (Exception e) {
            log.error("Trip.SEO.PULL.JOB pull host destination info error...", e);
        }
    }

    @QSchedule("car.market.seo.pull.hot.information.sync")
    public void pullInformation() {
        try {
            List<GetCarSeoRentalInfo> getCarSeoRentalInfoList = getCarSeoRental();
            saveCarSeoRentalInfo(getCarSeoRentalInfoList);
        } catch (Exception e) {
            log.error("Trip.SEO.PULL.JOB pull host destination info error...", e);
        }
    }

    /**
     * ibu推送热门目的地数据
     */
    @QSchedule("car.market.geo.push.hot.destination.sync")
    public void pushToIbu() {
        if ("true".equalsIgnoreCase(QConfigUtil.getSeoHotConfigOrDefault("seo.city.push.switch", "true"))) {
            pushCityToIbu();
        }
        if ("true".equalsIgnoreCase(QConfigUtil.getSeoHotConfigOrDefault("seo.country.push.switch", "false"))) {
            pushCountryToIbu();
        }
        if ("true".equalsIgnoreCase(QConfigUtil.getSeoHotConfigOrDefault("seo.airport.push.switch", "false"))) {
            pushDestinationToIbu();
        }
        if ("true".equalsIgnoreCase(QConfigUtil.getSeoHotConfigOrDefault("seo.vendor.push.switch", "false"))) {
            pushVendorToIbu();
        }
        if ("true".equalsIgnoreCase(QConfigUtil.getSeoHotConfigOrDefault("seo.vendor.city.push.switch", "false"))) {
            pushVendorCityToIbu();
        }
    }

    /**
     * 推送热门供应商数据到ibu
     */
    public void pushVendorToIbu() {
        List<SeoHotVendor> seoHotVendorArrayList = new ArrayList<>();
        try {
            seoHotVendorArrayList = seoHotDestinationBusiness.getSeoHotVendorInfoList(Boolean.TRUE);
        } catch (Exception e) {
            log.error("Trip.SEO.PUSH.JOB get hot vendor from db error...", e);
        }
        if (CollectionUtils.isNotEmpty(seoHotVendorArrayList)) {
            seoHotVendorArrayList.forEach(x -> {
                MessageParams params = MessageParams.builder().url(x.getUrl()).seoHotDestinationEnum(SeoHotDestinationEnum.SUPPLIER).vendorName(x.getVendorName()).vendorCode(x.getVendorId()).build();
                List<HotDestinationMassageDTO> hotDestinationMassageDTO = seoHotDestinationBusiness.buildHotDestinationMassageDTO(params);
                hotDestinationMassageDTO.forEach(this::sendMassage);
            });
        }
    }

    /**
     * 推送热门供应商城市数据到ibu
     */
    public void pushVendorCityToIbu() {
        List<SeoHotVendorCity> seoHotVendorCityList = new ArrayList<>();
        try {
            seoHotVendorCityList = seoHotDestinationBusiness.getSeoHotVendorCityInfoList(Boolean.TRUE);
        } catch (Exception e) {
            log.error("Trip.SEO.PUSH.JOB get hot vendor city from db error...", e);
        }
        Map<Long, String> provinceIdMap = new HashMap<>();
        // 2、获得多语言cityName
        Map<String, Map<String, String>> nameMap = getCityNameMap(null, provinceIdMap, seoHotVendorCityList);
        if (CollectionUtils.isNotEmpty(seoHotVendorCityList)) {
            seoHotVendorCityList.forEach(x -> {
                MessageParams params = MessageParams.builder()
                        .url(x.getUrl())
                        .seoHotDestinationEnum(SeoHotDestinationEnum.SUPPLIER_CITY)
                        .vendorCode(x.getVendorId())
                        .vendorName(x.getVendorName())
                        .vendorCityName(x.getCityName())
                        .vendorCityId(x.getCityId())
                        .nameMap(nameMap)
                        .build();
                List<HotDestinationMassageDTO> hotDestinationMassageDTO = seoHotDestinationBusiness.buildHotDestinationMassageDTO(params);
                hotDestinationMassageDTO.forEach(this::sendMassage);
            });
        }
    }


    public void pushCityToIbu() {
        List<SeoHotCityinfo> seoHotCityinfoList = new ArrayList<>();
        try {
            seoHotCityinfoList = seoHotDestinationBusiness.getSeoHotCityinfoList(Boolean.TRUE);
        } catch (Exception e) {
            log.error("Trip.SEO.PUSH.JOB get hot city from db error...", e);
        }
        Map<Long, String> provinceIdMap = new HashMap<>();
        // 2、获得多语言cityName
        Map<String, Map<String, String>> nameMap = getCityNameMap(seoHotCityinfoList, provinceIdMap, null);
        if (CollectionUtils.isNotEmpty(seoHotCityinfoList)) {
            seoHotCityinfoList.forEach(x -> {
                MessageParams params = MessageParams.builder().url(x.getUrl()).seoHotDestinationEnum(SeoHotDestinationEnum.CITY).countryId(x.getCountryId()).cityId(x.getCityId()).nameMap(nameMap).provinceId(provinceIdMap.get(x.getCityId().longValue())).build();
                List<HotDestinationMassageDTO> hotDestinationMassageDTO = seoHotDestinationBusiness.buildHotDestinationMassageDTO(params);
                hotDestinationMassageDTO.forEach(this::sendMassage);
            });
        }
    }

    public void pushCountryToIbu(){
        List<SeoHotCountryinfo> seoHotCountryinfoList = new ArrayList<>();
        try {
            // 1、db拉取国家信息
            seoHotCountryinfoList = seoHotDestinationBusiness.getSeoHotCountryinfoList(Boolean.TRUE);
        } catch (Exception e) {
            log.error("Trip.SEO.PUSH.JOB get hot country from db error...", e);
        }
        // 2、获得多语言countryName
        Map<String, Map<String, String>> nameMap = getCountryNameMap(seoHotCountryinfoList);
        if (CollectionUtils.isNotEmpty(seoHotCountryinfoList)) {
            seoHotCountryinfoList.forEach(x -> {
                MessageParams params = MessageParams.builder().url(x.getUrl()).seoHotDestinationEnum(SeoHotDestinationEnum.COUNTRY).countryId(x.getCountryId()).nameMap(nameMap).build();
                List<HotDestinationMassageDTO> hotDestinationMassageDTO = seoHotDestinationBusiness.buildHotDestinationMassageDTO(params);
                hotDestinationMassageDTO.forEach(this::sendMassage);
            });
        }
    }

    public void pushDestinationToIbu(){
        List<SeoHotDestinatioinfo> seoHotDestinatioinfoList = new ArrayList<>();
        try {
            // 1、db拉取热门机场信息
            seoHotDestinatioinfoList = seoHotDestinationBusiness.getSeoHotDestinationinfoList(Boolean.TRUE);
        } catch (Exception e) {
            log.error("Trip.SEO.PUSH.JOB get hot Destination from db error...", e);
        }
        // 2、获得多语言airportName
        Map<Long, City> cityMap = batchQueryCityInfo(seoHotDestinatioinfoList.stream().map(x -> x.getCityId().longValue()).collect(Collectors.toList()), "en-ID");
        Map<Long, String> provinceIdMap = cityMap.values().stream().collect(Collectors.toMap(City::getId, x -> x.getProvinceId().toString()));
        Map<String, Map<String, String>> nameMap = getAirportNameMap(seoHotDestinatioinfoList);
        if (CollectionUtils.isNotEmpty(seoHotDestinatioinfoList)) {
            seoHotDestinatioinfoList.forEach(x -> {
                MessageParams params = MessageParams.builder().url(x.getUrl()).seoHotDestinationEnum(SeoHotDestinationEnum.AIRPORT).countryId(x.getCountryId()).cityId(x.getCityId()).nameMap(nameMap).provinceId(provinceIdMap.get(x.getCityId().longValue())).poiId(x.getPoiId()).poiCode(x.getPoiCode()).build();
                List<HotDestinationMassageDTO> hotDestinationMassageDTO = seoHotDestinationBusiness.buildHotDestinationMassageDTO(params);
                hotDestinationMassageDTO.forEach(this::sendMassage);
            });
        }
    }

    private Map<String, Map<String, String>> getCityNameMap(List<SeoHotCityinfo> seoHotCityinfoList, Map<Long, String> provinceIdMap, List<SeoHotVendorCity> seoHotVendorCityList) {
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        List<Long> cityList = CollectionUtils.isEmpty(seoHotCityinfoList) ? seoHotVendorCityList.stream().map(x -> Long.valueOf(x.getCityId())).collect(Collectors.toList()) :
                seoHotCityinfoList.stream().map(x -> Long.valueOf(x.getCityId())).collect(Collectors.toList());
        if (StringUtils.isEmpty(LOCALE)) {
            return nameMap;
        }
        String[] locales = LOCALE.split(",");
        for (String locale : locales) {
            // 获取各个语种对应的城市名称
            try{
                Map<Long, City> map = batchQueryCityInfo(cityList, locale);
                if (!map.isEmpty()) {
                    Map<String, String> collect = map.values().stream().collect(Collectors.toMap(x -> x.getId().toString(), City::getTranslationName));
                    nameMap.put(locale, collect);
                    // 省份信息
                    if (provinceIdMap.isEmpty()) {
                        provinceIdMap.putAll(map.values().stream().collect(Collectors.toMap(City::getId, x -> x.getProvinceId().toString())));
                    }
                }
            }catch (Exception e) {
                log.error("Trip.seo.push.job getCityNameMap error...", e);
            }
        }
        return nameMap;
    }

    private Map<String, Map<String, String>> getCountryNameMap(List<SeoHotCountryinfo> seoHotCountryinfoList) {
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        List<Long> countryId = seoHotCountryinfoList.stream().map(x -> Long.valueOf(x.getCountryId())).collect(Collectors.toList());
        if (StringUtils.isEmpty(LOCALE)) {
            return nameMap;
        }
        String[] locales = LOCALE.split(",");
        for (String locale : locales) {
            // 获取各个语种对应的国家名称
            try{
                Map<Long, Country> map = batchQueryCountryInfo(countryId, locale);
                if (!map.isEmpty()) {
                    Map<String, String> collect = map.values().stream().collect(Collectors.toMap(x -> x.getId().toString(), Country::getTranslationName));
                    nameMap.put(locale, collect);
                }
            }catch (Exception e) {
                log.error("Trip.seo.push.job getCountryNameMap error...", e);
            }
        }
        return nameMap;
    }

    private Map<String, Map<String, String>> getAirportNameMap(List<SeoHotDestinatioinfo> seoHotDestinatioinfoList) {
        Map<String, Map<String, String>> nameMap = new HashMap<>();
        List<String> airportCode = seoHotDestinatioinfoList.stream().map(SeoHotDestinatioinfo::getPoiCode).collect(Collectors.toList());
        if (StringUtils.isEmpty(LOCALE)) {
            return nameMap;
        }
        String[] locales = LOCALE.split(",");
        for (String locale : locales) {
            // 获取各个语种对应的机场名称
            try{
                Map<String, String> map = new HashMap<>();
                for(String code : airportCode) {
                    List<Airport> airportNoCityId = osdBasicDataProxy.getAirportNoCityId(code, locale);
                    if (CollectionUtils.isNotEmpty(airportNoCityId)) {
                        map.put(code, airportNoCityId.get(0).getAirportName());
                    }
                }
                nameMap.put(locale, map);
                Thread.sleep(5000);
            }catch (Exception e) {
                log.error("Trip.seo.push.job getCountryNameMap error...", e);
            }
        }
        return nameMap;
    }


    /**
     * qmq 推送热门消息到 ibu
     */
    private void sendMassage(HotDestinationMassageDTO hotDestinationMassageDTO) {
        try {
            String data = JsonUtil.toJSONString(hotDestinationMassageDTO);
            if (StringUtils.isNotEmpty(data)) {
                Message message = messageProducer.generateMessage(SEO_IBU_QMQ_SUBJECT);
                message.setProperty("type", SEO_IBU_QMQ_TYPE);
                message.setProperty("version", System.currentTimeMillis());
                message.setProperty("data", data);
                MaiarMessage.wrapQMQ(message);
                messageProducer.sendMessage(message, new MessageSendStateListener() {
                    @Override
                    public void onSuccess(Message message) {
                        log.info("send hot destination to ibu.success", "message producer success");
                    }
                    @Override
                    public void onFailed(Message message) {
                        log.error("send hot destination to ibu.failed", "message producer failed");
                    }
                });
            }
        } catch (Exception e) {
            log.error("ibu seo hot destination message failed", e);
        }
    }

    /**
     * bi 获取热门目的地数据
     */
    private List<GetHostDestinationInfo> getHotDestinationData() throws Exception {
        long offset = 0;
        long row = 500;
        List<GetHostDestinationInfo> result = new ArrayList<>();
        List<GetHostDestinationInfo> temp;
        do {
            // 构建请求参数
            GetDataRequestType soaReq = getGetDataRequestType(row, offset, CAR_POPULAR_AIRPORT_TOKEN);
            GetDataResponseType soaRes = TourAIOneServiceClientForGroup.getClient().getData(soaReq);
            CLogUtil.info("HotDestinationGetData", soaReq, soaRes);
            CLogUtil.esLog("HotDestinationGetData", null, soaReq, soaRes);
            if (StringUtils.isEmpty(soaRes.getBacks())) {
                break;
            }
            temp = JsonUtil.parseArray(soaRes.getBacks(), GetHostDestinationInfo.class);
            result.addAll(temp);
            Optional<Long> max = temp.stream().map(GetHostDestinationInfo::getMysql_id).max(Comparator.comparingLong(o -> o));
            if (max.isPresent()) {
                offset = max.get();
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }


    /**
     * bi 获取热门供应商数据
     */
    private List<GetCarSeoRentalInfo> getCarSeoRental() throws Exception {
        long offset = 0;
        long row = 500;
        List<GetCarSeoRentalInfo> result = new ArrayList<>();
        List<GetCarSeoRentalInfo> temp;
        do {
            GetDataRequestType soaReq = getGetDataRequestType(row, offset, CAR_SEO_RENTAL_TOKEN);
            GetDataResponseType soaRes = TourAIOneServiceClientForGroup.getClient().getData(soaReq);
            CLogUtil.info("HotCarSeoRentalInformationGetData", soaReq, soaRes);
            CLogUtil.esLog("HotCarSeoRentalInformationGetData", null, soaReq, soaRes);
            if (StringUtils.isEmpty(soaRes.getBacks())) {
                break;
            }
            temp = JsonUtil.parseArray(soaRes.getBacks(), GetCarSeoRentalInfo.class);
            result.addAll(temp);
            Optional<Long> max = temp.stream().map(GetCarSeoRentalInfo::getMysql_id).max(Comparator.comparingLong(o -> o));
            if (max.isPresent()) {
                offset = max.get();
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    /**
     * 构建 bi 请求参数
     */
    private GetDataRequestType getGetDataRequestType(long row, long offset, String token) {
        GetDataParam params = new GetDataParam();
        params.setRows(row);
        params.setOffset(offset);
        GetDataRequestType soaReq = new GetDataRequestType();
        soaReq.setApiName(BI_API_NAME);
        soaReq.setToken(token);
        soaReq.setParams(JsonUtil.toJSONString(params));
        return soaReq;
    }

    /**
     * 机场【火车】信息落库
     */
    public void saveDestination(List<GetHostDestinationInfo> hostDestinationInfoData) throws SQLException {
        // 1、查询db所有数据
        Map<String, SeoHotDestinatioinfo> seoHotDestinatioinfoMap = seoHotDestinationBusiness.getSeoHotDestinationinfoList(Boolean.FALSE).stream().collect(Collectors.toMap(SeoHotDestinatioinfo::getPoiCode, Function.identity()));
        Map<Integer, SeoHotCountryinfo> countryinfoMap = seoHotDestinationBusiness.getSeoHotCountryinfoList(Boolean.FALSE).stream().collect(Collectors.toMap(SeoHotCountryinfo::getCountryId, Function.identity()));
        Map<Integer, SeoHotCityinfo> cityinfoMap = seoHotDestinationBusiness.getSeoHotCityinfoList(Boolean.FALSE).stream().collect(Collectors.toMap(SeoHotCityinfo::getCityId, Function.identity()));

        // 2、过滤非法cityId、countryId等data，同时赋值正确的cityId
        Map<String, GetHostDestinationInfo> getHostDestinationInfoMap = hostDestinationInfoData.stream()
                .filter(x -> judgePoiInfoValid(x.getPickuplocationcode(), x.getPickupcountryid(), x.getPickupcityid()))
                .distinct()
                .map(this::completeHotDestinationData)
                .collect(Collectors.toMap(GetHostDestinationInfo::getPickuplocationcode, Function.identity()));

        // 3、需要新请求的country、city、destination
        Set<Long> requestNewInsertCountry = new HashSet<>();
        Map<Long, GetHostDestinationInfo> requestNewInsertCity = new HashMap<>();
        Map<String, GetHostDestinationInfo> requestNewCodeList = new HashMap<>();

        // 4、遍历抽取出新热门的数据，需要发请求拿信息
        for (Map.Entry<String, GetHostDestinationInfo> entry : getHostDestinationInfoMap.entrySet()) {
            Integer pickupCountryId = Integer.valueOf(entry.getValue().getPickupcountryid());
            Integer pickupCityId = Integer.valueOf(entry.getValue().getPickupcityid());
            String code = entry.getValue().getPickuplocationcode();

            if (!countryinfoMap.containsKey(pickupCountryId)) {
                requestNewInsertCountry.add(Long.valueOf(pickupCountryId));
            } else {
                countryinfoMap.get(pickupCountryId).setStatus(SeoHotStatusEnums.TEMP.getCode());
            }

            if (!cityinfoMap.containsKey(pickupCityId)) {
                requestNewInsertCity.put(Long.valueOf(pickupCityId), entry.getValue());
            } else {
                cityinfoMap.get(pickupCityId).setStatus(SeoHotStatusEnums.TEMP.getCode());
            }

            if (!seoHotDestinatioinfoMap.containsKey(code)) {
                requestNewCodeList.put(code, entry.getValue());
            } else {
                seoHotDestinatioinfoMap.get(code).setStatus(SeoHotStatusEnums.TEMP.getCode());
            }
        }

        // 5、拿国家信息【分批】
        Map<Long, Country> countryMap = batchQueryCountryInfo(getHostDestinationInfoMap.values().stream().map(x -> Long.valueOf(x.getPickupcountryid())).distinct().collect(Collectors.toList()), "zh-us");
        // 6、拿城市信息【分批】
        Map<Long, City> cityMap = batchQueryCityInfo(getHostDestinationInfoMap.values().stream().map(x -> Long.valueOf(x.getPickupcityid())).distinct().collect(Collectors.toList()), "zh-us");


        // 7、机场表
        if (CollectionUtils.isNotEmpty(requestNewCodeList.keySet())) {
            seoHotDestinationBusiness.buildNewDestination(requestNewCodeList, countryMap, cityMap);
        }
        if (CollectionUtils.isNotEmpty(seoHotDestinatioinfoMap.values())) {
            List<SeoHotDestinatioinfo> collect = seoHotDestinatioinfoMap.values().stream().map(x -> updateAirport(x, getHostDestinationInfoMap, countryinfoMap, cityMap)).collect(Collectors.toList());
            seoHotDestinationBusiness.batchUpdateDestination(collect);
        }

        // 8、城市表
        if (!requestNewInsertCity.isEmpty()) {
            seoHotDestinationBusiness.buildNewCity(cityMap, countryMap, requestNewInsertCity, countryinfoMap);
        }
        if (CollectionUtils.isNotEmpty(cityinfoMap.values())) {
            List<SeoHotCityinfo> collect = cityinfoMap.values().stream().map(x -> updateCity(x, countryinfoMap)).collect(Collectors.toList());
            seoHotDestinationBusiness.batchUpdateCity(collect);
        }

        // 9、国家表
        if (!requestNewInsertCountry.isEmpty()) {
            seoHotDestinationBusiness.buildNewCountry(countryMap, requestNewInsertCountry);
        }
        if (CollectionUtils.isNotEmpty(countryinfoMap.values())) {
            List<SeoHotCountryinfo> collect = countryinfoMap.values().stream().map(this::updateCountry).collect(Collectors.toList());
            seoHotDestinationBusiness.batchUpdateCountry(collect);
        }
    }

    private SeoHotCountryinfo updateCountry(SeoHotCountryinfo x) {
        x.setStatus(setStatusMethod(x.getStatus()));

        StringBuilder stringBuilder = new StringBuilder();
        String countryName = x.getCountryName();
        if (x.getCountryId().equals(TURKEY_ID_INT)) {
            countryName = TURKEY;
            x.setCountryName(TURKEY);
        }
        stringBuilder.append(PRE_URL).append("to-").append(countryName.toLowerCase().replace(" ", "-")).append("-").append(x.getCountryId()).append("/");
        x.setUrl(stringBuilder.toString());
        return x;
    }

    private SeoHotCityinfo updateCity(SeoHotCityinfo x, Map<Integer, SeoHotCountryinfo> countryinfoMap) {
        x.setStatus(setStatusMethod(x.getStatus()));
        StringBuilder stringBuilder = new StringBuilder();
        String countryName = countryinfoMap.get(x.getCountryId()).getCountryName();
        if (x.getCountryId().equals(TURKEY_ID_INT)) {
            countryName = TURKEY;
        }
        stringBuilder.append(PRE_URL)
                .append("to-").append(countryName.toLowerCase().replace(" ", "-")).append("-").append(x.getCountryId()).append("/")
                .append(x.getCityName().toLowerCase().replace(" ", "-")).append("-").append(x.getCityId()).append("/");
        x.setUrl(stringBuilder.toString());
        return x;
    }

    /**
     * 更新机场信息
     */
    private SeoHotDestinatioinfo updateAirport(SeoHotDestinatioinfo x, Map<String, GetHostDestinationInfo> getHostDestinationInfoMap, Map<Integer, SeoHotCountryinfo> countryinfoMap, Map<Long, City> cityMap) {
        try {
            x.setStatus(setStatusMethod(x.getStatus()));
            if (getHostDestinationInfoMap.containsKey(x.getPoiCode())) {
                GetHostDestinationInfo getHostDestinationInfo = getHostDestinationInfoMap.get(x.getPoiCode());
                x.setOrderNum(getHostDestinationInfo.getAdvanceorder_cnt() != null ? getHostDestinationInfo.getAdvanceorder_cnt().intValue() : 0);
                x.setCityId(Integer.valueOf(getHostDestinationInfo.getPickupcityid()));
                StringBuilder stringBuilder = new StringBuilder();
                String countryName = countryinfoMap.get(x.getCountryId()).getCountryName().toLowerCase().replace(" ", "-");
                if (x.getCountryId().equals(TURKEY_ID_INT)) {
                    countryName = TURKEY;
                }
                String cityName = cityMap.get(Long.valueOf(getHostDestinationInfo.getPickupcityid())).getEnglishName().toLowerCase().replace(" ", "-");
                stringBuilder.append(PRE_URL)
                        .append("to-").append(countryName.toLowerCase()).append("-").append(x.getCountryId())
                        .append("/")
                        .append(cityName).append("-").append(getHostDestinationInfo.getPickupcityid())
                        .append("/")
                        .append(x.getPoiName().toLowerCase().replace(" ", "-")).append("-").append(x.getPoiCode().toLowerCase())
                        .append("/");
                x.setUrl(stringBuilder.toString());
            }
        } catch (Exception e) {
            log.error("updateAirport error", e);
        }
        return x;
    }


    /**
     * 热门供应商信息
     */
    public boolean saveCarSeoRentalInfo(List<GetCarSeoRentalInfo> getCarSeoRentalInfoList) {
        try {
            List<SeoHotInformation> needUpdate = new ArrayList<>();
            List<SeoHotInformation> needInsert = new ArrayList<>();

            Map<String, SeoHotInformation> stringSeoHotInformationMap = seoHotDestinationBusiness.getSeoHotInformation(false)
                    .stream().collect(Collectors.toMap(SeoHotInformation::getPoiCode, Function.identity()));

            Map<String, GetCarSeoRentalInfo> stringGetCarSeoRentalInfoMap = getCarSeoRentalInfoList.stream()
                    .filter(x -> judgePoiInfoValid(x.getPickuplocationcode(), x.getPickcountryid(), x.getPickcityid()))
                    .peek(x -> x.setPickuplocationcode(x.getPickuplocationcode().toUpperCase()))
                    .distinct()
                    .map(this::completeGetSeoHotInformationData)
                    .collect(Collectors.toMap(GetCarSeoRentalInfo::getPickuplocationcode, Function.identity()));

            for (Map.Entry<String, GetCarSeoRentalInfo> entry : stringGetCarSeoRentalInfoMap.entrySet()) {
                if (stringSeoHotInformationMap.containsKey(entry.getKey())) {
                    SeoHotInformation seoHotInformation = stringSeoHotInformationMap.get(entry.getKey());
                    seoHotDestinationBusiness.buildSeoHotInformation(entry.getValue(), seoHotInformation);
                    needUpdate.add(seoHotInformation);
                } else {
                    SeoHotInformation seoHotInformation = new SeoHotInformation();
                    seoHotDestinationBusiness.buildSeoHotInformation(entry.getValue(), seoHotInformation);
                    needInsert.add(seoHotInformation);
                }
            }

            needUpdate.stream().map(SeoHotInformation::getPoiCode).collect(Collectors.toList()).forEach(stringSeoHotInformationMap.keySet()::remove);
            List<SeoHotInformation> collect = stringSeoHotInformationMap.values().stream().peek(x -> x.setStatus(1)).collect(Collectors.toList());
            needUpdate.addAll(collect);

            if (CollectionUtils.isNotEmpty(needUpdate)) {
                seoHotDestinationBusiness.batchUpdateInformation(needUpdate);
            }
            if (CollectionUtils.isNotEmpty(needInsert)) {
                seoHotDestinationBusiness.batchInsertIntoInformation(needInsert);
            }
            return true;
        } catch (SQLException e) {
            log.error("sql exception error", e);
        }
        return false;
    }

    /**
     * 分批次去拿城市信息
     */
    private Map<Long, City> batchQueryCityInfo(List<Long> cityIds, String locale) {
        Map<Long, City> result = new HashMap<>();
        Map<Long, City> temp;
        int offset = 0, size = cityIds.size();
        while (offset < size) {
            temp = cityRepository.findMany(cityIds.stream().skip(offset).limit(Math.min(offset + BATCH_SIZE, size) - offset).collect(Collectors.toList()), locale);
            if (CollectionUtils.isEmpty(temp.values())) {
                break;
            }
            result.putAll(temp);
            offset += BATCH_SIZE;
        }
        return result;

    }

    /**
     * 分批次去拿国家信息
     */
    private Map<Long, Country> batchQueryCountryInfo(List<Long> countryIds, String locale) {
        Map<Long, Country> result = new HashMap<>();
        Map<Long, Country> temp;
        int offset = 0, size = countryIds.size();
        while (offset < size) {
            temp = countryRepository.findMany(countryIds.stream().skip(offset).limit(Math.min(offset + BATCH_SIZE, size) - offset).collect(Collectors.toList()), locale);
            if (CollectionUtils.isEmpty(temp.values())) {
                break;
            }
            result.putAll(temp);
            offset += BATCH_SIZE;
        }
        return result;
    }

    /**
     * 请求拿城市id和机场name
     */
    private GetHostDestinationInfo completeHotDestinationData(GetHostDestinationInfo destinationInfo) {
        destinationInfo.setPickuplocationcode(destinationInfo.getPickuplocationcode().toUpperCase());
        List<Airport> basicAirportQuery = osdBasicDataProxy.getAirportNoCityId(destinationInfo.getPickuplocationcode(), "en-US");
        if (CollectionUtils.isNotEmpty(basicAirportQuery)) {
            destinationInfo.setPickupcityid(String.valueOf(basicAirportQuery.get(0).getCityId()));
            destinationInfo.setPickuplocationname(basicAirportQuery.get(0).getAirportEName());
        }
        return destinationInfo;
    }

    /**
     * 请求拿城市id和机场name
     */
    private GetCarSeoRentalInfo completeGetSeoHotInformationData(GetCarSeoRentalInfo getCarSeoRentalInfo) {
        getCarSeoRentalInfo.setPickuplocationcode(getCarSeoRentalInfo.getPickuplocationcode().toUpperCase());
        List<Airport> basicAirportQuery = osdBasicDataProxy.getAirportNoCityId(getCarSeoRentalInfo.getPickuplocationcode(), "en-US");
        if (CollectionUtils.isNotEmpty(basicAirportQuery)) {
            getCarSeoRentalInfo.setPickcityid(String.valueOf(basicAirportQuery.get(0).getCityId()));
        }
        return getCarSeoRentalInfo;
    }

    /**
     * 过滤非法数据
     */
    private boolean judgePoiInfoValid(String code, String countryId, String cityId) {
        return StringUtils.isNotEmpty(code) && StringUtils.isNumeric(countryId) && StringUtils.isNumeric(cityId);
    }

    /**
     * 更新状态字段
     *
     */
    private int setStatusMethod(Integer x) {
        return x <= 1 ? SeoHotStatusEnums.INACTIVE.getCode() : SeoHotStatusEnums.ACTIVE.getCode();
    }
}

