package com.ctrip.car.market.job.domain.schedule;

import com.ctrip.car.market.job.domain.config.CarServiceConfig;
import com.ctrip.car.market.job.domain.dto.ZhiJiaMetaData;
import com.ctrip.car.market.job.domain.dto.ZhiJiaMetaDataOrigin;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.util.concurrent.ListenableFuture;
import com.ning.http.client.Response;
import org.springframework.stereotype.Component;
import qunar.hc.QHttpOption;
import qunar.hc.QunarAsyncClient;
import qunar.tc.qschedule.config.QSchedule;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.UUID;

@Component
public class QiCheZhiJiaSchedule {

    private static ILog log = LogManager.getLogger(QiCheZhiJiaSchedule.class);

    private static final QunarAsyncClient client = new QunarAsyncClient();

    @Resource
    private CarServiceConfig carServiceConfig;

    @QSchedule("car.market.common.job.qczj.brush.data.job")
    public void task() {
        try {
            int sum = new Integer(carServiceConfig.getValueFromConfConfigByKey("qichezhijiaCount"));
            int random = new Integer(java.util.Optional.ofNullable(carServiceConfig.getValueFromConfConfigByKey("qichezhijiarandom")).orElse("100"));
            sum += (int) (Math.random() * (random - 1) + 1);
            for (int i = 0; i < sum; i++) {
                ZhiJiaMetaData metaData = new ZhiJiaMetaData();
                metaData.setCuid(UUID.randomUUID().toString());
                metaData.setDocid("59_1010_0");
                metaData.setCtime(System.currentTimeMillis() + "");
                metaData.setDuration((int) (Math.random() * 10000 + i) + "");
                metaData.setBiz_type("car");
                metaData.setSource("" + (int) (Math.random() * (4 - 1) + 1));
                metaData.setPlat("" + (int) (Math.random() * (3 - 1) + 1));
                metaData.setItype("" + (int) (Math.random() * (7 - 1) + 1));
                try {
                    Thread.sleep(new Integer(carServiceConfig.getValueFromConfConfigByKey("qichezhijiaSleep")));
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    log.warn("QiCheZhiJiaManager", e);
                }
                ZhiJiaMetaDataOrigin zhiJiaMetaDataOrigin = new ZhiJiaMetaDataOrigin();
                zhiJiaMetaDataOrigin.setMetaData(Arrays.asList(metaData));
                zhiJiaMetaDataOrigin.setSourceCode("xiecheng");

                QHttpOption option = new QHttpOption();
                option.addHeader("Content-Type", "application/json;charset=UTF-8");
                option.setPostBodyData(JsonUtil.toJSONString(zhiJiaMetaDataOrigin));
                ListenableFuture<Response> response = client.post("http://ptback.api.autohome.com.cn/postback/metadata?sourceCode=xiecheng", option);
                response.get().getResponseBody();
            }
        } catch (Exception ex) {
            log.warn("QiCheZhiJiaManager", ex);
        }
    }
}
