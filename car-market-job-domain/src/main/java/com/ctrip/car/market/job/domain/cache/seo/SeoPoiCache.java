package com.ctrip.car.market.job.domain.cache.seo;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.car.market.job.domain.cache.BasicCacheAbstract;
import com.ctrip.car.market.job.domain.service.seo.QueryAllSeoHotPoiService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.SEO_POI_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.SeoPoiCacheName;

@Component(SeoPoiCache.Name)
@CreateCacheArea(area = "public")
public class SeoPoiCache extends BasicCacheAbstract<String, List<SeoHotDestinatioinfoDO>> {

    public static final String Name = SeoPoiCacheName;

    @Resource
    private QueryAllSeoHotPoiService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = SEO_POI_KEY, remotePreLoadName = Name)
    private Cache<String, List<SeoHotDestinatioinfoDO>> cache;

    @Override
    public ConvertMessage<String, List<SeoHotDestinatioinfoDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<String, List<SeoHotDestinatioinfoDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<String, List<SeoHotDestinatioinfoDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<String, List<SeoHotDestinatioinfoDO>> getLoader() {
        return null;
    }
}
