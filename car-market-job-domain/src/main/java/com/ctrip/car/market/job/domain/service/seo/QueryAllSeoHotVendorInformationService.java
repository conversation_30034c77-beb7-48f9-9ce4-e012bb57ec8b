package com.ctrip.car.market.job.domain.service.seo;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotVendorInformationMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotVendorInformation;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllSeoHotVendorInformationService implements CachePreLoader<String, List<SeoHotVendorInformationDO>>, CacheLoader<String, List<SeoHotVendorInformationDO>> {

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private SeoHotVendorInformationMapper mapper;

    private Map<String, List<SeoHotVendorInformationDO>> getAllVendorInformation() throws Exception {
        List<SeoHotVendorInformation> data = marketDBService.queryALlVendorInformation();
        return data.stream().filter(l -> StringUtils.isNotEmpty(l.getVendorCode())).map(mapper::to).collect(Collectors.groupingBy(l -> l.getVendorCode().toLowerCase()));
    }

    @Override
    public List<SeoHotVendorInformationDO> load(String area, String key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<String, List<SeoHotVendorInformationDO>> loadAll(String area, Set<String> keys) throws Throwable {
        return getAllVendorInformation();
    }

    @Override
    public Map<String, List<SeoHotVendorInformationDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllVendorInformation();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
