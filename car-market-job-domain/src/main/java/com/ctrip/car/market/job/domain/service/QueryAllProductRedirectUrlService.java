package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnProductRedirectUrlDO;
import com.ctrip.car.market.job.domain.mapper.ProductRedirectUrlMapper;
import com.ctrip.car.market.job.repository.entity.CpnProductRedirectUrl;
import com.ctrip.car.market.job.repository.service.RestrictedService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllProductRedirectUrlService implements CachePreLoader<String, List<CpnProductRedirectUrlDO>>, CacheLoader<String, List<CpnProductRedirectUrlDO>> {

    @Resource
    private RestrictedService service;

    @Resource
    private ProductRedirectUrlMapper mapper;

    private Map<String, List<CpnProductRedirectUrlDO>> getAllProductRedirectUrl() throws Exception {
        List<CpnProductRedirectUrl> data = service.queryAllProductRedirectUrl();
        if (CollectionUtils.isEmpty(data)) {
            Map<String, List<CpnProductRedirectUrlDO>> map = Maps.newHashMap();
            map.put("_flag", Lists.newArrayList());
            return map;
        }
        return data.stream().map(mapper::to).collect(Collectors.groupingBy(CpnProductRedirectUrlDO::getUnionType));
    }

    @Override
    public List<CpnProductRedirectUrlDO> load(String area, String key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<String, List<CpnProductRedirectUrlDO>> loadAll(String area, Set<String> keys) throws Throwable {
        return getAllProductRedirectUrl();
    }

    @Override
    public Map<String, List<CpnProductRedirectUrlDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllProductRedirectUrl();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
