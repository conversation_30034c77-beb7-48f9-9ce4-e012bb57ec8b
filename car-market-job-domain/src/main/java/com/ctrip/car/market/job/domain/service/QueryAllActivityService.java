package com.ctrip.car.market.job.domain.service;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.common.entity.act.CustomContent;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.domain.mapper.ActInfoMapper;
import com.ctrip.car.market.job.repository.entity.activity.ActCtripactinfo;
import com.ctrip.car.market.job.repository.service.ActivityService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;
import com.ctrip.car.market.job.domain.utils.JsonUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class QueryAllActivityService implements CachePreLoader<Long, ActInfoDO>, CacheLoader<Long, ActInfoDO> {

    private final ILog log = LogManager.getLogger(QueryAllActivityService.class);

    @Resource
    private ActivityService service;

    @Resource
    private ActInfoMapper mapper;

    private Map<Long, ActInfoDO> getAllActivity() throws Exception {
        List<ActCtripactinfo> data = service.queryAllActivity();
        for (ActCtripactinfo item : data) {
            try {
                item.setContent(JsonUtils.toObject(item.getCustomContent(), CustomContent.class));
            } catch (Exception e) {
                log.warn("getAllActivity", e);
            }
        }
        return data.stream().map(mapper::to).collect(Collectors.toMap(ActInfoDO::getId, l -> l, (k1, k2) -> k1));
    }

    @Override
    public ActInfoDO load(String area, Long key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<Long, ActInfoDO> loadAll(String area, Set<Long> keys) throws Throwable {
        return getAllActivity();
    }

    @Override
    public Map<Long, ActInfoDO> preLoad(String s, Date date) {
        try {
            return getAllActivity();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
