package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateConsumerByQmq;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.CpnRestrictedPromotionDO;
import com.ctrip.car.market.job.domain.message.RestrictedPromotion_SummaryMessageConvert;
import com.ctrip.car.market.job.domain.service.QueryAllRestrictedPromotion_SummaryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.RESTRICTED_PROMOTION_SUMMARY_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.RestrictedPromotion_SummaryCacheName;
import static com.ctrip.car.market.job.common.consts.MqConst.BasicDataChangeSubject;

@Component(RestrictedPromotion_SummaryCache.Name)
@CreateCacheArea(area = "public")
public class RestrictedPromotion_SummaryCache extends BasicCacheAbstract<Long, List<CpnRestrictedPromotionDO>> {

    public static final String Name = RestrictedPromotion_SummaryCacheName;

    @Resource
    private RestrictedPromotion_SummaryMessageConvert convert;

    @Resource
    private QueryAllRestrictedPromotion_SummaryService service;

    @IncUpdateConsumerByQmq(subject = BasicDataChangeSubject, convertBeanName = Name)
    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = RESTRICTED_PROMOTION_SUMMARY_HASH_KEY, remotePreLoadName = Name)
    private Cache<Long, List<CpnRestrictedPromotionDO>> cache;

    @Override
    public ConvertMessage<Long, List<CpnRestrictedPromotionDO>> getConvertMessage() {
        return convert;
    }

    @Override
    public CachePreLoader<Long, List<CpnRestrictedPromotionDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, List<CpnRestrictedPromotionDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, List<CpnRestrictedPromotionDO>> getLoader() {
        return null;
    }
}
