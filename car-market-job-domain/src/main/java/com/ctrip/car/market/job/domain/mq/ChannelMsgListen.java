package com.ctrip.car.market.job.domain.mq;

import com.ctrip.car.market.job.domain.dto.ChannelDTO;
import com.ctrip.car.market.job.domain.enums.ChannelEventTypeEnum;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.ctrip.car.market.job.repository.entity.Channel;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Component
public class ChannelMsgListen {

    private final ILog log = LogManager.getLogger(ChannelMsgListen.class);

    @Resource
    private MarketDBService marketDBService;

    @QmqConsumer(prefix = "dcs.car.channel.changed", consumerGroup = "100011922-task-watch")
    public void channelConsumer(Message msg) {
        log.info("ChannelMsgListenMessage", JsonUtil.toJSONString(msg));
        String eventType = msg.getStringProperty("eventType");
        String primaryId = msg.getStringProperty("primaryId");
        String data = msg.getStringProperty("data");
        if (StringUtils.isEmpty(eventType)) {
            return;
        }
        ChannelDTO channelDTO = null;
        if (!ChannelEventTypeEnum.DELETE.getType().equals(eventType)) {
            channelDTO = JsonUtil.parseObject(data, ChannelDTO.class);
        }
        try {
            Channel sample = new Channel();
            sample.setTertiaryChannelId(Long.valueOf(primaryId));
            List<Channel> channels = marketDBService.queryChannel(sample);
            if (ChannelEventTypeEnum.DELETE.getType().equals(eventType)) {
                if (CollectionUtils.isEmpty(channels)) {
                    log.warn("channelNumberConsumer", "not find channel_" + eventType + "_" + primaryId);
                    return;
                }
                Channel channel = new Channel();
                channel.setId(channels.get(0).getId());
                int delete = marketDBService.deleteChannel(channel);
                setLog(delete, msg);
            } else if (ChannelEventTypeEnum.INSERT.getType().equals(eventType)) {
                if (CollectionUtils.isNotEmpty(channels)) {
                    log.warn("channelNumberConsumer", "channel munber have exit_" + eventType + "_" + primaryId);
                    return;
                }
                Channel channel = getChannelByMessage(channelDTO);
                int insert = marketDBService.insertChannel(channel);
                setLog(insert, msg);
            } else if (ChannelEventTypeEnum.UPDATE.getType().equals(eventType)) {
                if (CollectionUtils.isEmpty(channels)) {
                    log.warn("channelNumberConsumer", "not find channel munber_" + eventType + "_" + primaryId);
                    Channel channel = getChannelByMessage(channelDTO);
                    int insert = marketDBService.insertChannel(channel);
                    setLog(insert, msg);
                    return;
                }
                Channel channel = getChannelByMessage(channelDTO);
                channel.setId(channels.get(0).getId());
                int update = marketDBService.updateChannel(channel);
                setLog(update, msg);
            }
        } catch (Exception e) {
            log.error("ChannelMsgListen", e);
        }
    }

    private Channel getChannelByMessage(ChannelDTO msg) {
        Channel channel = new Channel();
        channel.setPrimaryChannelId(msg.getPrimaryChannelId());
        channel.setPrimaryChannelName(msg.getPrimaryChannelName());
        channel.setSecondaryChannelId(msg.getSecondaryChannelId());
        channel.setSecondaryChannelName(msg.getSecondaryChannelName());
        channel.setTertiaryChannelId(msg.getTertiaryChannelId());
        channel.setTertiaryChannelName(msg.getTertiaryChannelName());

        channel.setOperatorId(msg.getOperatorId());
        channel.setOperator(msg.getOperator());
        channel.setDatachangeLasttime(new Timestamp(new Date().getTime()));
        if (msg.getValid() != null) {
            if (msg.getValid()) {
                channel.setValid(1);
            } else {
                channel.setValid(0);
            }
        }
        return channel;
    }

    private void setLog(int isSuccess, Message msg) {
        if (isSuccess > 0) {
            log.info("ChannelMsgListen", JsonUtil.toJSONString(msg));
        } else {
            log.warn("ChannelMsgListen", JsonUtil.toJSONString(msg));
        }
    }
}
