package com.ctrip.car.market.job.domain.service.seo;

import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorCityDO;
import com.ctrip.car.market.job.domain.mapper.SeoHotVendorCityMapper;
import com.ctrip.car.market.job.repository.entity.SeoHotVendorCity;
import com.ctrip.car.market.job.repository.service.MarketDBService;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class QueryAllHotVendorCityService implements CachePreLoader<String, List<SeoHotVendorCityDO>>, CacheLoader<String, List<SeoHotVendorCityDO>> {

    @Resource
    private MarketDBService marketDBService;

    @Resource
    private SeoHotVendorCityMapper mapper;

    private Map<String, List<SeoHotVendorCityDO>> getAllHotVendorCity() throws Exception {
        List<SeoHotVendorCity> data = marketDBService.queryAllHotVendorCity();
        return data.stream().filter(l -> StringUtils.isNotEmpty(l.getVendorId())).map(mapper::to).collect(Collectors.groupingBy(l -> l.getVendorId().toLowerCase()));
    }

    @Override
    public List<SeoHotVendorCityDO> load(String area, String key) throws Throwable {
        return loadAll(area, Sets.newHashSet(key)).get(key);
    }

    @Override
    public Map<String, List<SeoHotVendorCityDO>> loadAll(String area, Set<String> keys) throws Throwable {
        return getAllHotVendorCity();
    }

    @Override
    public Map<String, List<SeoHotVendorCityDO>> preLoad(String area, Date lastUpdateTime) {
        try {
            return getAllHotVendorCity();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
