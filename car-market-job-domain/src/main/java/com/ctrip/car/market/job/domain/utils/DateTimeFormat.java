package com.ctrip.car.market.job.domain.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

public class DateTimeFormat {

    public final static String yyyyMMddHHmm = "yyyy-MM-dd HH:mm";

    public final static String yyyyMMdd = "yyyy-MM-dd";

    public final static String yyyyMMddHHmmSS = "yyyy-MM-dd HH:mm:ss";

    public final static String yyyyMMddHH= "yyyy/MM/dd HH:mm";


    public static Date parse(String date) {
        if (Objects.nonNull(date)) {
            SimpleDateFormat format = new SimpleDateFormat(yyyyMMddHHmmSS);
            try {
                return format.parse(date);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static String format(Timestamp ts, String pattern) {
        if (Objects.isNull(ts)||Objects.isNull(pattern)) {
            return null;
        }
        return DateFormatUtils.format(ts.getTime(), pattern);
    }

    public static String format(Calendar ts, String pattern) {
        if (Objects.isNull(ts) || Objects.isNull(pattern)) {
            return null;
        }
        return DateFormatUtils.format(ts, pattern);
    }

    public static String yyyyMMddHHmm(Timestamp ts) {
        if (Objects.isNull(ts)) {
            return null;
        }
        return DateFormatUtils.format(ts.getTime(), yyyyMMddHHmm);
    }

    public static String yyyyMMddHHmmss(Timestamp ts) {
        if (Objects.isNull(ts)) {
            return null;
        }
        return DateFormatUtils.format(ts.getTime(), yyyyMMddHHmmSS);
    }

    public static String yyyyMMdd(Timestamp ts) {
        if (Objects.isNull(ts)) {
            return null;
        }
        return DateFormatUtils.format(ts.getTime(), yyyyMMdd);
    }

    public static String yyyyMMdd(Calendar cl) {
        if (Objects.isNull(cl)) {
            return null;
        }
        return DateFormatUtils.format(cl, yyyyMMdd);
    }
}
