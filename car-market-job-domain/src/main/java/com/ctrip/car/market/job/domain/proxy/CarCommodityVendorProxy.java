package com.ctrip.car.market.job.domain.proxy;

import com.ctrip.car.commodity.vendor.query.service.method.QueryVendorBrandListRequestType;
import com.ctrip.car.commodity.vendor.query.service.method.QueryVendorBrandListResponseType;
import com.ctrip.car.commodity.vendor.query.service.soa.CarCommodityVendorQueryApiClient;
import com.ctrip.car.top.BaseRequest;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class CarCommodityVendorProxy {

    private final static CarCommodityVendorQueryApiClient cityClient = CarCommodityVendorQueryApiClient.getInstance();

    public String queryVendorLogo(String vendorCode) {
        QueryVendorBrandListRequestType requestType = new QueryVendorBrandListRequestType();
        requestType.setBaseRequest(new BaseRequest());
        requestType.getBaseRequest().setRequestId(UUID.randomUUID().toString());
        requestType.setBrandCodes(Lists.newArrayList(vendorCode));
        try {
            QueryVendorBrandListResponseType responseType = cityClient.queryVendorBrandList(requestType);
            if (responseType != null && CollectionUtils.isNotEmpty(responseType.getBrands())) {
                return responseType.getBrands().get(0).getBrandEnUrl();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
