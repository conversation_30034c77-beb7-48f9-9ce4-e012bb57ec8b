package com.ctrip.car.market.job.domain.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.ChannelNumberDO;
import com.ctrip.car.market.job.domain.service.QueryAllChannelNumberService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.CHANNEL_NUMBER_HASH_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.ChannelNumberCacheName;

@Component(ChannelNumberCache.Name)
@CreateCacheArea(area = "public")
public class ChannelNumberCache extends BasicCacheAbstract<Long, ChannelNumberDO> {

    public static final String Name = ChannelNumberCacheName;

    @Resource
    private QueryAllChannelNumberService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = CHANNEL_NUMBER_HASH_KEY, remotePreLoadName = Name)
    private Cache<Long, ChannelNumberDO> cache;

    @Override
    public ConvertMessage<Long, ChannelNumberDO> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<Long, ChannelNumberDO> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<Long, ChannelNumberDO> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<Long, ChannelNumberDO> getLoader() {
        return null;
    }
}
