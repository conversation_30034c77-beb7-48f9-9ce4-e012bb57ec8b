package com.ctrip.car.market.job.domain.mapper;

import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorCityDO;
import com.ctrip.car.market.job.repository.entity.SeoHotVendorCity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SeoHotVendorCityMapper {

    SeoHotVendorCityDO to(SeoHotVendorCity value);

    List<SeoHotVendorCityDO> to(List<SeoHotVendorCity> values);
}
