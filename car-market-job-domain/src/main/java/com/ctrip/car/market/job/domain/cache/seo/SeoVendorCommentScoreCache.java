package com.ctrip.car.market.job.domain.cache.seo;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheLoader;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheArea;
import com.alicp.jetcache.anno.IncUpdateProducerByRedis;
import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.alicp.jetcache.support.preload.CachePreLoader;
import com.ctrip.car.market.job.common.entity.seo.SeoVendorCommentScoreDO;
import com.ctrip.car.market.job.domain.cache.BasicCacheAbstract;
import com.ctrip.car.market.job.domain.service.seo.QueryAllSeoVendorCommentScoreService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst.SEO_VENDOR_COMMENT_SCORE_KEY;
import static com.ctrip.car.market.job.common.consts.CacheName.SeoVendorCommentScoreCacheName;

@Component(SeoVendorCommentScoreCache.Name)
@CreateCacheArea(area = "public")
public class SeoVendorCommentScoreCache extends BasicCacheAbstract<String, List<SeoVendorCommentScoreDO>> {

    public static final String Name = SeoVendorCommentScoreCacheName;

    @Resource
    private QueryAllSeoVendorCommentScoreService service;

    @IncUpdateProducerByRedis
    @CreateCache(name = Name, redisHashKey = SEO_VENDOR_COMMENT_SCORE_KEY, remotePreLoadName = Name)
    private Cache<String, List<SeoVendorCommentScoreDO>> cache;

    @Override
    public ConvertMessage<String, List<SeoVendorCommentScoreDO>> getConvertMessage() {
        return null;
    }

    @Override
    public CachePreLoader<String, List<SeoVendorCommentScoreDO>> getPreLoader() {
        return service;
    }

    @Override
    protected Cache<String, List<SeoVendorCommentScoreDO>> getCache() {
        return cache;
    }

    @Override
    public CacheLoader<String, List<SeoVendorCommentScoreDO>> getLoader() {
        return null;
    }
}
