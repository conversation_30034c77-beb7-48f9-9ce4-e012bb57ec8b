package com.ctrip.car.market.job.domain.message;

import com.alicp.jetcache.support.incupdate.ConvertMessage;
import com.ctrip.car.market.job.common.entity.CpnLabelDO;
import com.ctrip.car.market.job.domain.enums.TabelEnum;
import com.ctrip.car.market.job.domain.mapper.LabelMapper;
import com.ctrip.car.market.job.repository.entity.CpnLabel;
import com.ctrip.car.market.job.repository.service.LabelService;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

@Component
public class LabelMessageConvert extends AbstractConvertMessage<Long, CpnLabel, CpnLabelDO> implements ConvertMessage<Long, CpnLabelDO> {

    @Resource
    private LabelService service;

    @Resource
    private LabelMapper mapper;

    public LabelMessageConvert() {
        super(TabelEnum.Label);
    }

    @Override
    public Map<Long, CpnLabelDO> convertMessage(String area, Message message) {
        try {
            return super.getMap(message);
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    @Override
    public Long getKey(String ids) {
        return Long.valueOf(ids);
    }

    @Override
    public CpnLabel getData(Long id) throws Exception {
        return service.queryLabel(id);
    }

    @Override
    public CpnLabelDO mapper(CpnLabel value) {
        return mapper.to(value);
    }
}
