package com.ctrip.car.market.job.domain.config;

import com.ctrip.car.market.job.domain.dto.StoreItem;
import com.ctrip.car.market.job.domain.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ShopOpenConfig {

    @QMapConfig("shopOpen.properties")
    public void shopOpenOnChange(Map<String, String> map) throws Exception {
        this.supplierIdList = JsonUtil.parseArray(map.getOrDefault("supplierIdList", "[]"),Long.class );
        this.storeList = JsonUtil.parseArray(map.getOrDefault("storeList", "[]"), StoreItem.class);
        if (CollectionUtils.isNotEmpty(storeList)) {
            for (StoreItem item : storeList) {
                if (StringUtils.isNotBlank(item.getStoreIds())) {
                    String storeIds = item.getStoreIds();
                    item.setStoreIdList(Arrays.stream(storeIds.split(",")).filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList()));
                }
            }
        }
    }

    private List<Long> supplierIdList;

    private List<StoreItem> storeList;

    public List<Long> getSupplierIdList() {
        return supplierIdList;
    }

    public List<StoreItem> getStoreList() {
        return storeList;
    }
}
