package com.ctrip.car.market.job.common.entity.seo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class SeoHotDestinatioinfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    @JsonProperty("b")
    private Integer poiType;

    @JsonProperty("c")
    private Long poiId;

    @JsonProperty("d")
    private String poiCode;

    @JsonProperty("e")
    private String poiName;

    @JsonProperty("f")
    private Integer countryId;

    @JsonProperty("g")
    private Integer cityId;

    @JsonProperty("h")
    private String url;

    @JsonProperty("i")
    private Integer status;

    @JsonProperty("j")
    private Integer orderNum;

    @JsonProperty("k")
    private Timestamp datachangeCreatetime;

    @JsonProperty("l")
    private Timestamp datachangeLasttime;
}
