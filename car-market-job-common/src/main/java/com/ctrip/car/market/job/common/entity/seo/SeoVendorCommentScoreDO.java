package com.ctrip.car.market.job.common.entity.seo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Getter
@Setter
public class SeoVendorCommentScoreDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    @JsonProperty("b")
    private String vendorCode;

    @JsonProperty("c")
    private BigDecimal socre;

    @JsonProperty("d")
    private String subItemScore;

    @JsonProperty("e")
    private Integer totalCount;

    @JsonProperty("f")
    private Integer status;

    @JsonProperty("g")
    private Timestamp datachangeCreatetime;

    @JsonProperty("h")
    private Timestamp datachangeLasttime;
}
