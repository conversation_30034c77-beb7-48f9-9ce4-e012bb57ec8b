package com.ctrip.car.market.job.common.entity.seo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class SeoHotVendorInformationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    @JsonProperty("b")
    private String vendorCode;

    @JsonProperty("c")
    private String vendorName;

    @JsonProperty("d")
    private Integer cityId;

    @JsonProperty("e")
    private String cityName;

    @JsonProperty("f")
    private Integer poiType;

    @JsonProperty("g")
    private String poiCode;

    @JsonProperty("h")
    private String poiName;

    @JsonProperty("i")
    private Integer vehicleGroupId;

    @JsonProperty("j")
    private Long vehicleId;

    @JsonProperty("k")
    private Integer tenancy;

    @JsonProperty("l")
    private Integer searchNum;

    @JsonProperty("m")
    private Integer storeNum;

    @JsonProperty("n")
    private Integer status;

    @JsonProperty("o")
    private Timestamp datachangeCreatetime;

    @JsonProperty("p")
    private Timestamp datachangeLasttime;
}
