package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class ActSkuInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    @JsonProperty("b")
    private Long activityId;

    @JsonProperty("c")
    private Long skuId;

    @JsonProperty("d")
    private Boolean isActive;

    @JsonProperty("e")
    private Timestamp datachangeCreatetime;

    @JsonProperty("f")
    private Timestamp datachangeLasttime;
}
