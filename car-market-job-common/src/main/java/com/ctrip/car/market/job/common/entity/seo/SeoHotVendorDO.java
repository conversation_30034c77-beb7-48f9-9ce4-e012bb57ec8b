package com.ctrip.car.market.job.common.entity.seo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class SeoHotVendorDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    @JsonProperty("b")
    private String vendorId;

    @JsonProperty("c")
    private String vendorName;

    @JsonProperty("d")
    private String url;

    @JsonProperty("e")
    private Integer status;

    @JsonProperty("f")
    private Timestamp datachangeCreatetime;

    @JsonProperty("g")
    private Timestamp datachangeLasttime;
}
