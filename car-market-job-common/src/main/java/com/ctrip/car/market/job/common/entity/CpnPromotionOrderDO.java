package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class CpnPromotionOrderDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    @JsonProperty("b")
    private Integer promotionId;

    @JsonProperty("c")
    private String promotionName;

    @JsonProperty("d")
    private Timestamp startTime;

    @JsonProperty("e")
    private Timestamp endTime;

    @JsonProperty("f")
    private Integer useStation;

    @JsonProperty("g")
    private Integer status;

    @JsonProperty("h")
    private Integer customerType;

    @JsonProperty("i")
    private String content;

    @JsonProperty("j")
    private String createUser;

    @JsonProperty("k")
    private String updateUser;

    @JsonProperty("l")
    private Timestamp datachangeCreatetime;

    @JsonProperty("m")
    private Timestamp datachangeLasttime;
}
