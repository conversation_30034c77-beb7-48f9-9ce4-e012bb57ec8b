package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class CpnRestrictedConditionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Integer conditionId;

    @JsonProperty("b")
    private Integer promotionId;

    @JsonProperty("c")
    private String conditionName;

    @JsonProperty("d")
    private String content;

    @JsonProperty("e")
    private Timestamp datachangeCreatetime;

    @JsonProperty("f")
    private Timestamp datachangeLasttime;

    @JsonProperty("g")
    private Integer isValid;

    @JsonProperty("h")
    private Integer groupId;

    @JsonProperty("i")
    private Integer groupVersion;
}
