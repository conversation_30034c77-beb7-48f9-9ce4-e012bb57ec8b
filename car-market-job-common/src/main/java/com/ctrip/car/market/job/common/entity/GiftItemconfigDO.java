package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class GiftItemconfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long itemid;

    @JsonProperty("b")
    private String giftNo;

    @JsonProperty("c")
    private Integer isdefault;

    @JsonProperty("d")
    private Integer itemno;

    @JsonProperty("e")
    private Integer percent;

    @JsonProperty("f")
    private String url;

    @JsonProperty("g")
    private String urlh5;

    @JsonProperty("h")
    private Integer promotion;

    @JsonProperty("i")
    private String itemremark;

    @JsonProperty("j")
    private Timestamp datachangeCreatetime;

    @JsonProperty("k")
    private Timestamp datachangeLasttime;

    @JsonProperty("l")
    private String imageUrl;

    @JsonProperty("m")
    private String config;
}
