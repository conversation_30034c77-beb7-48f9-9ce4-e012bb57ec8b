package com.ctrip.car.market.job.common.entity.seo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class SeoHotProvinceinfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    @JsonProperty("b")
    private Integer provinceId;

    @JsonProperty("c")
    private String provinceName;

    @JsonProperty("d")
    private Integer countryId;

    @JsonProperty("e")
    private String url;

    @JsonProperty("f")
    private Integer status;

    @JsonProperty("g")
    private Timestamp datachangeCreatetime;

    @JsonProperty("h")
    private Timestamp datachangeLasttime;
}
