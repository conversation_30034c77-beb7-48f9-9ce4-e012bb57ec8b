package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class CpnGroupRcConditionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Integer groupID;

    @JsonProperty("b")
    private Timestamp datachangeCreatetime;

    @JsonProperty("c")
    private Timestamp datachangeLasttime;

    @JsonProperty("d")
    private Integer currentVersion;
}
