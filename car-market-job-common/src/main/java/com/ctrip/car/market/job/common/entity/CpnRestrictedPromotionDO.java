package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Getter
@Setter
public class CpnRestrictedPromotionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Integer promotionId;

    @JsonProperty("b")
    private String promotionName;

    @JsonProperty("c")
    private String promotionDisplayName;

    @JsonProperty("d")
    private String remark;

    @JsonProperty("e")
    private String createUser;

    @JsonProperty("f")
    private Timestamp datachangeCreatetime;

    @JsonProperty("g")
    private String modifyUser;

    @JsonProperty("h")
    private Timestamp datachangeLasttime;

    @JsonProperty("i")
    private Integer deductionType;

    @JsonProperty("j")
    private BigDecimal deductionAmount;

    @JsonProperty("k")
    private Timestamp startDate;

    @JsonProperty("l")
    private Timestamp disableDate;

    @JsonProperty("m")
    private Integer cooperativeDepartment;

    @JsonProperty("n")
    private String cooperativeDepartmentType;

    @JsonProperty("o")
    private Long summaryId;
}
