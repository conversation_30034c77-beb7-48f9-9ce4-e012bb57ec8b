package com.ctrip.car.market.job.common.entity;

import com.ctrip.car.market.common.entity.act.ActivityTempContent;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Types;

@Getter
@Setter
public class ActTempInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long tmpId;

    @JsonProperty("b")
    private Integer templateType;

    @JsonProperty("c")
    private String name;

    @JsonProperty("d")
    private String remark;

    @JsonProperty("e")
    private Integer labelId;

    @JsonProperty("f")
    private Integer priority;

    @JsonProperty("g")
    private Timestamp registerStartTime;

    @JsonProperty("h")
    private Timestamp registerEndTime;

    @JsonProperty("i")
    private Timestamp activityStartTime;

    @JsonProperty("j")
    private Timestamp activityEndTime;

    @JsonProperty("k")
    private String excludeDate;

    @JsonProperty("l")
    private String repetitionPeriod;

    @JsonProperty("m")
    private String tempContent;

    @JsonProperty("n")
    private String createUser;

    @JsonProperty("o")
    private Timestamp datachangeCreatetime;

    @JsonProperty("p")
    private String modifyUser;

    @JsonProperty("q")
    private Integer deductionType;

    @JsonProperty("r")
    private Integer costShare;

    @JsonProperty("s")
    private Integer status;

    @JsonProperty("t")
    private Timestamp datachangeLasttime;

    @JsonProperty("u")
    private ActivityTempContent content;

    @JsonProperty("v")
    private Integer groupId;

    @JsonProperty("w")
    private Boolean autoSignUp;

    @JsonProperty("x")
    private Integer discountBase;

    @JsonProperty("y")
    private Integer supportModifyOrder;

    @JsonProperty("z")
    private Integer needRegister;
}
