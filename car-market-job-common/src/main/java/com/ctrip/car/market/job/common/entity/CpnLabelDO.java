package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class CpnLabelDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long code;

    @JsonProperty("b")
    private Integer cType;

    @JsonProperty("c")
    private String name;

    @JsonProperty("d")
    private String description;

    @JsonProperty("e")
    private Integer pos;

    @JsonProperty("f")
    private String groupCode;

    @JsonProperty("g")
    private String groupName;

    @JsonProperty("h")
    private Integer source;

    @JsonProperty("i")
    private Boolean isDisplay;

    @JsonProperty("j")
    private Boolean isActive;

    @JsonProperty("k")
    private Timestamp datachangeCreatetime;

    @JsonProperty("l")
    private Timestamp datachangeLasttime;

    @JsonProperty("m")
    private Boolean isListDisplay;

    @JsonProperty("n")
    private Integer status;

    @JsonProperty("o")
    private Integer productCategoryId;

    @JsonProperty("p")
    private Integer platform;

    @JsonProperty("q")
    private Integer groupId;

    @JsonProperty("r")
    private Integer positiveType;

    @JsonProperty("s")
    private Integer showType;

    @JsonProperty("t")
    private String imgUrl;

    @JsonProperty("u")
    private String shortDesc;

    @JsonProperty("v")
    private String filterImgUrl;

    @JsonProperty("w")
    private String locales;

    @JsonProperty("x")
    private Integer sort;

    @JsonProperty("y")
    private String extraDescription;

    @JsonProperty("z")
    private String extraNameDesc;

    @JsonProperty("a1")
    private String displayPos;

    @JsonProperty("b1")
    private Integer mergeId;


}
