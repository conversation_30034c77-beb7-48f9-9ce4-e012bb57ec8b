package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class MsgAllianceinfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Integer iD;

    @JsonProperty("b")
    private Long allianceID;

    @JsonProperty("c")
    private String allianceName;

    @JsonProperty("d")
    private Integer scene;

    @JsonProperty("e")
    private Boolean anonymous;

    @JsonProperty("f")
    private Timestamp datachangeCreatetime;

    @JsonProperty("g")
    private Timestamp datachangeLasttime;
}
