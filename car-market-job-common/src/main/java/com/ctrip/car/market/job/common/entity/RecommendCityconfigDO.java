package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class RecommendCityconfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    /**
     * 城市名称
     */
    @JsonProperty("b")
    private String cityName;

    /**
     * 城市ID
     */
    @JsonProperty("c")
    private Integer cityId;

    /**
     * 产品类型名称
     */
    @JsonProperty("d")
    private String productCategoryName;

    /**
     * 产品类型Id32-国内代驾 33-海外代驾 34-海外自驾 35-国内自驾 37-增值服务 38-P2P
     */
    @JsonProperty("e")
    private Integer productCategoryId;

    /**
     * 页面形式0国内租车列表页1海外租车列表页2聚合页面
     */
    @JsonProperty("f")
    private Integer pageType;

    /**
     * 携程首页名称
     */
    @JsonProperty("g")
    private String pageShowName;

    /**
     * 展示优先级
     */
    @JsonProperty("h")
    private Integer pageShowIndex;

    /**
     * 国家ID
     */
    @JsonProperty("i")
    private Integer countryId;

    /**
     * 国家名称
     */
    @JsonProperty("j")
    private String countryName;

    /**
     * 洲ID
     */
    @JsonProperty("k")
    private Integer continentId;

    /**
     * 洲名称
     */
    @JsonProperty("l")
    private String continentName;

    /**
     * 营销自己的标签,逗号分隔
     */
    @JsonProperty("m")
    private String showLabels;

    /**
     * h5跳转连接，如果产线没有的话，才用这个默认的
     */
    @JsonProperty("n")
    private String h5JumpUrl;

    /**
     * App跳转链接
     */
    @JsonProperty("o")
    private String appJumpUrl;

    /**
     * 创建时间
     */
    @JsonProperty("p")
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @JsonProperty("q")
    private Timestamp datachangeLasttime;

    /**
     * 优先级产线分类
     */
    @JsonProperty("r")
    private Integer pageProductId;

    /**
     * 携程首页名称
     */
    @JsonProperty("s")
    private String pageHomeTitle;

    /**
     * 一条街数据版本
     */
    @JsonProperty("t")
    private Integer versionId;
}
