package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class ChannelNumberDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    /**
     * 渠道号名称
     */
    @JsonProperty("b")
    private String channelName;

    /**
     * 第三级渠道id
     */
    @JsonProperty("c")
    private Long tertiaryChannelId;

    /**
     * 合作BU，关联dictionary，dict_type=cooperation的
     */
    @JsonProperty("d")
    private String cooperationId;

    /**
     * 合作BU名称
     */
    @JsonProperty("e")
    private String cooperationName;

    /**
     * 合作方式API对接、页面嵌入、倒流，关联dictionary，
     */
    @JsonProperty("f")
    private String cooperationModeId;

    /**
     * 合作方式名称
     */
    @JsonProperty("g")
    private String cooperationModeName;

    /**
     * 页面端app、h5、online、小程序，关联dictionary，dict_type=page_location的dict_key字段
     */
    @JsonProperty("h")
    private String pageLocationId;

    /**
     * 页面端名称
     */
    @JsonProperty("i")
    private String pageLocationName;

    /**
     * 市场信息id
     */
    @JsonProperty("j")
    private Long channelMarketId;

    /**
     * 备注
     */
    @JsonProperty("k")
    private String remark;

    /**
     * 操作人id
     */
    @JsonProperty("l")
    private String operatorId;

    /**
     * 操作人
     */
    @JsonProperty("m")
    private String operator;

    /**
     * 创建时间
     */
    @JsonProperty("n")
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @JsonProperty("o")
    private Timestamp datachangeLasttime;
}
