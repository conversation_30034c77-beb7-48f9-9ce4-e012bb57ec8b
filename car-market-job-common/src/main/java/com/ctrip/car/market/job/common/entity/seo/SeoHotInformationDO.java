package com.ctrip.car.market.job.common.entity.seo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class SeoHotInformationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    @JsonProperty("b")
    private Integer poiType;

    @JsonProperty("c")
    private String poiCode;

    @JsonProperty("d")
    private Integer countryId;

    @JsonProperty("e")
    private Integer cityId;

    @JsonProperty("f")
    private Integer vendorId;

    @JsonProperty("g")
    private String vendorName;

    @JsonProperty("h")
    private Integer vehicleGroupId;

    @JsonProperty("i")
    private String vehicleGroupName;

    @JsonProperty("j")
    private Integer tenancy;

    @JsonProperty("k")
    private Integer status;

    @JsonProperty("l")
    private Timestamp datachangeCreatetime;

    @JsonProperty("m")
    private Timestamp datachangeLasttime;
}
