package com.ctrip.car.market.job.common;

public class CommonResult<T> {

    private boolean success;
    private String code;
    private String msg;
    private T data;
    private String errorType;
    public static <T> CommonResult<T> success(T data) {
        CommonResult<T> stepResult = new CommonResult<>();
        stepResult.success = true;
        stepResult.data = data;
        return stepResult;
    }

    public static <T> CommonResult<T> failed(String msg) {
        CommonResult<T> stepResult = new CommonResult<>();
        stepResult.success = false;
        stepResult.msg = msg;
        return stepResult;
    }

    public static <T> CommonResult<T> failed(String code, String msg) {
        CommonResult<T> stepResult = new CommonResult<>();
        stepResult.success = false;
        stepResult.code = code;
        stepResult.msg = msg;
        return stepResult;
    }

    public static <T> CommonResult<T> failed(String errorType, String code, String msg) {
        CommonResult<T> stepResult = new CommonResult<>();
        stepResult.success = false;
        stepResult.errorType = errorType;
        stepResult.code = code;
        stepResult.msg = msg;
        return stepResult;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }
}
