package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class CpnLabelGroupDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    @JsonProperty("b")
    private Integer category;

    @JsonProperty("c")
    private Integer subCategory;

    @JsonProperty("d")
    private String subCategoryDesc;

    @JsonProperty("e")
    private Integer marketGroup;

    @JsonProperty("f")
    private String marketGroupDesc;

    @JsonProperty("g")
    private Timestamp datachangeLasttime;

    @JsonProperty("h")
    private Boolean showQuickPickInList;

    @JsonProperty("i")
    private Boolean showQuickNoticeInList;

    @JsonProperty("j")
    private String quickNoticeIcon;

    @JsonProperty("k")
    private Integer showPriority;

    @JsonProperty("l")
    private String noticeContent;
}
