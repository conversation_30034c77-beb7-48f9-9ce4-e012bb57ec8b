package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class ActReturnCityInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    @JsonProperty("b")
    private Long activityId;

    @JsonProperty("c")
    private Integer cityId;

    @JsonProperty("d")
    private Boolean isActive;

    @JsonProperty("e")
    private Timestamp dataChangeCreateTime;

    @JsonProperty("f")
    private Timestamp datachangeLasttime;
}
