package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Setter
@Getter
public class UnionEnforcementconfigurationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long eFID;

    @JsonProperty("b")
    private String pageId;

    @JsonProperty("c")
    private Timestamp firstDateTime;

    @JsonProperty("d")
    private Timestamp lastDateTime;

    @JsonProperty("e")
    private Integer validStatu;

    @JsonProperty("f")
    private String content;

    @JsonProperty("g")
    private String modifyUser;

    @JsonProperty("h")
    private String remark;

    @JsonProperty("i")
    private Timestamp datachangeCreatetime;

    @JsonProperty("j")
    private Timestamp datachangeLasttime;

    @JsonProperty("k")
    private Integer priorityLevels;
}
