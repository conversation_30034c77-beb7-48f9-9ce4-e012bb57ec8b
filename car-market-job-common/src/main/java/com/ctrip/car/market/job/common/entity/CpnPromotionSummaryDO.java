package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Getter
@Setter
public class CpnPromotionSummaryDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long summaryID;

    @JsonProperty("b")
    private String activityName;

    @JsonProperty("c")
    private Integer projectID;

    @JsonProperty("d")
    private String projectName;

    @JsonProperty("e")
    private Integer channel;

    @JsonProperty("f")
    private String channelName;

    @JsonProperty("g")
    private String channelType;

    @JsonProperty("h")
    private Integer costType;

    @JsonProperty("i")
    private BigDecimal sharePercent;

    @JsonProperty("j")
    private String shareOther;

    @JsonProperty("k")
    private String modifyUser;

    @JsonProperty("l")
    private Integer isValid;

    @JsonProperty("m")
    private Timestamp datachangeCreatetime;

    @JsonProperty("n")
    private Timestamp datachangeLasttime;

    @JsonProperty("o")
    private Integer activityID;
}
