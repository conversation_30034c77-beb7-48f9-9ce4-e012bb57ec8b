package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Getter
@Setter
public class CarSaleRankDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    /**
     * 车型名称
     */
    @JsonProperty("b")
    private String vehicleName;

    /**
     * 车型组名称
     */
    @JsonProperty("c")
    private String vehicleGroupName;

    /**
     * 车型id
     */
    @JsonProperty("d")
    private Integer vehicleId;

    /**
     * 车辆排挡：0手动挡 1自动挡
     */
    @JsonProperty("e")
    private Integer trans;

    /**
     * 排量
     */
    @JsonProperty("f")
    private String displacement;

    /**
     * 座位数
     */
    @JsonProperty("g")
    private Integer seat;

    /**
     * 车门数
     */
    @JsonProperty("h")
    private Integer doorNo;

    /**
     * 三厢
     */
    @JsonProperty("i")
    private String carriageDesc;

    /**
     * 所有城市的排名
     */
    @JsonProperty("j")
    private Integer vehicleRank;

    /**
     * 城市id
     */
    @JsonProperty("k")
    private Integer cityId;

    /**
     * 车型图片链接
     */
    @JsonProperty("l")
    private String vehicleImageUrl;

    /**
     * 是否有效
     */
    @JsonProperty("m")
    private Boolean active;

    /**
     * 创建时间
     */
    @JsonProperty("n")
    private Timestamp datachageCreatetime;

    /**
     * 更新时间
     */
    @JsonProperty("o")
    private Timestamp datachangeLasttime;

    /**
     * 服务标签，英文逗号分隔，例：押金双免,免费取消
     */
    @JsonProperty("p")
    private String serviceLabels;

    /**
     * 城市内销量最好的供应商id
     */
    @JsonProperty("q")
    private Integer vendorId;

    /**
     * 城市内的最低价
     */
    @JsonProperty("r")
    private BigDecimal floorPrice;

    /**
     * 是否卡拉比数据源
     */
    @JsonProperty("s")
    private Boolean isCalabi;
}
