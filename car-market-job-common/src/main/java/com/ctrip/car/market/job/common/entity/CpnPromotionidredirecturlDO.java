package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class CpnPromotionidredirecturlDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long iD;

    @JsonProperty("b")
    private Long promotionId;

    @JsonProperty("c")
    private String showName;

    @JsonProperty("d")
    private String shortDesc;

    @JsonProperty("e")
    private String pcJumpUrl;

    @JsonProperty("f")
    private String h5JumpUrl;

    @JsonProperty("g")
    private String appJumpUrl;

    @JsonProperty("h")
    private String rnJumpUrl;

    @JsonProperty("i")
    private String otherJumpContent;

    @JsonProperty("j")
    private Timestamp datachangeCreatetime;

    @JsonProperty("k")
    private Timestamp datachangeLasttime;

    @JsonProperty("l")
    private String createor;

    @JsonProperty("m")
    private String modifer;

    @JsonProperty("n")
    private String offlineRoleCode;

    @JsonProperty("o")
    private Integer isValid;

}
