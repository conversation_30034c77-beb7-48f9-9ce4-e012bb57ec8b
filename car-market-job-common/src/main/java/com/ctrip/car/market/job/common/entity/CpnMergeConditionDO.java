package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class CpnMergeConditionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long iD;

    @JsonProperty("b")
    private String displayName;

    @JsonProperty("c")
    private String channelIds;

    @JsonProperty("d")
    private String remark;

    @JsonProperty("e")
    private Timestamp startDate;

    @JsonProperty("f")
    private Timestamp endDate;

    @JsonProperty("g")
    private Integer active;

    @JsonProperty("h")
    private Timestamp datachangeCreatetime;

    @JsonProperty("i")
    private String operator;

    @JsonProperty("j")
    private String categorycodes;

    @JsonProperty("k")
    private String channelGroups;

    @JsonProperty("l")
    private String excludeChannelIds;

    @JsonProperty("m")
    private Timestamp datachangeLasttime;

    @JsonProperty("n")
    private String conent;

    @JsonProperty("o")
    private String area;
}
