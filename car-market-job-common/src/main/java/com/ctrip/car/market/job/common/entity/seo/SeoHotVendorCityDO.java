package com.ctrip.car.market.job.common.entity.seo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class SeoHotVendorCityDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    @JsonProperty("b")
    private String vendorId;

    @JsonProperty("c")
    private String vendorName;

    @JsonProperty("d")
    private Integer cityId;

    @JsonProperty("e")
    private String cityName;

    @JsonProperty("f")
    private String url;

    @JsonProperty("g")
    private Integer status;

    @JsonProperty("h")
    private Timestamp datachangeCreatetime;

    @JsonProperty("i")
    private Timestamp datachangeLasttime;
}
