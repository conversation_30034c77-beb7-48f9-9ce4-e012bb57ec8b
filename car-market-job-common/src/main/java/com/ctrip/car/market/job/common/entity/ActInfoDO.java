package com.ctrip.car.market.job.common.entity;

import com.ctrip.car.market.common.entity.act.CustomContent;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class ActInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    @JsonProperty("b")
    private Long tempId;

    @JsonProperty("c")
    private Integer vendorType;

    @JsonProperty("d")
    private Long vendorId;

    @JsonProperty("e")
    private Timestamp startTime;

    @JsonProperty("f")
    private Timestamp endTime;

    @JsonProperty("g")
    private String vehicleGroupIds;

    @JsonProperty("h")
    private String vendorCouponCode;

    @JsonProperty("i")
    private Integer status;

    @JsonProperty("j")
    private Timestamp datachangeCreateTime;

    @JsonProperty("k")
    private Timestamp datachangeLasttime;

    @JsonProperty("l")
    private String createUser;

    @JsonProperty("m")
    private String modifyUser;

    @JsonProperty("n")
    private Boolean excludeCity;

    @JsonProperty("o")
    private Boolean excludeReturnCity;

    @JsonProperty("p")
    private Boolean excludeStore;

    @JsonProperty("q")
    private Boolean excludeSku;

    @JsonProperty("r")
    private Boolean excludeProduct;

    @JsonProperty("s")
    private String customContent;

    @JsonProperty("t")
    private CustomContent content;

    @JsonProperty("u")
    private Integer signUpMode;
}
