package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class CpnUnionPromotionLimitDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Integer limitId;

    @JsonProperty("b")
    private Integer promotionId;

    @JsonProperty("c")
    private String promotionName;

    @JsonProperty("d")
    private String promotionDisplayName;

    @JsonProperty("e")
    private Timestamp startTime;

    @JsonProperty("f")
    private Timestamp endTime;

    @JsonProperty("g")
    private Integer department;

    @JsonProperty("h")
    private String context;

    @JsonProperty("i")
    private Integer valid;

    @JsonProperty("j")
    private String remark;

    @JsonProperty("k")
    private String modifyUser;

    @JsonProperty("l")
    private Timestamp datachangeLasttime;
}
