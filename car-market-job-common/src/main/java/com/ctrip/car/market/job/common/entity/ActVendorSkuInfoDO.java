package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ActVendorSkuInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long id;

    @JsonProperty("b")
    private Long vendorId;

    @JsonProperty("c")
    private Long skuId;

    @JsonProperty("d")
    private Long storeId;

    @JsonProperty("e")
    private Long standardProductId;
}
