package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class CpnGroupVersionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Integer gvid;

    @JsonProperty("b")
    private Integer groupId;

    @JsonProperty("c")
    private Long groupConditionId;

    @JsonProperty("d")
    private String modifyUser;

    @JsonProperty("e")
    private Boolean isValid;

    @JsonProperty("f")
    private Timestamp datachangeCreatetime;

    @JsonProperty("g")
    private Timestamp datachangeLasttime;

    @JsonProperty("h")
    private Integer urlTempId;

    @JsonProperty("i")
    private Integer version;

    @JsonProperty("j")
    private Integer feConfigId;

    @JsonProperty("k")
    private Integer unionType;
}
