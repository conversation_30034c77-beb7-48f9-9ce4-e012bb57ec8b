package com.ctrip.car.market.job.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
public class CpnProductRedirectUrlDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("a")
    private Long urlID;

    @JsonProperty("b")
    private Integer businessType;

    @JsonProperty("c")
    private Integer businessSubType;

    @JsonProperty("d")
    private String redirectUrl;

    @JsonProperty("e")
    private Integer isValid;

    @JsonProperty("f")
    private Integer urlType;

    @JsonProperty("g")
    private String urlName;

    @JsonProperty("h")
    private String remark;

    @JsonProperty("i")
    private Timestamp datachangeCreatetime;

    @JsonProperty("j")
    private String discribe;

    @JsonProperty("k")
    private String unionType;

    @JsonProperty("l")
    private Timestamp datachangeLasttime;

    @JsonProperty("m")
    private String jumpMiniAppId;
}
