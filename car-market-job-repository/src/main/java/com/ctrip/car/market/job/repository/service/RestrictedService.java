package com.ctrip.car.market.job.repository.service;

import com.ctrip.car.market.job.repository.entity.*;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.base.SQLResult;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RestrictedService {

    private final DalTableOperations<CpnRestrictedPromotion> restrictedOperations = DalOperationsFactory.getDalTableOperations(CpnRestrictedPromotion.class);
    private final DalTableOperations<CpnRestrictedCondition> restrictedConditionOperations = DalOperationsFactory.getDalTableOperations(CpnRestrictedCondition.class);
    private final DalTableOperations<CpnGroupRcCondition> groupRcConditionOperations = DalOperationsFactory.getDalTableOperations(CpnGroupRcCondition.class);
    private final DalTableOperations<CpnGroupVersion> groupVersionOperations = DalOperationsFactory.getDalTableOperations(CpnGroupVersion.class);
    private final DalTableOperations<CpnPromotionidredirecturl> promotionRedirectUrlOperations = DalOperationsFactory.getDalTableOperations(CpnPromotionidredirecturl.class);
    private final DalTableOperations<CpnPromotionOrder> promotionOrderOperations = DalOperationsFactory.getDalTableOperations(CpnPromotionOrder.class);
    private final DalTableOperations<CpnUnionPromotionLimit> unionPromotionLimitOperations = DalOperationsFactory.getDalTableOperations(CpnUnionPromotionLimit.class);
    private final DalTableOperations<GiftItemconfig> gifItemConfigOperations = DalOperationsFactory.getDalTableOperations(GiftItemconfig.class);
    private final DalTableOperations<UnionEnforcementconfiguration> unionEnforcementConfigurationOperations = DalOperationsFactory.getDalTableOperations(UnionEnforcementconfiguration.class);
    private final DalTableOperations<CpnProductRedirectUrl> productRedirectUrlOperations = DalOperationsFactory.getDalTableOperations(CpnProductRedirectUrl.class);
    private final DalTableOperations<CpnMergeCondition> mergeConditionOperations = DalOperationsFactory.getDalTableOperations(CpnMergeCondition.class);
    private final DalTableOperations<CpnPromotionSummary> promotionSummaryOperations = DalOperationsFactory.getDalTableOperations(CpnPromotionSummary.class);


    public List<CpnRestrictedPromotion> queryAllRestrictedPromotion() throws Exception {
        List<CpnRestrictedPromotion> result = Lists.newArrayList();
        int promotionId = 0;
        List<CpnRestrictedPromotion> temp;
        do {
            temp = restrictedOperations.query("select * from cpn_restricted_promotion where promotionId>? order by promotionId limit 5000;", new DalHints(), promotionId);
            if (CollectionUtils.isNotEmpty(temp)) {
                promotionId = temp.stream().mapToInt(CpnRestrictedPromotion::getPromotionId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<CpnRestrictedCondition> queryAllRestrictedCondition() throws Exception {
        List<CpnRestrictedCondition> result = Lists.newArrayList();
        int conditionId = 0;
        List<CpnRestrictedCondition> temp;
        do {
            temp = restrictedConditionOperations.query("select * from cpn_restricted_condition where isValid=1 and conditionId>? order by conditionId limit 5000;", new DalHints(), conditionId);
            if (CollectionUtils.isNotEmpty(temp)) {
                conditionId = temp.stream().mapToInt(CpnRestrictedCondition::getConditionId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<CpnGroupRcCondition> queryAllGroupRcCondition() throws Exception {
        List<CpnGroupRcCondition> result = Lists.newArrayList();
        int groupId = 0;
        List<CpnGroupRcCondition> temp;
        do {
            temp = groupRcConditionOperations.query("select * from cpn_group_rccondition where groupID>? order by groupID limit 5000;", new DalHints(), groupId);
            if (CollectionUtils.isNotEmpty(temp)) {
                groupId = temp.stream().mapToInt(CpnGroupRcCondition::getGroupID).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<CpnGroupVersion> queryAllGroupVersion() throws Exception {
        List<CpnGroupVersion> result = Lists.newArrayList();
        int gvid = 0;
        List<CpnGroupVersion> temp;
        do {
            temp = groupVersionOperations.query("select * from cpn_group_version where isValid=1 and gvid>? order by gvid limit 5000;", new DalHints(), gvid);
            if (CollectionUtils.isNotEmpty(temp)) {
                gvid = temp.stream().mapToInt(CpnGroupVersion::getGvid).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<CpnPromotionidredirecturl> queryAllPromotionRedirectUrl() throws Exception {
        return promotionRedirectUrlOperations.query("select * from cpn_promotionidredirecturl where isValid=1", new DalHints(), Maps.newHashMap());
    }

    public List<CpnPromotionOrder> queryAllPromotionOrder() throws Exception {
        return promotionOrderOperations.query("select * from cpn_promotion_order where status=1 and startTime<= now() and endTime >= now()", new DalHints(), Maps.newHashMap());
    }

    public List<CpnUnionPromotionLimit> queryAllUnionPromotionLimit() throws Exception {
        return unionPromotionLimitOperations.query("select * from cpn_union_promotion_limit where valid=1", new DalHints(), Maps.newHashMap());
    }

    public List<GiftItemconfig> queryAllGifItemConfig() throws Exception {
        return gifItemConfigOperations.query("select * from gift_itemconfig", new DalHints(), Maps.newHashMap());
    }

    public List<UnionEnforcementconfiguration> queryAllUnionEnforcementConfiguration() throws Exception {
        return unionEnforcementConfigurationOperations.query("select * from union_enforcementconfiguration where ValidStatu=1", new DalHints(), Maps.newHashMap());
    }

    public List<CpnProductRedirectUrl> queryAllProductRedirectUrl() throws Exception {
        return productRedirectUrlOperations.query("select * from cpn_productredirecturl where IsValid=1;", new DalHints(), Maps.newHashMap());
    }

    public List<CpnMergeCondition> queryAllMergeCondition() throws Exception {
        return mergeConditionOperations.query("SELECT * FROM cpn_merge_condition WHERE `active` = 1 AND startDate < NOW() AND endDate > NOW()", new DalHints(), Maps.newHashMap());
    }

    public List<CpnPromotionSummary> queryAllPromotionSummary() throws Exception {
        List<CpnPromotionSummary> result = Lists.newArrayList();
        long summerId = 0;
        List<CpnPromotionSummary> temp;
        do {
            temp = promotionSummaryOperations.query("select * from Cpn_PromotionSummary where IsValid=1 and SummaryID>? order by SummaryID limit 5000;", new DalHints(), summerId);
            if (CollectionUtils.isNotEmpty(temp)) {
                summerId = temp.stream().mapToLong(CpnPromotionSummary::getSummaryID).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public CpnRestrictedPromotion queryRestrictedPromotion(Integer promotionId) throws Exception {
        return restrictedOperations.queryByPk(promotionId, new DalHints());
    }

    public List<CpnRestrictedPromotion> queryRestrictedPromotionBySummary(Long summaryId) throws Exception {
        return restrictedOperations.query("select * from cpn_restricted_promotion where summaryId=?", new DalHints(), summaryId);
    }

    public CpnRestrictedCondition queryRestrictedCondition(Integer conditionId) throws Exception {
        return restrictedConditionOperations.queryByPk(conditionId, new DalHints());
    }

    public List<CpnRestrictedCondition> queryRestrictedConditionByPromotionId(Integer promotionId) throws Exception {
        return restrictedConditionOperations.query("select * from cpn_restricted_condition where promotionId=? and isValid=1", new DalHints(), promotionId);
    }

    public List<CpnRestrictedCondition> queryRestrictedConditionByGroupId(Integer groupId) throws Exception {
        return restrictedConditionOperations.query("select * from cpn_restricted_condition where groupId=? and isValid=1", new DalHints(), groupId);
    }

    public CpnGroupRcCondition queryGroupRcCondition(Integer groupId) throws Exception {
        return groupRcConditionOperations.queryByPk(groupId, new DalHints());
    }

    public CpnGroupVersion queryGroupVersion(Integer gvid) throws Exception {
        return groupVersionOperations.queryByPk(gvid, new DalHints());
    }

    public List<CpnGroupVersion> queryGroupVersionByGroupId(Integer groupId) throws Exception {
        return groupVersionOperations.query("select * from cpn_group_version where groupId=? and isValid=1", new DalHints(), groupId);
    }

    public CpnPromotionidredirecturl queryPromotionRedirectUrl(Long id) throws Exception {
        return promotionRedirectUrlOperations.queryByPk(id, new DalHints());
    }

    public CpnPromotionidredirecturl queryPromotionRedirectUrlByPromotionId(Long id) throws Exception {
        return promotionRedirectUrlOperations.queryFirst("select * from cpn_promotionidredirecturl where promotionId=? and isValid=1", new DalHints(), SQLResult.type(CpnPromotionidredirecturl.class), id);
    }

    public CpnPromotionOrder queryPromotionOrder(Long id) throws Exception {
        return promotionOrderOperations.queryByPk(id, new DalHints());
    }

    public CpnUnionPromotionLimit queryUnionPromotionLimit(Integer limitId) throws Exception {
        return unionPromotionLimitOperations.queryByPk(limitId, new DalHints());
    }

    public List<CpnUnionPromotionLimit> queryUnionPromotionLimitByPromotionId(Integer promotionId) throws Exception {
        return unionPromotionLimitOperations.query("select * From cpn_union_promotion_limit where promotionId=? and valid=1", new DalHints(), promotionId);
    }

    public GiftItemconfig queryGifItemConfig(Long itemId) throws Exception {
        return gifItemConfigOperations.queryByPk(itemId, new DalHints());
    }

    public List<GiftItemconfig> queryGifItemConfigByNo(String gifNo) throws Exception {
        return gifItemConfigOperations.query("select * from gift_itemconfig where giftNo = ?", new DalHints(), gifNo);
    }

    public UnionEnforcementconfiguration queryUnionEnforcementConfiguration(Long eFID) throws Exception {
        return unionEnforcementConfigurationOperations.queryByPk(eFID, new DalHints());
    }

    public CpnProductRedirectUrl queryProductRedirectUrl(Long urlId) throws Exception {
        return productRedirectUrlOperations.queryByPk(urlId, new DalHints());
    }

    public List<CpnProductRedirectUrl> queryProductRedirectUrlByUnionType(String unionType) throws Exception {
        return productRedirectUrlOperations.query("select* from cpn_productredirecturl where UnionType=? and IsValid=1", new DalHints(), unionType);
    }

    public CpnMergeCondition queryMergeCondition(Long id) throws Exception {
        return mergeConditionOperations.queryByPk(id, new DalHints());
    }

    public CpnPromotionSummary queryPromotionSummary(Long summaryId) throws Exception {
        return promotionSummaryOperations.queryByPk(summaryId, new DalHints());
    }

    public List<CpnPromotionSummary> queryPromotionSummaryByActivityId(Integer activityId) throws Exception {
        return promotionSummaryOperations.query("select * from cpn_promotionsummary where ActivityID = ? and IsValid=1", new DalHints(), activityId);
    }

    public List<CpnRestrictedPromotion> queryTxTravel() throws Exception {
        return restrictedOperations.query("select * from cpn_restricted_promotion where cooperativeDepartment=100 and disableDate>DATE_SUB(now(),INTERVAL 30 day);",
                new DalHints(),
                SQLResult.type(CpnRestrictedPromotion.class));
    }
}
