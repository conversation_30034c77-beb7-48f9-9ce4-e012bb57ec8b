package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "union_enforcementconfiguration")
public class UnionEnforcementconfiguration implements DalPojo {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "EFID")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long eFID;

    /**
     * pageID
     */
    @Column(name = "PageId")
    @Type(value = Types.VARCHAR)
    private String pageId;

    /**
     * 生效日期
     */
    @Column(name = "FirstDateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp firstDateTime;

    /**
     * 失效日期
     */
    @Column(name = "LastDateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp lastDateTime;

    /**
     * 0已创建，1已启动，2已禁用
     */
    @Column(name = "ValidStatu")
    @Type(value = Types.TINYINT)
    private Integer validStatu;

    /**
     * 内容
     */
    @Column(name = "Content")
    @Type(value = Types.VARCHAR)
    private String content;

    /**
     * 修改人
     */
    @Column(name = "ModifyUser")
    @Type(value = Types.VARCHAR)
    private String modifyUser;

    /**
     * 备注
     */
    @Column(name = "Remark")
    @Type(value = Types.VARCHAR)
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 修改时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 优先级
     */
    @Column(name = "PriorityLevels")
    @Type(value = Types.TINYINT)
    private Integer priorityLevels;

    public Long getEFID() {
        return eFID;
    }

    public void setEFID(Long eFID) {
        this.eFID = eFID;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public Timestamp getFirstDateTime() {
        return firstDateTime;
    }

    public void setFirstDateTime(Timestamp firstDateTime) {
        this.firstDateTime = firstDateTime;
    }

    public Timestamp getLastDateTime() {
        return lastDateTime;
    }

    public void setLastDateTime(Timestamp lastDateTime) {
        this.lastDateTime = lastDateTime;
    }

    public Integer getValidStatu() {
        return validStatu;
    }

    public void setValidStatu(Integer validStatu) {
        this.validStatu = validStatu;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Integer getPriorityLevels() {
        return priorityLevels;
    }

    public void setPriorityLevels(Integer priorityLevels) {
        this.priorityLevels = priorityLevels;
    }

}