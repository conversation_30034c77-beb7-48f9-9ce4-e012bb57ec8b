package com.ctrip.car.market.job.repository.dao;



import com.ctrip.car.market.job.repository.entity.SeoHotVendorCity;
import com.ctrip.platform.dal.dao.*;

import com.ctrip.platform.dal.common.enums.DatabaseCategory;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.base.SQLResultSpec;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-16
 * @API DOC: http://pages.release.ctripcorp.com/framework/dal-client-trip/#/3/3.2/3.2
 */
@Repository
public class SeoHotVendorCityDao {
    private final DalTableOperations<SeoHotVendorCity> DAL_TABLE_OPERATIONS = DalOperationsFactory.getDalTableOperations(SeoHotVendorCity.class);

    /**
     * Get a DalClient to access the database. It maybe your customDalClient or a dal internal Client
     *
     * @return DalClient
     */
    public DalClient getClient() {
        return DAL_TABLE_OPERATIONS.getClient();
    }

    /**
     * Get the category of the Database.
     *
     * @return DatabaseCategory
     */
    public DatabaseCategory getDatabaseCategory() {
        return DAL_TABLE_OPERATIONS.getDatabaseCategory();
    }

    public List<SeoHotVendorCity> query(String sql, DalHints dalHints, Object... args) throws SQLException {
        return DAL_TABLE_OPERATIONS.query(sql, dalHints, args);
    }

    public <K> List<K> query(String sql, DalHints dalHints, SQLResultSpec result, Object... args) throws SQLException {
        return DAL_TABLE_OPERATIONS.query(sql, dalHints, result, args);
    }

    public SeoHotVendorCity queryByPk(Number id, DalHints hints) throws SQLException {
        return DAL_TABLE_OPERATIONS.queryByPk(id, hints);
    }

    public <K> List<K> queryBy(SeoHotVendorCity sample, DalHints hints, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.queryBy(sample, hints, result);
    }

    public int[] batchInsert(DalHints hints, List<SeoHotVendorCity> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.batchInsert(hints, daoPojos);
    }

    public int[] batchUpdate(DalHints hints, List<SeoHotVendorCity> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.batchUpdate(hints, daoPojos);
    }
}
