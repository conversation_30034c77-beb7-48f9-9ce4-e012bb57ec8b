package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_promotionsummary")
public class CpnPromotionSummary implements DalPojo {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "SummaryID")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long summaryID;

    /**
     * 活动名称
     */
    @Column(name = "ActivityName")
    @Type(value = Types.VARCHAR)
    private String activityName;

    /**
     * 项目ID
     */
    @Column(name = "ProjectID")
    @Type(value = Types.INTEGER)
    private Integer projectID;

    /**
     * 项目名称
     */
    @Column(name = "ProjectName")
    @Type(value = Types.VARCHAR)
    private String projectName;

    /**
     * 渠道
     */
    @Column(name = "Channel")
    @Type(value = Types.INTEGER)
    private Integer channel;

    /**
     * 渠道名
     */
    @Column(name = "ChannelName")
    @Type(value = Types.VARCHAR)
    private String channelName;

    /**
     * 渠道类型/形式
     */
    @Column(name = "ChannelType")
    @Type(value = Types.VARCHAR)
    private String channelType;

    /**
     * 成本类型 0:无成本 1:利润分成(百分比) 2:其它
     */
    @Column(name = "CostType")
    @Type(value = Types.INTEGER)
    private Integer costType;

    /**
     * 分成百分比
     */
    @Column(name = "SharePercent")
    @Type(value = Types.DECIMAL)
    private BigDecimal sharePercent;

    /**
     * 其它分成
     */
    @Column(name = "ShareOther")
    @Type(value = Types.VARCHAR)
    private String shareOther;

    /**
     * 修改人
     */
    @Column(name = "ModifyUser")
    @Type(value = Types.VARCHAR)
    private String modifyUser;

    /**
     * 是否有效 0-无效 1-有效
     */
    @Column(name = "IsValid")
    @Type(value = Types.TINYINT)
    private Integer isValid;

    /**
     * 创建时间
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 活动ID
     */
    @Column(name = "ActivityID")
    @Type(value = Types.INTEGER)
    private Integer activityID;

    public Long getSummaryID() {
        return summaryID;
    }

    public void setSummaryID(Long summaryID) {
        this.summaryID = summaryID;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public Integer getProjectID() {
        return projectID;
    }

    public void setProjectID(Integer projectID) {
        this.projectID = projectID;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public Integer getCostType() {
        return costType;
    }

    public void setCostType(Integer costType) {
        this.costType = costType;
    }

    public BigDecimal getSharePercent() {
        return sharePercent;
    }

    public void setSharePercent(BigDecimal sharePercent) {
        this.sharePercent = sharePercent;
    }

    public String getShareOther() {
        return shareOther;
    }

    public void setShareOther(String shareOther) {
        this.shareOther = shareOther;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Integer getActivityID() {
        return activityID;
    }

    public void setActivityID(Integer activityID) {
        this.activityID = activityID;
    }

}
