package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2025-07-08
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "mkt_wallet_cashback_info")
public class MktWalletCashbackInfo implements DalPojo {

    /**
     * 自增主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 商户号
     */
    @Column(name = "reqMerchantId")
    @Type(value = Types.VARCHAR)
    private String reqMerchantId;

    /**
     * 携程uid
     */
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;

    /**
     * 请求流水号
     */
    @Column(name = "requestRefNo")
    @Type(value = Types.VARCHAR)
    private String requestRefNo;

    /**
     * 请求时间
     */
    @Column(name = "requestTime")
    @Type(value = Types.VARCHAR)
    private String requestTime;

    /**
     * 产品码
     */
    @Column(name = "productCode")
    @Type(value = Types.VARCHAR)
    private String productCode;

    /**
     * 转入的金额,目前默认币种为人民币，单位为分
     */
    @Column(name = "amount")
    @Type(value = Types.DECIMAL)
    private BigDecimal amount;

    /**
     * 订单编号
     */
    @Column(name = "orderId")
    @Type(value = Types.BIGINT)
    private Long orderId;

    /**
     * 订单名称
     */
    @Column(name = "orderName")
    @Type(value = Types.VARCHAR)
    private String orderName;

    /**
     * 订单类型
     */
    @Column(name = "orderType")
    @Type(value = Types.INTEGER)
    private Integer orderType;

    /**
     * 转入记录号
     */
    @Column(name = "mainPayInRecordId")
    @Type(value = Types.VARCHAR)
    private String mainPayInRecordId;

    /**
     * 0:成功 非0:见各接口具体定义
     */
    @Column(name = "resultCode")
    @Type(value = Types.INTEGER)
    private Integer resultCode;

    /**
     * 返回描述
     */
    @Column(name = "description")
    @Type(value = Types.VARCHAR)
    private String description;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 任务助力表
     */
    @Id
    @Column(name = "taskAssistId")
    @Type(value = Types.BIGINT)
    private Long taskAssistId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReqMerchantId() {
        return reqMerchantId;
    }

    public void setReqMerchantId(String reqMerchantId) {
        this.reqMerchantId = reqMerchantId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getRequestRefNo() {
        return requestRefNo;
    }

    public void setRequestRefNo(String requestRefNo) {
        this.requestRefNo = requestRefNo;
    }

    public String getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(String requestTime) {
        this.requestTime = requestTime;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getMainPayInRecordId() {
        return mainPayInRecordId;
    }

    public void setMainPayInRecordId(String mainPayInRecordId) {
        this.mainPayInRecordId = mainPayInRecordId;
    }

    public Integer getResultCode() {
        return resultCode;
    }

    public void setResultCode(Integer resultCode) {
        this.resultCode = resultCode;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getTaskAssistId() {
        return taskAssistId;
    }

    public void setTaskAssistId(Long taskAssistId) {
        this.taskAssistId = taskAssistId;
    }
}

