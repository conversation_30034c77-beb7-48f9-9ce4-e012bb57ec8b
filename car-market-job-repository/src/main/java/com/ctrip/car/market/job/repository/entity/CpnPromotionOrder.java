package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-01-25
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_promotion_order")
public class CpnPromotionOrder implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 策略ID
     */
    @Column(name = "promotionId")
    @Type(value = Types.INTEGER)
    private Integer promotionId;

    /**
     * 策略名称
     */
    @Column(name = "promotionName")
    @Type(value = Types.VARCHAR)
    private String promotionName;

    /**
     * 开始时间
     */
    @Column(name = "startTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp startTime;

    /**
     * 结束时间
     */
    @Column(name = "endTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp endTime;

    /**
     * 适用站点：0-携程 1-去哪儿
     */
    @Column(name = "useStation")
    @Type(value = Types.TINYINT)
    private Integer useStation;

    /**
     * 状态：0-未开启  1-开启
     */
    @Column(name = "status")
    @Type(value = Types.TINYINT)
    private Integer status;

    /**
     * 0：不限 1：限新客 2：限老客
     */
    @Column(name = "customerType")
    @Type(value = Types.TINYINT)
    private Integer customerType;

    /**
     * Json格式内容
     */
    @Column(name = "content")
    @Type(value = Types.VARCHAR)
    private String content;

    /**
     * 创建人
     */
    @Column(name = "createUser")
    @Type(value = Types.VARCHAR)
    private String createUser;

    /**
     * 修改人
     */
    @Column(name = "updateUser")
    @Type(value = Types.VARCHAR)
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getPromotionId() {
        return promotionId;
    }

    public void setPromotionId(Integer promotionId) {
        this.promotionId = promotionId;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public Timestamp getStartTime() {
        return startTime;
    }

    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    public Timestamp getEndTime() {
        return endTime;
    }

    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }

    public Integer getUseStation() {
        return useStation;
    }

    public void setUseStation(Integer useStation) {
        this.useStation = useStation;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
