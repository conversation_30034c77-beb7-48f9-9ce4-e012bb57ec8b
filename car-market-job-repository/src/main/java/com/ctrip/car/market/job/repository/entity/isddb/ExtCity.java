package com.ctrip.car.market.job.repository.entity.isddb;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.sql.Types;

@Entity
@Database(name = "carisddb_dalcluster")
@Table(name = "city")
public class ExtCity implements DalPojo {

    @Column(name = "CityId")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    @Column(name = "CityName")
    @Type(value = Types.VARCHAR)
    private String cityName;

    @Column(name = "ProvinceName")
    @Type(value = Types.VARCHAR)
    private String provinceName;

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

}