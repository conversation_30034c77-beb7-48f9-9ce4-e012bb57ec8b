package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "car_kalab_city")
public class CarKalabCity implements DalPojo {

    /**
     * 主键ID
     */
    @Id
	@Column(name = "Id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 城市ID
     */
	@Column(name = "cityId")
	@Type(value = Types.BIGINT)
	private Long cityId;

    /**
     * 城市名称
     */
	@Column(name = "cityName")
	@Type(value = Types.VARCHAR)
	private String cityName;

    /**
     * 省份ID
     */
	@Column(name = "provinceId")
	@Type(value = Types.BIGINT)
	private Long provinceId;

    /**
     * 省份名称
     */
	@Column(name = "provinceName")
	@Type(value = Types.VARCHAR)
	private String provinceName;

    /**
     * 国家ID
     */
	@Column(name = "countryId")
	@Type(value = Types.BIGINT)
	private Long countryId;

    /**
     * 国家名称
     */
	@Column(name = "countryName")
	@Type(value = Types.VARCHAR)
	private String countryName;

    /**
     * isd 国内，OSD海外
     */
	@Column(name = "businessType")
	@Type(value = Types.VARCHAR)
	private String businessType;

    /**
     * trip业务是否开放此类城市
     */
	@Column(name = "tripValidStatus")
	@Type(value = Types.TINYINT)
	private Integer tripValidStatus;

    /**
     * 携程是否开放此城市 0 不开放，1开发
     */
	@Column(name = "ctripValidStatus")
	@Type(value = Types.TINYINT)
	private Integer ctripValidStatus;

    /**
     * 机场三字码
     */
	@Column(name = "airportCode")
	@Type(value = Types.VARCHAR)
	private String airportCode;

    /**
     * 最低价格
     */
	@Column(name = "minPrice")
	@Type(value = Types.INTEGER)
	private Integer minPrice;

    /**
     * ctrip的 app跳转地址
     */
	@Column(name = "ctripAppUrl")
	@Type(value = Types.VARCHAR)
	private String ctripAppUrl;

    /**
     * ctrip的 H5跳转地址
     */
	@Column(name = "ctripH5Url")
	@Type(value = Types.VARCHAR)
	private String ctripH5Url;

    /**
     * 城市英文名称
     */
	@Column(name = "cityEnName")
	@Type(value = Types.VARCHAR)
	private String cityEnName;

    /**
     * 国家英文名称
     */
	@Column(name = "countryEnName")
	@Type(value = Types.VARCHAR)
	private String countryEnName;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

    /**
     * 去哪 APPurl
     */
	@Column(name = "qunarAppUrl")
	@Type(value = Types.VARCHAR)
	private String qunarAppUrl;

    /**
     * 去哪h5 url
     */
	@Column(name = "qunarH5Url")
	@Type(value = Types.VARCHAR)
	private String qunarH5Url;

    /**
     * trip PC
     */
	@Column(name = "tripPcUrl")
	@Type(value = Types.VARCHAR)
	private String tripPcUrl;

    /**
     * trp h5
     */
	@Column(name = "tripH5Url")
	@Type(value = Types.VARCHAR)
	private String tripH5Url;

    /**
     * trip app
     */
	@Column(name = "tripAppUrl")
	@Type(value = Types.VARCHAR)
	private String tripAppUrl;

	/**
	 * orderVolume
	 */
	@Column(name = "orderVolume")
	@Type(value = Types.INTEGER)
	private Integer orderVolume;

	/**
	 * 优先级（1最高；除0外，值越大优先级越小；0最小）
	 */
	@Column(name = "priority")
	@Type(value = Types.INTEGER)
	private Integer priority;

	/**
	 * TRIP 城市封面图片url
	 */
	@Column(name = "tripCityPictureUrl")
	@Type(value = Types.VARCHAR)
	private String tripCityPictureUrl;

	public String getTripCityPictureUrl() {
		return tripCityPictureUrl;
	}

	public void setTripCityPictureUrl(String tripCityPictureUrl) {
		this.tripCityPictureUrl = tripCityPictureUrl;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCityId() {
		return cityId;
	}

	public void setCityId(Long cityId) {
		this.cityId = cityId;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public Long getCountryId() {
		return countryId;
	}

	public void setCountryId(Long countryId) {
		this.countryId = countryId;
	}

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public Integer getTripValidStatus() {
		return tripValidStatus;
	}

	public void setTripValidStatus(Integer tripValidStatus) {
		this.tripValidStatus = tripValidStatus;
	}

	public Integer getCtripValidStatus() {
		return ctripValidStatus;
	}

	public void setCtripValidStatus(Integer ctripValidStatus) {
		this.ctripValidStatus = ctripValidStatus;
	}

	public String getAirportCode() {
		return airportCode;
	}

	public void setAirportCode(String airportCode) {
		this.airportCode = airportCode;
	}

	public Integer getMinPrice() {
		return minPrice;
	}

	public void setMinPrice(Integer minPrice) {
		this.minPrice = minPrice;
	}

	public String getCtripAppUrl() {
		return ctripAppUrl;
	}

	public void setCtripAppUrl(String ctripAppUrl) {
		this.ctripAppUrl = ctripAppUrl;
	}

	public String getCtripH5Url() {
		return ctripH5Url;
	}

	public void setCtripH5Url(String ctripH5Url) {
		this.ctripH5Url = ctripH5Url;
	}

	public String getCityEnName() {
		return cityEnName;
	}

	public void setCityEnName(String cityEnName) {
		this.cityEnName = cityEnName;
	}

	public String getCountryEnName() {
		return countryEnName;
	}

	public void setCountryEnName(String countryEnName) {
		this.countryEnName = countryEnName;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public String getQunarAppUrl() {
		return qunarAppUrl;
	}

	public void setQunarAppUrl(String qunarAppUrl) {
		this.qunarAppUrl = qunarAppUrl;
	}

	public String getQunarH5Url() {
		return qunarH5Url;
	}

	public void setQunarH5Url(String qunarH5Url) {
		this.qunarH5Url = qunarH5Url;
	}

	public String getTripPcUrl() {
		return tripPcUrl;
	}

	public void setTripPcUrl(String tripPcUrl) {
		this.tripPcUrl = tripPcUrl;
	}

	public String getTripH5Url() {
		return tripH5Url;
	}

	public void setTripH5Url(String tripH5Url) {
		this.tripH5Url = tripH5Url;
	}

	public String getTripAppUrl() {
		return tripAppUrl;
	}

	public void setTripAppUrl(String tripAppUrl) {
		this.tripAppUrl = tripAppUrl;
	}

	public Integer getPriority() {
		return priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	public Integer getOrderVolume() {
		return orderVolume;
	}

	public void setOrderVolume(Integer orderVolume) {
		this.orderVolume = orderVolume;
	}
}