package com.ctrip.car.market.job.repository.service;

import com.ctrip.car.market.job.repository.entity.*;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class LabelService {

    private final DalTableOperations<CpnLabel> labelOperations = DalOperationsFactory.getDalTableOperations(CpnLabel.class);
    private final DalTableOperations<CpnLabelGroup> labelGroupOperations = DalOperationsFactory.getDalTableOperations(CpnLabelGroup.class);

    private final DalTableOperations<ChannelNumber> channelNumberOperations = DalOperationsFactory.getDalTableOperations(ChannelNumber.class);

    private final DalTableOperations<RecommendCityconfig> recommendCityOperations = DalOperationsFactory.getDalTableOperations(RecommendCityconfig.class);

    public List<CpnLabel> queryAllLabel() throws Exception {
        return labelOperations.query("select * from cpn_label where IsActive=1", new DalHints(), Maps.newHashMap());
    }

    public List<CpnLabelGroup> queryAllLabelGroup() throws Exception {
        return labelGroupOperations.query("select * from cpn_label_group", new DalHints(), Maps.newHashMap());
    }

    public List<ChannelNumber> queryAllChannelNumber() throws Exception {
        return channelNumberOperations.query("select * from channel_number", new DalHints(), Maps.newHashMap());
    }

    public List<RecommendCityconfig> queryAllRecommendCityConfig() throws Exception {
        List<RecommendCityconfig> result = Lists.newArrayList();
        long id = 0;
        List<RecommendCityconfig> temp;
        do {
            temp = recommendCityOperations.query("select * from recommend_cityconfig where Id>? order by Id limit 5000;", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(RecommendCityconfig::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public CpnLabel queryLabel(Long code) throws Exception {
        return labelOperations.queryByPk(code, new DalHints());
    }
}
