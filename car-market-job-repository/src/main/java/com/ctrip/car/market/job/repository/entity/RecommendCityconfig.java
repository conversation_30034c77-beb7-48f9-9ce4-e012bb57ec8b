package com.ctrip.car.market.job.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2023-06-05
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "recommend_cityconfig")
public class RecommendCityconfig implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "Id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 城市名称
     */
    @Column(name = "cityName")
    @Type(value = Types.VARCHAR)
    private String cityName;

    /**
     * 城市ID
     */
    @Column(name = "cityId")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    /**
     * 产品类型名称
     */
    @Column(name = "productCategoryName")
    @Type(value = Types.VARCHAR)
    private String productCategoryName;

    /**
     * 产品类型Id32-国内代驾 33-海外代驾 34-海外自驾 35-国内自驾 37-增值服务 38-P2P
     */
    @Column(name = "productCategoryId")
    @Type(value = Types.TINYINT)
    private Integer productCategoryId;

    /**
     * 页面形式0国内租车列表页1海外租车列表页2聚合页面
     */
    @Column(name = "pageType")
    @Type(value = Types.TINYINT)
    private Integer pageType;

    /**
     * 携程首页名称
     */
    @Column(name = "pageShowName")
    @Type(value = Types.VARCHAR)
    private String pageShowName;

    /**
     * 展示优先级
     */
    @Column(name = "pageShowIndex")
    @Type(value = Types.TINYINT)
    private Integer pageShowIndex;

    /**
     * 国家ID
     */
    @Column(name = "countryId")
    @Type(value = Types.SMALLINT)
    private Integer countryId;

    /**
     * 国家名称
     */
    @Column(name = "countryName")
    @Type(value = Types.VARCHAR)
    private String countryName;

    /**
     * 洲ID
     */
    @Column(name = "continentId")
    @Type(value = Types.TINYINT)
    private Integer continentId;

    /**
     * 洲名称
     */
    @Column(name = "continentName")
    @Type(value = Types.VARCHAR)
    private String continentName;

    /**
     * 营销自己的标签,逗号分隔
     */
    @Column(name = "showLabels")
    @Type(value = Types.VARCHAR)
    private String showLabels;

    /**
     * h5跳转连接，如果产线没有的话，才用这个默认的
     */
    @Column(name = "h5JumpUrl")
    @Type(value = Types.VARCHAR)
    private String h5JumpUrl;

    /**
     * App跳转链接
     */
    @Column(name = "appJumpUrl")
    @Type(value = Types.VARCHAR)
    private String appJumpUrl;

    /**
     * 创建时间
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 优先级产线分类
     */
    @Column(name = "pageProductId")
    @Type(value = Types.TINYINT)
    private Integer pageProductId;

    /**
     * 携程首页名称
     */
    @Column(name = "pageHomeTitle")
    @Type(value = Types.VARCHAR)
    private String pageHomeTitle;

    /**
     * 一条街数据版本
     */
    @Column(name = "versionId")
    @Type(value = Types.TINYINT)
    private Integer versionId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getProductCategoryName() {
        return productCategoryName;
    }

    public void setProductCategoryName(String productCategoryName) {
        this.productCategoryName = productCategoryName;
    }

    public Integer getProductCategoryId() {
        return productCategoryId;
    }

    public void setProductCategoryId(Integer productCategoryId) {
        this.productCategoryId = productCategoryId;
    }

    public Integer getPageType() {
        return pageType;
    }

    public void setPageType(Integer pageType) {
        this.pageType = pageType;
    }

    public String getPageShowName() {
        return pageShowName;
    }

    public void setPageShowName(String pageShowName) {
        this.pageShowName = pageShowName;
    }

    public Integer getPageShowIndex() {
        return pageShowIndex;
    }

    public void setPageShowIndex(Integer pageShowIndex) {
        this.pageShowIndex = pageShowIndex;
    }

    public Integer getCountryId() {
        return countryId;
    }

    public void setCountryId(Integer countryId) {
        this.countryId = countryId;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public Integer getContinentId() {
        return continentId;
    }

    public void setContinentId(Integer continentId) {
        this.continentId = continentId;
    }

    public String getContinentName() {
        return continentName;
    }

    public void setContinentName(String continentName) {
        this.continentName = continentName;
    }

    public String getShowLabels() {
        return showLabels;
    }

    public void setShowLabels(String showLabels) {
        this.showLabels = showLabels;
    }

    public String getH5JumpUrl() {
        return h5JumpUrl;
    }

    public void setH5JumpUrl(String h5JumpUrl) {
        this.h5JumpUrl = h5JumpUrl;
    }

    public String getAppJumpUrl() {
        return appJumpUrl;
    }

    public void setAppJumpUrl(String appJumpUrl) {
        this.appJumpUrl = appJumpUrl;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Integer getPageProductId() {
        return pageProductId;
    }

    public void setPageProductId(Integer pageProductId) {
        this.pageProductId = pageProductId;
    }

    public String getPageHomeTitle() {
        return pageHomeTitle;
    }

    public void setPageHomeTitle(String pageHomeTitle) {
        this.pageHomeTitle = pageHomeTitle;
    }

    public Integer getVersionId() {
        return versionId;
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

}
