package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_merge_condition")
public class CpnMergeCondition implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long iD;

    /**
     * 显示名称
     */
    @Column(name = "displayName")
    @Type(value = Types.VARCHAR)
    private String displayName;

    /**
     * 渠道
     */
    @Column(name = "channelIds")
    @Type(value = Types.VARCHAR)
    private String channelIds;

    /**
     * 备注
     */
    @Column(name = "remark")
    @Type(value = Types.VARCHAR)
    private String remark;

    /**
     * 开始时间
     */
    @Column(name = "startDate")
    @Type(value = Types.TIMESTAMP)
    private Timestamp startDate;

    /**
     * 结束时间
     */
    @Column(name = "endDate")
    @Type(value = Types.TIMESTAMP)
    private Timestamp endDate;

    /**
     * 0无效，1有效
     */
    @Column(name = "active")
    @Type(value = Types.TINYINT)
    private Integer active;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 操作人
     */
    @Column(name = "operator")
    @Type(value = Types.VARCHAR)
    private String operator;


    @Column(name = "categorycodes")
    @Type(value = Types.VARCHAR)
    private String categorycodes;

    @Column(name = "channelGroups")
    @Type(value = Types.VARCHAR)
    private String channelGroups;

    /**
     * 排除渠道号
     */
    @Column(name = "excludeChannelIds")
    @Type(value = Types.VARCHAR)
    private String excludeChannelIds;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * json格式化的内容
     */
    @Column(name = "conent")
    @Type(value = Types.VARCHAR)
    private String conent;

    /**
     * 区域 0国内 1海外
     */
    @Column(name = "area")
    @Type(value = Types.VARCHAR)
    private String area;

    public Long getID() {
        return iD;
    }

    public void setID(Long iD) {
        this.iD = iD;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getChannelIds() {
        return channelIds;
    }

    public void setChannelIds(String channelIds) {
        this.channelIds = channelIds;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Timestamp getStartDate() {
        return startDate;
    }

    public void setStartDate(Timestamp startDate) {
        this.startDate = startDate;
    }

    public Timestamp getEndDate() {
        return endDate;
    }

    public void setEndDate(Timestamp endDate) {
        this.endDate = endDate;
    }

    public Integer getActive() {
        return active;
    }

    public void setActive(Integer active) {
        this.active = active;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getConent() {
        return conent;
    }

    public void setConent(String conent) {
        this.conent = conent;
    }

    public String getCategorycodes() {
        return categorycodes;
    }

    public void setCategorycodes(String categorycodes) {
        this.categorycodes = categorycodes;
    }

    public String getChannelGroups() {
        return channelGroups;
    }

    public void setChannelGroups(String channelGroups) {
        this.channelGroups = channelGroups;
    }

    public String getExcludeChannelIds() {
        return excludeChannelIds;
    }

    public void setExcludeChannelIds(String excludeChannelIds) {
        this.excludeChannelIds = excludeChannelIds;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }
}
