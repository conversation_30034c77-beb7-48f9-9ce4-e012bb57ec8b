package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_restricted_promotion")
public class CpnRestrictedPromotion implements DalPojo {

    /**
     * PromotionID-策略id
     */
    @Id
    @Column(name = "promotionId")
    @Type(value = Types.INTEGER)
    private Integer promotionId;

    /**
     * 策略名称
     */
    @Column(name = "promotionName")
    @Type(value = Types.VARCHAR)
    private String promotionName;

    /**
     * 显示名称
     */
    @Column(name = "promotionDisplayName")
    @Type(value = Types.VARCHAR)
    private String promotionDisplayName;

    /**
     * 备注
     */
    @Column(name = "remark")
    @Type(value = Types.VARCHAR)
    private String remark;

    /**
     * 创建人
     */
    @Column(name = "createUser")
    @Type(value = Types.VARCHAR)
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 修改人
     */
    @Column(name = "modifyUser")
    @Type(value = Types.VARCHAR)
    private String modifyUser;

    /**
     * 修改时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 折扣类型
     */
    @Column(name = "deductionType")
    @Type(value = Types.TINYINT)
    private Integer deductionType;

    /**
     * 金额
     */
    @Column(name = "deductionAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal deductionAmount;

    /**
     * 开始时间
     */
    @Column(name = "startDate")
    @Type(value = Types.TIMESTAMP)
    private Timestamp startDate;

    /**
     * 结束时间
     */
    @Column(name = "disableDate")
    @Type(value = Types.TIMESTAMP)
    private Timestamp disableDate;

    /**
     * CooperativeDepartment
     */
    @Column(name = "cooperativeDepartment")
    @Type(value = Types.INTEGER)
    private Integer cooperativeDepartment;

    /**
     * CooperativeDepartmentType
     */
    @Column(name = "cooperativeDepartmentType")
    @Type(value = Types.VARCHAR)
    private String cooperativeDepartmentType;

    /**
     * SummaryID
     */
    @Column(name = "summaryId")
    @Type(value = Types.BIGINT)
    private Long summaryId;

    public Integer getPromotionId() {
        return promotionId;
    }

    public void setPromotionId(Integer promotionId) {
        this.promotionId = promotionId;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public String getPromotionDisplayName() {
        return promotionDisplayName;
    }

    public void setPromotionDisplayName(String promotionDisplayName) {
        this.promotionDisplayName = promotionDisplayName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Integer getDeductionType() {
        return deductionType;
    }

    public void setDeductionType(Integer deductionType) {
        this.deductionType = deductionType;
    }

    public BigDecimal getDeductionAmount() {
        return deductionAmount;
    }

    public void setDeductionAmount(BigDecimal deductionAmount) {
        this.deductionAmount = deductionAmount;
    }

    public Timestamp getStartDate() {
        return startDate;
    }

    public void setStartDate(Timestamp startDate) {
        this.startDate = startDate;
    }

    public Timestamp getDisableDate() {
        return disableDate;
    }

    public void setDisableDate(Timestamp disableDate) {
        this.disableDate = disableDate;
    }

    public Integer getCooperativeDepartment() {
        return cooperativeDepartment;
    }

    public void setCooperativeDepartment(Integer cooperativeDepartment) {
        this.cooperativeDepartment = cooperativeDepartment;
    }

    public String getCooperativeDepartmentType() {
        return cooperativeDepartmentType;
    }

    public void setCooperativeDepartmentType(String cooperativeDepartmentType) {
        this.cooperativeDepartmentType = cooperativeDepartmentType;
    }

    public Long getSummaryId() {
        return summaryId;
    }

    public void setSummaryId(Long summaryId) {
        this.summaryId = summaryId;
    }

}
