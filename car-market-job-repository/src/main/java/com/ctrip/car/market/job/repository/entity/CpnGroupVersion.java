package com.ctrip.car.market.job.repository.entity;

import java.sql.Timestamp;
import java.sql.Types;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_group_version")
public class CpnGroupVersion implements DalPojo {

    /**
     * gvid
     */
    @Id
    @Column(name = "gvid")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.INTEGER)
    private Integer gvid;

    /**
     * GroupID
     */
    @Column(name = "groupId")
    @Type(value = Types.INTEGER)
    private Integer groupId;

    /**
     * GroupConditionID
     */
    @Column(name = "groupConditionId")
    @Type(value = Types.BIGINT)
    private Long groupConditionId;

    /**
     * 修改人
     */
    @Column(name = "modifyUser")
    @Type(value = Types.VARCHAR)
    private String modifyUser;

    /**
     * 是否有效
     */
    @Column(name = "isValid")
    @Type(value = Types.BIT)
    private Boolean isValid;

    /**
     * 创建时间
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * url模板iD
     */
    @Column(name = "urlTempId")
    @Type(value = Types.INTEGER)
    private Integer urlTempId;

    /**
     * 版本
     */
    @Column(name = "version")
    @Type(value = Types.INTEGER)
    private Integer version;

    /**
     * FEConfigID
     */
    @Column(name = "feConfigId")
    @Type(value = Types.INTEGER)
    private Integer feConfigId;

    /**
     * 试用站点
     */
    @Column(name = "unionType")
    @Type(value = Types.TINYINT)
    private Integer unionType;

    public Integer getGvid() {
        return gvid;
    }

    public void setGvid(Integer gvid) {
        this.gvid = gvid;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Long getGroupConditionId() {
        return groupConditionId;
    }

    public void setGroupConditionId(Long groupConditionId) {
        this.groupConditionId = groupConditionId;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Boolean getIsValid() {
        return isValid;
    }

    public void setIsValid(Boolean isValid) {
        this.isValid = isValid;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Integer getUrlTempId() {
        return urlTempId;
    }

    public void setUrlTempId(Integer urlTempId) {
        this.urlTempId = urlTempId;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getFeConfigId() {
        return feConfigId;
    }

    public void setFeConfigId(Integer feConfigId) {
        this.feConfigId = feConfigId;
    }

    public Integer getUnionType() {
        return unionType;
    }

    public void setUnionType(Integer unionType) {
        this.unionType = unionType;
    }
}