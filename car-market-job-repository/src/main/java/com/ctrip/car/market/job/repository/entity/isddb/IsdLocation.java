package com.ctrip.car.market.job.repository.entity.isddb;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "carisddb_dalcluster")
@Table(name = "isd_location")
public class IsdLocation implements DalPojo {

    /**
     * 自增LocationID
     */
    @Id
    @Column(name = "LocationID")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long locationID;

    /**
     * 城市ID
     */
    @Column(name = "CityID")
    @Type(value = Types.INTEGER)
    private Integer cityID;

    /**
     * 地铁线id
     */
    @Column(name = "DistrictId")
    @Type(value = Types.INTEGER)
    private Integer districtId;

    /**
     * 取车区域名
     */
    @Column(name = "LocationName")
    @Type(value = Types.VARCHAR)
    private String locationName;

    /**
     * 取车区域类型
     */
    @Column(name = "LocationType")
    @Type(value = Types.INTEGER)
    private Integer locationType;

    /**
     * 经度
     */
    @Column(name = "Longitude")
    @Type(value = Types.DECIMAL)
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @Column(name = "Latitude")
    @Type(value = Types.DECIMAL)
    private BigDecimal latitude;

    /**
     * 是否默认取车区域
     */
    @Column(name = "IsDefault")
    @Type(value = Types.BIT)
    private Boolean isDefault;

    /**
     * 是否热门
     */
    @Column(name = "IsHot")
    @Type(value = Types.BIT)
    private Boolean isHot;

    /**
     * 排序
     */
    @Column(name = "SortNum")
    @Type(value = Types.INTEGER)
    private Integer sortNum;

    /**
     * 搜索次数
     */
    @Column(name = "SearchCount")
    @Type(value = Types.INTEGER)
    private Integer searchCount;

    /**
     * 是否有效
     */
    @Column(name = "IsActive")
    @Type(value = Types.BIT)
    private Boolean isActive;

    /**
     * 最后更新时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 取车区域代码，存放唯一标识字段，机场:机场三字码,航站楼:id,火车站:三字码
     */
    @Column(name = "LocationCode")
    @Type(value = Types.VARCHAR)
    private String locationCode;

    /**
     * 是否存在门店
     */
    @Column(name = "HasStore")
    @Type(value = Types.BIT)
    private Boolean hasStore;

    /**
     * 数据来源（0：自有，1：专车 ..）
     */
    @Column(name = "SourceTo")
    @Type(value = Types.INTEGER)
    private Integer sourceTo;

    /**
     * 附近门店数量
     */
    @Column(name = "storeNum")
    @Type(value = Types.INTEGER)
    private Integer storeNum;

    public Long getLocationID() {
        return locationID;
    }

    public void setLocationID(Long locationID) {
        this.locationID = locationID;
    }

    public Integer getCityID() {
        return cityID;
    }

    public void setCityID(Integer cityID) {
        this.cityID = cityID;
    }

    public Integer getDistrictId() {
        return districtId;
    }

    public void setDistrictId(Integer districtId) {
        this.districtId = districtId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Integer getLocationType() {
        return locationType;
    }

    public void setLocationType(Integer locationType) {
        this.locationType = locationType;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Boolean getIsHot() {
        return isHot;
    }

    public void setIsHot(Boolean isHot) {
        this.isHot = isHot;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public Integer getSearchCount() {
        return searchCount;
    }

    public void setSearchCount(Integer searchCount) {
        this.searchCount = searchCount;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public Boolean getHasStore() {
        return hasStore;
    }

    public void setHasStore(Boolean hasStore) {
        this.hasStore = hasStore;
    }

    public Integer getSourceTo() {
        return sourceTo;
    }

    public void setSourceTo(Integer sourceTo) {
        this.sourceTo = sourceTo;
    }

    public Integer getStoreNum() {
        return storeNum;
    }

    public void setStoreNum(Integer storeNum) {
        this.storeNum = storeNum;
    }

}