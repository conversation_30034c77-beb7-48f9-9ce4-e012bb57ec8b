package com.ctrip.car.market.job.repository.service;

import com.ctrip.car.market.job.repository.entity.activity.*;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.base.SQLResult;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.List;

@Service
public class ActivityService {

    private final DalTableOperations<ActOsdactinfo> osdActOperations = DalOperationsFactory.getDalTableOperations(ActOsdactinfo.class);

    private final DalTableOperations<ActOsdlabel> osdActLabelOperations = DalOperationsFactory.getDalTableOperations(ActOsdlabel.class);

    private final DalTableOperations<ActCtripactinfo> actTableOperations = DalOperationsFactory.getDalTableOperations(ActCtripactinfo.class);

    private final DalTableOperations<ActCtriptempinfo> tempTableOperations = DalOperationsFactory.getDalTableOperations(ActCtriptempinfo.class);

    private final DalTableOperations<ActCityinfo> cityTableOperations = DalOperationsFactory.getDalTableOperations(ActCityinfo.class);

    private final DalTableOperations<ActReturnCityinfo> returnCityTableOperations = DalOperationsFactory.getDalTableOperations(ActReturnCityinfo.class);

    private final DalTableOperations<ActProductids> productTableOperations = DalOperationsFactory.getDalTableOperations(ActProductids.class);

    private final DalTableOperations<ActSkuinfo> skuTableOperations = DalOperationsFactory.getDalTableOperations(ActSkuinfo.class);

    private final DalTableOperations<VendorSkuinfo> vendorSkuTableOperations = DalOperationsFactory.getDalTableOperations(VendorSkuinfo.class);

    public List<ActOsdactinfo> queryAllOsdAct() throws Exception {
        return osdActOperations.query("select * from act_osdactinfo where status=1", new DalHints(), Maps.newHashMap());
    }

    public ActCtriptempinfo queryByTempId(Long tempId) throws SQLException {
        return tempTableOperations.queryByPk(tempId, new DalHints());
    }

    public List<ActCtripactinfo> queryAllActivity() throws Exception {
        List<ActCtripactinfo> result = Lists.newArrayList();
        long id = 0L;
        List<ActCtripactinfo> temp;
        do {
            temp = actTableOperations.query("select * from act_ctripactinfo where status=1 and Id>? order by Id limit 5000;", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(ActCtripactinfo::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public ActCtripactinfo queryByPk(Long activityId) throws SQLException {
        return actTableOperations.queryByPk(activityId, new DalHints());
    }

    public List<ActCtripactinfo> queryActivityByTemp(Long tempId) throws SQLException {
        return actTableOperations.query("select * from act_ctripactinfo where tempId=? and status=1;", new DalHints(), tempId);
    }

    public List<Long> queryActivityIDByTemp(Long tempId) throws SQLException {
        return actTableOperations.query("select Id from act_ctripactinfo where tempId=? and status=1;", new DalHints(), SQLResult.type(Long.class), tempId);
    }

    public List<ActCtripactinfo> queryActivityByVendor(Long vendorId) throws SQLException {
        return actTableOperations.query("select * from act_ctripactinfo where vendorId=? and status=1;", new DalHints(), vendorId);
    }

    public List<ActCtriptempinfo> queryAllTemp() throws SQLException {
        return tempTableOperations.query("select * from act_ctriptempinfo where status = 1", new DalHints(), Maps.newHashMap());
    }

    public List<ActCityinfo> queryActivityCity(Long actId) throws SQLException {
        return cityTableOperations.query("select * from act_cityinfo where activityId = ? and isactive=1", new DalHints(), actId);
    }

    public List<ActCityinfo> queryActivityCity() throws SQLException {
        List<ActCityinfo> result = Lists.newArrayList();
        long id = 0L;
        List<ActCityinfo> temp;
        do {
            temp = cityTableOperations.query("select city.* from act_cityinfo city inner join act_ctripactinfo act on act.Id=city.activityId and act.status=1 where city.id>? and city.isActive=1 order by city.id limit 5000;", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(ActCityinfo::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<ActSkuinfo> queryActivitySku() throws SQLException {
        List<ActSkuinfo> result = Lists.newArrayList();
        long id = 0L;
        List<ActSkuinfo> temp;
        do {
            temp = skuTableOperations.query("select sku.* from act_skuinfo sku inner join act_ctripactinfo act on act.Id=sku.activityId and act.status=1 where sku.id>? and sku.isActive=1 order by sku.id limit 5000;", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(ActSkuinfo::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<ActSkuinfo> queryActivitySku(Long actId) throws SQLException {
        return skuTableOperations.query("select * from act_skuinfo where activityId=? and isActive=1", new DalHints(), actId);
    }

    public List<VendorSkuinfo> queryVendorSku() throws SQLException {
        List<VendorSkuinfo> result = Lists.newArrayList();
        long id = 0L;
        List<VendorSkuinfo> temp;
        do {
            temp = vendorSkuTableOperations.query("select * from vendor_skuinfo where isActive=1 and id>? order by id limit 5000;", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(VendorSkuinfo::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<VendorSkuinfo> queryVendorSku(Long vendorId) throws SQLException {
        return vendorSkuTableOperations.query("select * from vendor_skuinfo where isActive=1 and vendorId=?;", new DalHints(), vendorId);
    }

    public List<ActReturnCityinfo> queryActivityReturnCity(Long actId) throws SQLException {
        return returnCityTableOperations.query("select * from act_returncityinfo where activityId = ? and isactive=1", new DalHints(), actId);
    }

    public List<ActReturnCityinfo> queryActivityReturnCity() throws SQLException {
        List<ActReturnCityinfo> result = Lists.newArrayList();
        long id = 0L;
        List<ActReturnCityinfo> temp;
        do {
            temp = returnCityTableOperations.query("select city.* from act_returncityinfo city inner join act_ctripactinfo act on act.Id=city.activityId and act.status=1 where city.id>? and city.isActive=1 order by city.id limit 5000;", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(ActReturnCityinfo::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<ActProductids> queryActivityProduct(Long actId) throws SQLException {
        return productTableOperations.query("select * from act_productids where activityId = ? and isactive=1", new DalHints(), actId);
    }

    public List<ActProductids> queryActivityProduct() throws Exception {
        List<ActProductids> result = Lists.newArrayList();
        long id = 0L;
        List<ActProductids> temp;
        do {
            temp = productTableOperations.query("select pro.* from act_productids pro inner join act_ctripactinfo act on act.Id=pro.activityId and act.status=1 where pro.id>? and pro.isActive=1 order by pro.Id limit 5000;", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(ActProductids::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }
}
