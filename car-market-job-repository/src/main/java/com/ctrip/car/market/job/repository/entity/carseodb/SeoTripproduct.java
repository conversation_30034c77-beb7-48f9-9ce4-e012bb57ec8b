package com.ctrip.car.market.job.repository.entity.carseodb;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.math.BigDecimal;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2024-04-05
 */
@Entity
@Database(name = "carseodb_dalcluster")
@Table(name = "seo_tripproduct")
public class SeoTripproduct implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "Id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 产品名称
     */
    @Column(name = "ProductName")
    @Type(value = Types.VARCHAR)
    private String productName;

    /**
     * 产品图片
     */
    @Column(name = "ProductImg")
    @Type(value = Types.VARCHAR)
    private String productImg;

    /**
     * 成型组名称
     */
    @Column(name = "GroupName")
    @Type(value = Types.VARCHAR)
    private String groupName;

    /**
     * 手动自动类型，1：自动，2：手动
     */
    @Column(name = "TransmissionType")
    @Type(value = Types.INTEGER)
    private Integer transmissionType;

    /**
     * 座位数
     */
    @Column(name = "Seat")
    @Type(value = Types.INTEGER)
    private Integer seat;

    /**
     * 车门数量
     */
    @Column(name = "DoorNo")
    @Type(value = Types.INTEGER)
    private Integer doorNo;

    /**
     * 行李数量
     */
    @Column(name = "LuggageNo")
    @Type(value = Types.INTEGER)
    private Integer luggageNo;

    /**
     * 最低价格，默认rmb
     */
    @Column(name = "DailyPrice")
    @Type(value = Types.DECIMAL)
    private BigDecimal dailyPrice;

    /**
     * 车型id
     */
    @Column(name = "VehicleId")
    @Type(value = Types.INTEGER)
    private Integer vehicleId;

    /**
     * H5列表页跳转链接
     */
    @Column(name = "H5ListUrl")
    @Type(value = Types.VARCHAR)
    private String h5ListUrl;

    /**
     * pc列表页跳转链接
     */
    @Column(name = "PcListUrl")
    @Type(value = Types.VARCHAR)
    private String pcListUrl;

    /**
     * 创建时间戳
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 供应商信息
     */
    @Column(name = "VendorContent")
    @Type(value = Types.VARCHAR)
    private String vendorContent;

    /**
     * 城市id
     */
    @Column(name = "CityId")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    /**
     * 客源国id
     */
    @Column(name = "SourceCountryId")
    @Type(value = Types.INTEGER)
    private Integer sourceCountryId;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 车型组英文名称
     */
    @Column(name = "GroupEname")
    @Type(value = Types.VARCHAR)
    private String groupEname;

    /**
     * 是否有效
     */
    @Column(name = "Active")
    @Type(value = Types.BIT)
    private Boolean active;

    /**
     * 车型英文名称
     */
    @Column(name = "ProductEnName")
    @Type(value = Types.VARCHAR)
    private String productEnName;

    /**
     * 品牌名称
     */
    @Column(name = "brandName")
    @Type(value = Types.VARCHAR)
    private String brandName;

    /**
     * 品牌英文名称
     */
    @Column(name = "brandEName")
    @Type(value = Types.VARCHAR)
    private String brandEName;

    /**
     * 品牌ID
     */
    @Column(name = "brandId")
    @Type(value = Types.BIGINT)
    private Long brandId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductImg() {
        return productImg;
    }

    public void setProductImg(String productImg) {
        this.productImg = productImg;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getTransmissionType() {
        return transmissionType;
    }

    public void setTransmissionType(Integer transmissionType) {
        this.transmissionType = transmissionType;
    }

    public Integer getSeat() {
        return seat;
    }

    public void setSeat(Integer seat) {
        this.seat = seat;
    }

    public Integer getDoorNo() {
        return doorNo;
    }

    public void setDoorNo(Integer doorNo) {
        this.doorNo = doorNo;
    }

    public Integer getLuggageNo() {
        return luggageNo;
    }

    public void setLuggageNo(Integer luggageNo) {
        this.luggageNo = luggageNo;
    }

    public BigDecimal getDailyPrice() {
        return dailyPrice;
    }

    public void setDailyPrice(BigDecimal dailyPrice) {
        this.dailyPrice = dailyPrice;
    }

    public Integer getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Integer vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getH5ListUrl() {
        return h5ListUrl;
    }

    public void setH5ListUrl(String h5ListUrl) {
        this.h5ListUrl = h5ListUrl;
    }

    public String getPcListUrl() {
        return pcListUrl;
    }

    public void setPcListUrl(String pcListUrl) {
        this.pcListUrl = pcListUrl;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public String getVendorContent() {
        return vendorContent;
    }

    public void setVendorContent(String vendorContent) {
        this.vendorContent = vendorContent;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getSourceCountryId() {
        return sourceCountryId;
    }

    public void setSourceCountryId(Integer sourceCountryId) {
        this.sourceCountryId = sourceCountryId;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getGroupEname() {
        return groupEname;
    }

    public void setGroupEname(String groupEname) {
        this.groupEname = groupEname;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public String getProductEnName() {
        return productEnName;
    }

    public void setProductEnName(String productEnName) {
        this.productEnName = productEnName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getBrandEName() {
        return brandEName;
    }

    public void setBrandEName(String brandEName) {
        this.brandEName = brandEName;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

}
