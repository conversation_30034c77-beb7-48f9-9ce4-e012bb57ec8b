package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @Description:
 * @create 2025/7/18 15:11
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "mkt_task_user_info")
public class MktTaskUserInfo implements DalPojo {

    /**
     * 自增主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "userId")
    @Type(value = Types.VARCHAR)
    private String userId;

    /**
     * 0:租车； 1:包车； 2:接送机
     */
    @Column(name = "productLine")
    @Type(value = Types.INTEGER)
    private Integer productLine;

    /**
     * 活动id
     */
    @Column(name = "projectId")
    @Type(value = Types.BIGINT)
    private Long projectId;

    /**
     * 任务id
     */
    @Column(name = "taskId")
    @Type(value = Types.BIGINT)
    private Long taskId;

    /**
     * 订单id
     */
    @Column(name = "orderId")
    @Type(value = Types.BIGINT)
    private Long orderId;

    /**
     * 订单状态 0：进行中 1：已完成 2：取消
     */
    @Column(name = "orderStatus")
    @Type(value = Types.INTEGER)
    private Integer orderStatus;

    /**
     * 总返现金额
     */
    @Column(name = "totalCashbackMoney")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalCashbackMoney;

    /**
     * 订单租车费
     */
    @Column(name = "orderRentalCarFee")
    @Type(value = Types.DECIMAL)
    private BigDecimal orderRentalCarFee;

    /**
     * 返现比例列表
     */
    @Column(name = "cashbackRatioList")
    @Type(value = Types.VARCHAR)
    private String cashbackRatioList;

    /**
     * 来源平台 PC/APP/H5/miniProgram, 选填
     */
    @Column(name = "platform")
    @Type(value = Types.VARCHAR)
    private String platform;

    /**
     * 任务状态{1:received,已领取;2:completed,已完成;3:awarded,已领奖}
     */
    @Column(name = "taskStatus")
    @Type(value = Types.INTEGER)
    private Integer taskStatus;

    /**
     * 用户任务领取表主键
     */
    @Column(name = "userReceivedTaskId")
    @Type(value = Types.BIGINT)
    private Long userReceivedTaskId;

    /**
     * 用户任务领取流水号
     */
    @Column(name = "serialNumber")
    @Type(value = Types.VARCHAR)
    private String serialNumber;

    /**
     * 任务名称
     */
    @Column(name = "taskName")
    @Type(value = Types.VARCHAR)
    private String taskName;

    /**
     * 用户任务领取时间
     */
    @Column(name = "receiveTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp receiveTime;

    /**
     * 用户任务失效时间
     */
    @Column(name = "deadline")
    @Type(value = Types.TIMESTAMP)
    private Timestamp deadline;

    /**
     * 任务有效开始时间
     */
    @Column(name = "taskStartTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp taskStartTime;

    /**
     * 任务有效结束时间
     */
    @Column(name = "taskEndTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp taskEndTime;

    /**
     * 任务完成时间
     */
    @Column(name = "completeTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp completeTime;

    /**
     * 领奖完成时间
     */
    @Column(name = "awardTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp awardTime;

    /**
     * 货币信息
     */
    @Column(name = "currencyDtoJson")
    @Type(value = Types.VARCHAR)
    private String currencyDtoJson;

    /**
     * 绑定时的ip地址
     */
    @Column(name = "ip")
    @Type(value = Types.VARCHAR)
    private String ip;

    /**
     * 绑定时的设备id
     */
    @Column(name = "clientId")
    @Type(value = Types.VARCHAR)
    private String clientId;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getProductLine() {
        return productLine;
    }

    public void setProductLine(Integer productLine) {
        this.productLine = productLine;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public BigDecimal getTotalCashbackMoney() {
        return totalCashbackMoney;
    }

    public void setTotalCashbackMoney(BigDecimal totalCashbackMoney) {
        this.totalCashbackMoney = totalCashbackMoney;
    }

    public BigDecimal getOrderRentalCarFee() {
        return orderRentalCarFee;
    }

    public void setOrderRentalCarFee(BigDecimal orderRentalCarFee) {
        this.orderRentalCarFee = orderRentalCarFee;
    }

    public String getCashbackRatioList() {
        return cashbackRatioList;
    }

    public void setCashbackRatioList(String cashbackRatioList) {
        this.cashbackRatioList = cashbackRatioList;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public Long getUserReceivedTaskId() {
        return userReceivedTaskId;
    }

    public void setUserReceivedTaskId(Long userReceivedTaskId) {
        this.userReceivedTaskId = userReceivedTaskId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Timestamp getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Timestamp receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Timestamp getDeadline() {
        return deadline;
    }

    public void setDeadline(Timestamp deadline) {
        this.deadline = deadline;
    }

    public Timestamp getTaskStartTime() {
        return taskStartTime;
    }

    public void setTaskStartTime(Timestamp taskStartTime) {
        this.taskStartTime = taskStartTime;
    }

    public Timestamp getTaskEndTime() {
        return taskEndTime;
    }

    public void setTaskEndTime(Timestamp taskEndTime) {
        this.taskEndTime = taskEndTime;
    }

    public Timestamp getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Timestamp completeTime) {
        this.completeTime = completeTime;
    }

    public Timestamp getAwardTime() {
        return awardTime;
    }

    public void setAwardTime(Timestamp awardTime) {
        this.awardTime = awardTime;
    }

    public String getCurrencyDtoJson() {
        return currencyDtoJson;
    }

    public void setCurrencyDtoJson(String currencyDtoJson) {
        this.currencyDtoJson = currencyDtoJson;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}

