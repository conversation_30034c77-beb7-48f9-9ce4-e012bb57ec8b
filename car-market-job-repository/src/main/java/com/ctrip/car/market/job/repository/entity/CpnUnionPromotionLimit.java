package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_union_promotion_limit")
public class CpnUnionPromotionLimit implements DalPojo {
    /**
     * 主键
     */
    @Id
    @Column(name = "limitId")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.INTEGER)
    private Integer limitId;

    /**
     * 策略id
     */
    @Column(name = "promotionId")
    @Type(value = Types.INTEGER)
    private Integer promotionId;

    /**
     * 策略名字
     */
    @Column(name = "promotionName")
    @Type(value = Types.VARCHAR)
    private String promotionName;

    /**
     * 展示名字
     */
    @Column(name = "promotionDisplayName")
    @Type(value = Types.VARCHAR)
    private String promotionDisplayName;

    /**
     * 开始时间
     */
    @Column(name = "startTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp startTime;

    /**
     * 结束时间
     */
    @Column(name = "endTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp endTime;

    /**
     * 合作部门，1：汽车票
     */
    @Column(name = "department")
    @Type(value = Types.SMALLINT)
    private Integer department;

    /**
     * 策略json配置
     */
    @Column(name = "context")
    @Type(value = Types.VARCHAR)
    private String context;

    /**
     * 0：无效，1：有效
     */
    @Column(name = "valid")
    @Type(value = Types.SMALLINT)
    private Integer valid;

    /**
     * 备注
     */
    @Column(name = "remark")
    @Type(value = Types.VARCHAR)
    private String remark;

    /**
     * 修改人
     */
    @Column(name = "modifyUser")
    @Type(value = Types.VARCHAR)
    private String modifyUser;

    /**
     * 更新时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Integer getLimitId() {
        return limitId;
    }

    public void setLimitId(Integer limitId) {
        this.limitId = limitId;
    }

    public Integer getPromotionId() {
        return promotionId;
    }

    public void setPromotionId(Integer promotionId) {
        this.promotionId = promotionId;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public String getPromotionDisplayName() {
        return promotionDisplayName;
    }

    public void setPromotionDisplayName(String promotionDisplayName) {
        this.promotionDisplayName = promotionDisplayName;
    }

    public Timestamp getStartTime() {
        return startTime;
    }

    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    public Timestamp getEndTime() {
        return endTime;
    }

    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }

    public Integer getDepartment() {
        return department;
    }

    public void setDepartment(Integer department) {
        this.department = department;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}