package com.ctrip.car.market.job.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2024-06-14
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "car_poi_mapping")
public class CarPoiMapping implements DalPojo {

    /**
     * 攻略poiid
     */
    @Id
    @Column(name = "poiId")
    @Type(value = Types.BIGINT)
    private Long poiId;

    /**
     * poi名称
     */
    @Column(name = "poiName")
    @Type(value = Types.VARCHAR)
    private String poiName;

    /**
     * 城市id
     */
    @Column(name = "cityId")
    @Type(value = Types.BIGINT)
    private Long cityId;

    /**
     * 用车id
     */
    @Column(name = "carPlaceId")
    @Type(value = Types.VARCHAR)
    private String carPlaceId;

    /**
     * 是否有效
     */
    @Column(name = "isActive")
    @Type(value = Types.BIT)
    private Boolean isActive;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getPoiId() {
        return poiId;
    }

    public void setPoiId(Long poiId) {
        this.poiId = poiId;
    }

    public String getPoiName() {
        return poiName;
    }

    public void setPoiName(String poiName) {
        this.poiName = poiName;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getCarPlaceId() {
        return carPlaceId;
    }

    public void setCarPlaceId(String carPlaceId) {
        this.carPlaceId = carPlaceId;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}

