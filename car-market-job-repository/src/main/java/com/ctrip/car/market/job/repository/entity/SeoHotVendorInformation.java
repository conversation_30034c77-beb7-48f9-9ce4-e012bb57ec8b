package com.ctrip.car.market.job.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2025-05-20
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "seo_hot_vendor_information")
public class SeoHotVendorInformation implements DalPojo {

    /**
     * 自增id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 供应商code
     */
    @Column(name = "vendorCode")
    @Type(value = Types.VARCHAR)
    private String vendorCode;

    /**
     * 供应商名称
     */
    @Column(name = "vendorName")
    @Type(value = Types.VARCHAR)
    private String vendorName;

    /**
     * 城市id
     */
    @Column(name = "cityId")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    /**
     * 城市名称
     */
    @Column(name = "cityName")
    @Type(value = Types.VARCHAR)
    private String cityName;

    /**
     * poi类型
     */
    @Column(name = "poiType")
    @Type(value = Types.INTEGER)
    private Integer poiType;

    /**
     * poi code
     */
    @Column(name = "poiCode")
    @Type(value = Types.VARCHAR)
    private String poiCode;

    /**
     * poiName
     */
    @Column(name = "poiName")
    @Type(value = Types.VARCHAR)
    private String poiName;

    /**
     * 车型组id
     */
    @Column(name = "vehicleGroupId")
    @Type(value = Types.INTEGER)
    private Integer vehicleGroupId;

    /**
     * 车型id
     */
    @Column(name = "vehicleId")
    @Type(value = Types.BIGINT)
    private Long vehicleId;

    /**
     * 热门租期
     */
    @Column(name = "tenancy")
    @Type(value = Types.INTEGER)
    private Integer tenancy;

    /**
     * 搜索量
     */
    @Column(name = "searchNum")
    @Type(value = Types.INTEGER)
    private Integer searchNum;

    /**
     * 门店数量
     */
    @Column(name = "storeNum")
    @Type(value = Types.INTEGER)
    private Integer storeNum;

    /**
     * 状态
     */
    @Column(name = "status")
    @Type(value = Types.INTEGER)
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getPoiType() {
        return poiType;
    }

    public void setPoiType(Integer poiType) {
        this.poiType = poiType;
    }

    public String getPoiCode() {
        return poiCode;
    }

    public void setPoiCode(String poiCode) {
        this.poiCode = poiCode;
    }

    public String getPoiName() {
        return poiName;
    }

    public void setPoiName(String poiName) {
        this.poiName = poiName;
    }

    public Integer getVehicleGroupId() {
        return vehicleGroupId;
    }

    public void setVehicleGroupId(Integer vehicleGroupId) {
        this.vehicleGroupId = vehicleGroupId;
    }

    public Long getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    public Integer getTenancy() {
        return tenancy;
    }

    public void setTenancy(Integer tenancy) {
        this.tenancy = tenancy;
    }

    public Integer getSearchNum() {
        return searchNum;
    }

    public void setSearchNum(Integer searchNum) {
        this.searchNum = searchNum;
    }

    public Integer getStoreNum() {
        return storeNum;
    }

    public void setStoreNum(Integer storeNum) {
        this.storeNum = storeNum;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}
