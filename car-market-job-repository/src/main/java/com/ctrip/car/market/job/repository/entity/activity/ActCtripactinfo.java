package com.ctrip.car.market.job.repository.entity.activity;

import com.ctrip.car.market.common.entity.act.CustomContent;
import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-09-19
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "act_ctripactinfo")
public class ActCtripactinfo implements DalPojo {

    /**
     * 自增主键
     */
    @Id
    @Column(name = "Id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 模板ID
     */
    @Column(name = "tempId")
    @Type(value = Types.BIGINT)
    private Long tempId;

    /**
     * 供应商类型
     */
    @Column(name = "vendorType")
    @Type(value = Types.INTEGER)
    private Integer vendorType;

    /**
     * 服务商ID，0表示携程
     */
    @Column(name = "vendorId")
    @Type(value = Types.BIGINT)
    private Long vendorId;

    /**
     * 活动开始时间
     */
    @Column(name = "startTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp startTime;

    /**
     * 活动结束时间
     */
    @Column(name = "endTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp endTime;

    /**
     * 报名参与车型组ID
     */
    @Column(name = "vehicleGroupIds")
    @Type(value = Types.VARCHAR)
    private String vehicleGroupIds;

    /**
     * 供应商优惠code
     */
    @Column(name = "VendorCouponCode")
    @Type(value = Types.VARCHAR)
    private String vendorCouponCode;

    /**
     * 状态
     */
    @Column(name = "status")
    @Type(value = Types.INTEGER)
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreateTime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
    @Column(name = "createUser")
    @Type(value = Types.VARCHAR)
    private String createUser;

    /**
     * 修改人
     */
    @Column(name = "modifyUser")
    @Type(value = Types.VARCHAR)
    private String modifyUser;

    /**
     * 是否排除城市
     */
    @Column(name = "excludeCity")
    @Type(value = Types.BIT)
    private Boolean excludeCity;

    /**
     * 是否排除还车城市
     */
    @Column(name = "excludeReturnCity")
    @Type(value = Types.BIT)
    private Boolean excludeReturnCity;

    /**
     * 是否排除门店
     */
    @Column(name = "excludeStore")
    @Type(value = Types.BIT)
    private Boolean excludeStore;

    /**
     * 是否排除sku
     */
    @Column(name = "excludeSku")
    @Type(value = Types.BIT)
    private Boolean excludeSku;

    /**
     * 是否排除标准产品
     */
    @Column(name = "excludeProduct")
    @Type(value = Types.BIT)
    private Boolean excludeProduct;

    /**
     * 自定义内容
     */
    @Column(name = "customContent")
    @Type(value = Types.VARCHAR)
    private String customContent;

    /**
     * 报名模式：0:供应商手动报名 1:跟价自动报名
     */
    @Column(name = "signUpMode")
    @Type(value = Types.INTEGER)
    private Integer signUpMode;

    private CustomContent content;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTempId() {
        return tempId;
    }

    public void setTempId(Long tempId) {
        this.tempId = tempId;
    }

    public Integer getVendorType() {
        return vendorType;
    }

    public void setVendorType(Integer vendorType) {
        this.vendorType = vendorType;
    }

    public Long getVendorId() {
        return vendorId;
    }

    public void setVendorId(Long vendorId) {
        this.vendorId = vendorId;
    }

    public Timestamp getStartTime() {
        return startTime;
    }

    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    public Timestamp getEndTime() {
        return endTime;
    }

    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }

    public String getVehicleGroupIds() {
        return vehicleGroupIds;
    }

    public void setVehicleGroupIds(String vehicleGroupIds) {
        this.vehicleGroupIds = vehicleGroupIds;
    }

    public String getVendorCouponCode() {
        return vendorCouponCode;
    }

    public void setVendorCouponCode(String vendorCouponCode) {
        this.vendorCouponCode = vendorCouponCode;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Timestamp getDatachangeCreateTime() {
        return datachangeCreateTime;
    }

    public void setDatachangeCreateTime(Timestamp datachangeCreateTime) {
        this.datachangeCreateTime = datachangeCreateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Boolean getExcludeCity() {
        return excludeCity;
    }

    public void setExcludeCity(Boolean excludeCity) {
        this.excludeCity = excludeCity;
    }

    public Boolean getExcludeReturnCity() {
        return excludeReturnCity;
    }

    public void setExcludeReturnCity(Boolean excludeReturnCity) {
        this.excludeReturnCity = excludeReturnCity;
    }

    public Boolean getExcludeStore() {
        return excludeStore;
    }

    public void setExcludeStore(Boolean excludeStore) {
        this.excludeStore = excludeStore;
    }

    public Boolean getExcludeSku() {
        return excludeSku;
    }

    public void setExcludeSku(Boolean excludeSku) {
        this.excludeSku = excludeSku;
    }

    public Boolean getExcludeProduct() {
        return excludeProduct;
    }

    public void setExcludeProduct(Boolean excludeProduct) {
        this.excludeProduct = excludeProduct;
    }

    public String getCustomContent() {
        return customContent;
    }

    public void setCustomContent(String customContent) {
        this.customContent = customContent;
    }

    public CustomContent getContent() {
        return content;
    }

    public void setContent(CustomContent content) {
        this.content = content;
    }

    public Integer getSignUpMode() {
        return signUpMode;
    }

    public void setSignUpMode(Integer signUpMode) {
        this.signUpMode = signUpMode;
    }
}

