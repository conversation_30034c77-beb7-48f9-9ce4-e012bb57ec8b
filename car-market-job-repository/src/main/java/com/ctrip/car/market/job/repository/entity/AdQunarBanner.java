package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "ad_qunar_banner")
public class AdQunarBanner implements DalPojo {

    /**
     * 自增主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 帧位
     */
    @Column(name = "frame")
    @Type(value = Types.INTEGER)
    private Integer frame;

    /**
     * 纵横物料ID
     */
    @Column(name = "impId")
    @Type(value = Types.VARCHAR)
    private String impId;

    /**
     * 推广计划ID
     */
    @Column(name = "campaignId")
    @Type(value = Types.BIGINT)
    private Long campaignId;

    /**
     * 是否有效
     */
    @Column(name = "activeStatus")
    @Type(value = Types.BIT)
    private Boolean activeStatus;

    /**
     * 图片地址
     */
    @Column(name = "imageURL")
    @Type(value = Types.VARCHAR)
    private String imageURL;

    /**
     * 点击地址
     */
    @Column(name = "clickURL")
    @Type(value = Types.VARCHAR)
    private String clickURL;

    /**
     * 监控地址
     */
    @Column(name = "monitorURL")
    @Type(value = Types.VARCHAR)
    private String monitorURL;

    /**
     * 开始时间
     */
    @Column(name = "startDate")
    @Type(value = Types.VARCHAR)
    private String startDate;

    /**
     * 结束时间
     */
    @Column(name = "endDate")
    @Type(value = Types.VARCHAR)
    private String endDate;

    /**
     * 物料尺寸 1080x2340
     */
    @Column(name = "size")
    @Type(value = Types.VARCHAR)
    private String size;

    /**
     * 去哪儿投放ID
     */
    @Column(name = "filghtId")
    @Type(value = Types.VARCHAR)
    private String filghtId;

    /**
     * 去哪物料ID
     */
    @Column(name = "adId")
    @Type(value = Types.VARCHAR)
    private String adId;

    /**
     * 创建人
     */
    @Column(name = "createUser")
    @Type(value = Types.VARCHAR)
    private String createUser;

    /**
     * 更新人
     */
    @Column(name = "updateUser")
    @Type(value = Types.VARCHAR)
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 携程服务器图片Url
     */
    @Column(name = "ctripImageURL")
    @Type(value = Types.VARCHAR)
    private String ctripImageURL;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getFrame() {
        return frame;
    }

    public void setFrame(Integer frame) {
        this.frame = frame;
    }

    public String getImpId() {
        return impId;
    }

    public void setImpId(String impId) {
        this.impId = impId;
    }

    public Long getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Long campaignId) {
        this.campaignId = campaignId;
    }

    public Boolean getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(Boolean activeStatus) {
        this.activeStatus = activeStatus;
    }

    public String getImageURL() {
        return imageURL;
    }

    public void setImageURL(String imageURL) {
        this.imageURL = imageURL;
    }

    public String getClickURL() {
        return clickURL;
    }

    public void setClickURL(String clickURL) {
        this.clickURL = clickURL;
    }

    public String getMonitorURL() {
        return monitorURL;
    }

    public void setMonitorURL(String monitorURL) {
        this.monitorURL = monitorURL;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getFilghtId() {
        return filghtId;
    }

    public void setFilghtId(String filghtId) {
        this.filghtId = filghtId;
    }

    public String getAdId() {
        return adId;
    }

    public void setAdId(String adId) {
        this.adId = adId;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getCtripImageURL() {
        return ctripImageURL;
    }

    public void setCtripImageURL(String ctripImageURL) {
        this.ctripImageURL = ctripImageURL;
    }

}