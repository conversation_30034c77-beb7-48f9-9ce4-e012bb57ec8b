package com.ctrip.car.market.job.repository.entity;


import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_label")
public class CpnLabel implements DalPojo {

    /**
     * 标识列
     */
    @Id
    @Column(name = "Code")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long code;

    /**
     * 标签类型（1-自定义 2-门店 3-车型 4-营销 5-供应商）
     */
    @Column(name = "CType")
    @Type(value = Types.INTEGER)
    private Integer cType;

    /**
     * 标签名称
     */
    @Column(name = "Name")
    @Type(value = Types.VARCHAR)
    private String name;

    /**
     * 标签描述
     */
    @Column(name = "Description")
    @Type(value = Types.VARCHAR)
    private String description;

    /**
     * 标签位置
     */
    @Column(name = "Pos")
    @Type(value = Types.INTEGER)
    private Integer pos;

    /**
     * 标签所在组的code
     */
    @Column(name = "GroupCode")
    @Type(value = Types.VARCHAR)
    private String groupCode;

    /**
     * 标签所在组的名称
     */
    @Column(name = "GroupName")
    @Type(value = Types.VARCHAR)
    private String groupName;

    /**
     * 标签来源（1-自定义 2-运营3-营销）
     */
    @Column(name = "Source")
    @Type(value = Types.INTEGER)
    private Integer source;

    /**
     * 是否展示
     */
    @Column(name = "IsDisplay")
    @Type(value = Types.BIT)
    private Boolean isDisplay;

    /**
     * 是否有效
     */
    @Column(name = "IsActive")
    @Type(value = Types.BIT)
    private Boolean isActive;

    /**
     * 默认时间
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间戳
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 列表页是否展示1展示，0不展示  因为活动系统长期存在使用两个display字段混淆而且没有被发现得问题，而且活动得标签展示没有做细分控制，此字段废弃，统一使用isdisplay字段做是否展示字段
     */
    @Column(name = "IsListDisplay")
    @Type(value = Types.BIT)
    private Boolean isListDisplay;

    /**
     * 状态
     */
    @Column(name = "Status")
    @Type(value = Types.INTEGER)
    private Integer status;

    /**
     * 0所有,35国内自驾，34海外自驾
     */
    @Column(name = "ProductCategoryId")
    @Type(value = Types.TINYINT)
    private Integer productCategoryId;

    /**
     * 默认0表示携程，1表示PMS
     */
    @Column(name = "Platform")
    @Type(value = Types.TINYINT)
    private Integer platform;

    /**
     * cpn_label_group主键
     */
    @Column(name = "groupId")
    @Type(value = Types.INTEGER)
    private Integer groupId;

    /**
     * 标签导向
     * 0-正想标签
     * 1-负向标签
     */
    @Column(name = "positiveType")
    @Type(value = Types.TINYINT)
    private Integer positiveType;

    /**
     * 展示方式 0-文字 1-图片
     */
    @Column(name = "showType")
    @Type(value = Types.TINYINT)
    private Integer showType;

    /**
     * 标签图片url
     */
    @Column(name = "imgUrl")
    @Type(value = Types.VARCHAR)
    private String imgUrl;

    /**
     * 标签描述（列表页）
     */
    @Column(name = "shortDesc")
    @Type(value = Types.VARCHAR)
    private String shortDesc;

    /**
     * 列表页筛选图片url
     */
    @Column(name = "filterImgUrl")
    @Type(value = Types.VARCHAR)
    private String filterImgUrl;

    /**
     * 支持的语言，locale字段，使用逗号分隔
     */
    @Column(name = "locales")
    @Type(value = Types.VARCHAR)
    private String locales;

    /**
     * 排序
     */
    @Column(name = "sort")
    @Type(value = Types.TINYINT)
    private Integer sort;

    /**
     * 附加说明
     */
    @Column(name = "extra_description")
    @Type(value = Types.VARCHAR)
    private String extraDescription;

    /**
     * m名称附加说明
     */
    @Column(name = "extra_namedesc")
    @Type(value = Types.VARCHAR)
    private String extraNameDesc;

    /**
     * 1----  一级列表页（车型列表）  2----  弹层列表页（资源列表）
     */
    @Column(name = "displayPos")
    @Type(value = Types.VARCHAR)
    private String displayPos;


    /**
     * 1', v: '押金双免和免费取消'}, {k: '2', v: '限时免费取消'}, {k: '3', v: '有损取消
     */
    @Column(name = "mergeId")
    @Type(value = Types.TINYINT)
    private Integer mergeId;

    public Long getCode() {
        return code;
    }

    public void setCode(Long code) {
        this.code = code;
    }

    public Integer getCType() {
        return cType;
    }

    public void setCType(Integer cType) {
        this.cType = cType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getPos() {
        return pos;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public void setPos(Integer pos) {
        this.pos = pos;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Boolean getIsDisplay() {
        return isDisplay;
    }

    public void setIsDisplay(Boolean isDisplay) {
        this.isDisplay = isDisplay;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Boolean getIsListDisplay() {
        return isListDisplay;
    }

    public void setIsListDisplay(Boolean isListDisplay) {
        this.isListDisplay = isListDisplay;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getProductCategoryId() {
        return productCategoryId;
    }

    public void setProductCategoryId(Integer productCategoryId) {
        this.productCategoryId = productCategoryId;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Integer getPositiveType() {
        return positiveType;
    }

    public void setPositiveType(Integer positiveType) {
        this.positiveType = positiveType;
    }

    public Integer getShowType() {
        return showType;
    }

    public void setShowType(Integer showType) {
        this.showType = showType;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getShortDesc() {
        return shortDesc;
    }

    public void setShortDesc(String shortDesc) {
        this.shortDesc = shortDesc;
    }

    public String getFilterImgUrl() {
        return filterImgUrl;
    }

    public void setFilterImgUrl(String filterImgUrl) {
        this.filterImgUrl = filterImgUrl;
    }

    public String getLocales() {
        return locales;
    }

    public void setLocales(String locales) {
        this.locales = locales;
    }

    public String getExtraDescription() {
        return extraDescription;
    }

    public void setExtraDescription(String extraDescription) {
        this.extraDescription = extraDescription;
    }

    public String getExtraNameDesc() {
        return extraNameDesc;
    }

    public void setExtraNameDesc(String extraNameDesc) {
        this.extraNameDesc = extraNameDesc;
    }

    public String getDisplayPos() {
        return displayPos;
    }

    public void setDisplayPos(String displayPos) {
        this.displayPos = displayPos;
    }

    public Integer getMergeId() {
        return mergeId;
    }

    public void setMergeId(Integer mergeId) {
        this.mergeId = mergeId;
    }
}
