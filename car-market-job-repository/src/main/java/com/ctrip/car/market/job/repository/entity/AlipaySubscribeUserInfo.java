package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2024-09-29
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "alipay_subscribe_userinfo")
public class AlipaySubscribeUserInfo implements DalPojo {

    /**
     * 自增主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 模版id
     */
    @Column(name = "templateId")
    @Type(value = Types.VARCHAR)
    private String templateId;

    /**
     * 支付宝userid
     */
    @Column(name = "alipayUserId")
    @Type(value = Types.VARCHAR)
    private String alipayUserId;

    /**
     * 携程uid
     */
    @Column(name = "ctripUid")
    @Type(value = Types.VARCHAR)
    private String ctripUid;

    /**
     * 状态
     */
    @Column(name = "status")
    @Type(value = Types.VARCHAR)
    private Integer status;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getAlipayUserId() {
        return alipayUserId;
    }

    public void setAlipayUserId(String alipayUserId) {
        this.alipayUserId = alipayUserId;
    }

    public String getCtripUid() {
        return ctripUid;
    }

    public void setCtripUid(String ctripUid) {
        this.ctripUid = ctripUid;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }
}
