package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_promotionidredirecturl")
public class CpnPromotionidredirecturl implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long iD;

    /**
     * 策略ID
     */
    @Column(name = "promotionId")
    @Type(value = Types.BIGINT)
    private Long promotionId;

    /**
     * 优惠券显示名称
     */
    @Column(name = "showName")
    @Type(value = Types.VARCHAR)
    private String showName;

    /**
     * 渠道短描述
     */
    @Column(name = "shortDesc")
    @Type(value = Types.VARCHAR)
    private String shortDesc;

    /**
     * pc跳转
     */
    @Column(name = "pcJumpUrl")
    @Type(value = Types.VARCHAR)
    private String pcJumpUrl;

    /**
     * h5跳转
     */
    @Column(name = "h5JumpUrl")
    @Type(value = Types.VARCHAR)
    private String h5JumpUrl;

    /**
     * app跳转
     */
    @Column(name = "appJumpUrl")
    @Type(value = Types.VARCHAR)
    private String appJumpUrl;

    /**
     * rn跳转
     */
    @Column(name = "rnJumpUrl")
    @Type(value = Types.VARCHAR)
    private String rnJumpUrl;

    /**
     * 微信百度阿里马甲包之类的跳转
     */
    @Column(name = "otherJumpContent")
    @Type(value = Types.VARCHAR)
    private String otherJumpContent;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
    @Column(name = "createor")
    @Type(value = Types.VARCHAR)
    private String createor;

    /**
     * 更新人
     */
    @Column(name = "modifer")
    @Type(value = Types.VARCHAR)
    private String modifer;

    /**
     * 所属角色
     */
    @Column(name = "offlineRoleCode")
    @Type(value = Types.VARCHAR)
    private String offlineRoleCode;

    /**
     * 0未生效，1生效
     */
    @Column(name = "isValid")
    @Type(value = Types.TINYINT)
    private Integer isValid;

    public Long getID() {
        return iD;
    }

    public void setID(Long iD) {
        this.iD = iD;
    }

    public Long getPromotionId() {
        return promotionId;
    }

    public void setPromotionId(Long promotionId) {
        this.promotionId = promotionId;
    }

    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }

    public String getShortDesc() {
        return shortDesc;
    }

    public void setShortDesc(String shortDesc) {
        this.shortDesc = shortDesc;
    }

    public String getPcJumpUrl() {
        return pcJumpUrl;
    }

    public void setPcJumpUrl(String pcJumpUrl) {
        this.pcJumpUrl = pcJumpUrl;
    }

    public String getH5JumpUrl() {
        return h5JumpUrl;
    }

    public void setH5JumpUrl(String h5JumpUrl) {
        this.h5JumpUrl = h5JumpUrl;
    }

    public String getAppJumpUrl() {
        return appJumpUrl;
    }

    public void setAppJumpUrl(String appJumpUrl) {
        this.appJumpUrl = appJumpUrl;
    }

    public String getRnJumpUrl() {
        return rnJumpUrl;
    }

    public void setRnJumpUrl(String rnJumpUrl) {
        this.rnJumpUrl = rnJumpUrl;
    }

    public String getOtherJumpContent() {
        return otherJumpContent;
    }

    public void setOtherJumpContent(String otherJumpContent) {
        this.otherJumpContent = otherJumpContent;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getCreateor() {
        return createor;
    }

    public void setCreateor(String createor) {
        this.createor = createor;
    }

    public String getModifer() {
        return modifer;
    }

    public void setModifer(String modifer) {
        this.modifer = modifer;
    }

    public String getOfflineRoleCode() {
        return offlineRoleCode;
    }

    public void setOfflineRoleCode(String offlineRoleCode) {
        this.offlineRoleCode = offlineRoleCode;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

}