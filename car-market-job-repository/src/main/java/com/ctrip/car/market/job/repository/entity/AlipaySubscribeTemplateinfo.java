package com.ctrip.car.market.job.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;
import java.util.List;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2024-09-29
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "alipay_subscribe_templateinfo")
public class AlipaySubscribeTemplateinfo implements DalPojo {

    /**
     * 自增主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 模版id
     */
    @Column(name = "templateId")
    @Type(value = Types.VARCHAR)
    private String templateId;

    /**
     * 模版名称
     */
    @Column(name = "templateName")
    @Type(value = Types.VARCHAR)
    private String templateName;

    /**
     * 模版类型：【0：优惠券领取；1：活动开始；2：待支付；3：取消订单】
     */
    @Column(name = "templateType")
    @Type(value = Types.SMALLINT)
    private Integer templateType;

    /**
     * 模版json格式内容
     */
    @Column(name = "templateContent")
    @Type(value = Types.VARCHAR)
    private String templateContent;

    /**
     * 策略id
     */
    @Column(name = "strategyId")
    @Type(value = Types.INTEGER)
    private Integer strategyId;

    /**
     * 国内跳转链接
     */
    @Column(name = "isdUrl")
    @Type(value = Types.VARCHAR)
    private String isdUrl;

    /**
     * 出境跳转链接
     */
    @Column(name = "osdUrl")
    @Type(value = Types.VARCHAR)
    private String osdUrl;

    /**
     * 剩余配置时间字段（待支付提醒模版专属，单位min）
     */
    @Column(name = "remainingTime")
    @Type(value = Types.INTEGER)
    private Integer remainingTime;

    /**
     * 失效时间
     */
    @Column(name = "expirationTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp expirationTime;

    /**
     * 模版状态【0：启用，1：停用】
     */
    @Column(name = "status")
    @Type(value = Types.TINYINT)
    private Integer status;

    /**
     * 创建人
     */
    @Column(name = "createUser")
    @Type(value = Types.VARCHAR)
    private String createUser;

    /**
     * 修改人
     */
    @Column(name = "modifyUser")
    @Type(value = Types.VARCHAR)
    private String modifyUser;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    private List<String> paramaterList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public String getTemplateContent() {
        return templateContent;
    }

    public void setTemplateContent(String templateContent) {
        this.templateContent = templateContent;
    }

    public Integer getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(Integer strategyId) {
        this.strategyId = strategyId;
    }

    public String getIsdUrl() {
        return isdUrl;
    }

    public void setIsdUrl(String isdUrl) {
        this.isdUrl = isdUrl;
    }

    public String getOsdUrl() {
        return osdUrl;
    }

    public void setOsdUrl(String osdUrl) {
        this.osdUrl = osdUrl;
    }

    public Integer getRemainingTime() {
        return remainingTime;
    }

    public void setRemainingTime(Integer remainingTime) {
        this.remainingTime = remainingTime;
    }

    public Timestamp getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(Timestamp expirationTime) {
        this.expirationTime = expirationTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public List<String> getParamaterList() {
        return paramaterList;
    }

    public void setParamaterList(List<String> paramaterList) {
        this.paramaterList = paramaterList;
    }
}
