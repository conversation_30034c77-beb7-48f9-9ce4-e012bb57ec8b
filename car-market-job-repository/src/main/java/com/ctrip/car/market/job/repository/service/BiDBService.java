package com.ctrip.car.market.job.repository.service;

import com.ctrip.car.market.job.repository.entity.CarSaleRank;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BiDBService {

    private final DalTableOperations<CarSaleRank> saleRankOPerations = DalOperationsFactory.getDalTableOperations(CarSaleRank.class);

    public List<CarSaleRank> queryAllSaleRank() throws Exception {
        return saleRankOPerations.query("select * from car_sale_rank where `active`=1", new DalHints(), Maps.newHashMap());
    }
}
