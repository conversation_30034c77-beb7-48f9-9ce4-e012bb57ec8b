package com.ctrip.car.market.job.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2022-12-12
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "mktapi_token")
public class MktapiToken implements <PERSON><PERSON><PERSON><PERSON> {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 应用ID
     */
    @Column(name = "client_id")
    @Type(value = Types.BIGINT)
    private Long clientId;

    /**
     * 业务ID
     */
    @Column(name = "biz_id")
    @Type(value = Types.INTEGER)
    private Integer bizId;

    /**
     * access_token
     */
    @Column(name = "access_token")
    @Type(value = Types.VARCHAR)
    private String accessToken;

    /**
     * access_token_过期时间
     */
    @Column(name = "access_token_expires_in")
    @Type(value = Types.TIMESTAMP)
    private Timestamp accessTokenExpiresIn;

    /**
     * refresh_token
     */
    @Column(name = "refresh_token")
    @Type(value = Types.VARCHAR)
    private String refreshToken;

    /**
     * refresh_token_过期时间
     */
    @Column(name = "refresh_token_expires_in")
    @Type(value = Types.TIMESTAMP)
    private Timestamp refreshTokenExpiresIn;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @Type(value = Types.VARCHAR)
    private String createUser;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @Type(value = Types.VARCHAR)
    private String updateUser;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Integer getBizId() {
        return bizId;
    }

    public void setBizId(Integer bizId) {
        this.bizId = bizId;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public Timestamp getAccessTokenExpiresIn() {
        return accessTokenExpiresIn;
    }

    public void setAccessTokenExpiresIn(Timestamp accessTokenExpiresIn) {
        this.accessTokenExpiresIn = accessTokenExpiresIn;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Timestamp getRefreshTokenExpiresIn() {
        return refreshTokenExpiresIn;
    }

    public void setRefreshTokenExpiresIn(Timestamp refreshTokenExpiresIn) {
        this.refreshTokenExpiresIn = refreshTokenExpiresIn;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}

