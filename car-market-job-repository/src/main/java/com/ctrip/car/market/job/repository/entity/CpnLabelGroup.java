package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2019-11-05
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_label_group")
public class CpnLabelGroup implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 类别 1-促销类  2-服务类
     */
    @Column(name = "category")
    @Type(value = Types.INTEGER)
    private Integer category;

    /**
     * category中的子分类id
     */
    @Column(name = "subCategory")
    @Type(value = Types.INTEGER)
    private Integer subCategory;

    /**
     * 子标签的名字
     */
    @Column(name = "subCategoryDesc")
    @Type(value = Types.VARCHAR)
    private String subCategoryDesc;

    /**
     * MarketGroup
     */
    @Column(name = "marketGroup")
    @Type(value = Types.INTEGER)
    private Integer marketGroup;

    /**
     * 描述
     */
    @Column(name = "marketGroupDesc")
    @Type(value = Types.VARCHAR)
    private String marketGroupDesc;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 列表页是否显示快速筛选
     */
    @Column(name = "showQuickPickInList")
    @Type(value = Types.BIT)
    private Boolean showQuickPickInList;

    /**
     * 列表页是否显示快速通知栏
     */
    @Column(name = "showQuickNoticeInList")
    @Type(value = Types.BIT)
    private Boolean showQuickNoticeInList;

    /**
     * 快速通知栏图标
     */
    @Column(name = "QuickNoticeIcon")
    @Type(value = Types.VARCHAR)
    private String quickNoticeIcon;

    /**
     * 优先级
     */
    @Column(name = "showPriority")
    @Type(value = Types.INTEGER)
    private Integer showPriority;

    /**
     * 通知栏文案
     */
    @Column(name = "NoticeContent")
    @Type(value = Types.VARCHAR)
    private String noticeContent;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public Integer getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(Integer subCategory) {
        this.subCategory = subCategory;
    }

    public String getSubCategoryDesc() {
        return subCategoryDesc;
    }

    public void setSubCategoryDesc(String subCategoryDesc) {
        this.subCategoryDesc = subCategoryDesc;
    }

    public Integer getMarketGroup() {
        return marketGroup;
    }

    public void setMarketGroup(Integer marketGroup) {
        this.marketGroup = marketGroup;
    }

    public String getMarketGroupDesc() {
        return marketGroupDesc;
    }

    public void setMarketGroupDesc(String marketGroupDesc) {
        this.marketGroupDesc = marketGroupDesc;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Boolean getShowQuickPickInList() {
        return showQuickPickInList;
    }

    public void setShowQuickPickInList(Boolean showQuickPickInList) {
        this.showQuickPickInList = showQuickPickInList;
    }

    public Boolean getShowQuickNoticeInList() {
        return showQuickNoticeInList;
    }

    public void setShowQuickNoticeInList(Boolean showQuickNoticeInList) {
        this.showQuickNoticeInList = showQuickNoticeInList;
    }

    public String getQuickNoticeIcon() {
        return quickNoticeIcon;
    }

    public void setQuickNoticeIcon(String quickNoticeIcon) {
        this.quickNoticeIcon = quickNoticeIcon;
    }

    public Integer getShowPriority() {
        return showPriority;
    }

    public void setShowPriority(Integer showPriority) {
        this.showPriority = showPriority;
    }

    public String getNoticeContent() {
        return noticeContent;
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }

}
