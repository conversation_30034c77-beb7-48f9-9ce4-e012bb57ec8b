package com.ctrip.car.market.job.repository.entity.activity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-03-10
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "act_osdactinfo")
public class ActOsdactinfo implements DalPojo {

    /**
     * 自增id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 活动类型，0：供应商套餐活动 1：平台代运营
     */
    @Column(name = "type")
    @Type(value = Types.INTEGER)
    private Integer type;

    /**
     * 活动名称
     */
    @Column(name = "name")
    @Type(value = Types.VARCHAR)
    private String name;

    /**
     * 活动开始时间
     */
    @Column(name = "startTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp startTime;

    /**
     * 活动结束时间
     */
    @Column(name = "endTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp endTime;

    /**
     * 活动状态 ，1：活动中 2：已结束 3：已禁用
     */
    @Column(name = "status")
    @Type(value = Types.INTEGER)
    private Integer status;

    /**
     * 限制条件
     */
    @Column(name = "conditions")
    @Type(value = Types.VARCHAR)
    private String conditions;

    /**
     * 创建人
     */
    @Column(name = "createUser")
    @Type(value = Types.VARCHAR)
    private String createUser;

    /**
     * 修改人
     */
    @Column(name = "modifyUser")
    @Type(value = Types.VARCHAR)
    private String modifyUser;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Timestamp getStartTime() {
        return startTime;
    }

    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    public Timestamp getEndTime() {
        return endTime;
    }

    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getConditions() {
        return conditions;
    }

    public void setConditions(String conditions) {
        this.conditions = conditions;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}
