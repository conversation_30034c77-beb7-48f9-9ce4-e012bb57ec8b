package com.ctrip.car.market.job.repository.entity.carseodb;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2024-04-05
 */
@Entity
@Database(name = "carseodb_dalcluster")
@Table(name = "seo_faq")
public class SeoFaq implements DalPojo {

    /**
     * 主键id
     */
    @Id
    @Column(name = "Id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 最便宜的租车价格top 3（日价）+ 对应的供应商名称
     */
    @Column(name = "VendorsContent")
    @Type(value = Types.VARCHAR)
    private String vendorsContent;

    /**
     * 订单最多的租车公司top1
     */
    @Column(name = "HotVendorName")
    @Type(value = Types.VARCHAR)
    private String hotVendorName;

    /**
     * 订单最多的子车型组名称top1
     */
    @Column(name = "HotGroupName")
    @Type(value = Types.VARCHAR)
    private String hotGroupName;

    /**
     * 是否有效
     */
    @Column(name = "Active")
    @Type(value = Types.BIT)
    private Boolean active;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 车型组英文名称
     */
    @Column(name = "GroupEName")
    @Type(value = Types.VARCHAR)
    private String groupEName;

    /**
     * 城市id
     */
    @Column(name = "CityId")
    @Type(value = Types.BIGINT)
    private Long cityId;

    /**
     * 创建时间
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVendorsContent() {
        return vendorsContent;
    }

    public void setVendorsContent(String vendorsContent) {
        this.vendorsContent = vendorsContent;
    }

    public String getHotVendorName() {
        return hotVendorName;
    }

    public void setHotVendorName(String hotVendorName) {
        this.hotVendorName = hotVendorName;
    }

    public String getHotGroupName() {
        return hotGroupName;
    }

    public void setHotGroupName(String hotGroupName) {
        this.hotGroupName = hotGroupName;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getGroupEName() {
        return groupEName;
    }

    public void setGroupEName(String groupEName) {
        this.groupEName = groupEName;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

}
