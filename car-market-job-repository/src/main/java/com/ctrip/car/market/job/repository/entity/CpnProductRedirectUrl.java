package com.ctrip.car.market.job.repository.entity;


import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_productredirecturl")
public class CpnProductRedirectUrl implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "UrlID")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long urlID;

    /**
     * 产品类型Id 32-CHF国内代驾 33-OCH海外代驾 34-OSD海外自驾 35-ISD国内自驾 37-GPS订单
     */
    @Column(name = "BusinessType")
    @Type(value = Types.TINYINT)
    private Integer businessType;

    /**
     * 产品形态Id 17-接机 18-送机 23-日租 24-时租 25-代驾城际 26-Taxi送机/火车 27-Taxi随时 28-随叫随到 29-随叫随到（预约） 50-GPS订单 60-海外自驾打包产品 70-国内自驾春节打包产品 171-接火车 181-送火车
     */
    @Column(name = "BusinessSubType")
    @Type(value = Types.TINYINT)
    private Integer businessSubType;

    /**
     * 跳转链接
     */
    @Column(name = "RedirectUrl")
    @Type(value = Types.VARCHAR)
    private String redirectUrl;

    /**
     * 指示该数据是否有效
     */
    @Column(name = "IsValid")
    @Type(value = Types.TINYINT)
    private Integer isValid;

    /**
     * 链接类型 0-Online 1-APP 2-H5，3 独立app, 4 x小程序
     */
    @Column(name = "UrlType")
    @Type(value = Types.TINYINT)
    private Integer urlType;

    /**
     * 链接名称
     */
    @Column(name = "UrlName")
    @Type(value = Types.VARCHAR)
    private String urlName;

    /**
     * 备注
     */
    @Column(name = "Remark")
    @Type(value = Types.VARCHAR)
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 描述
     */
    @Column(name = "Discribe")
    @Type(value = Types.VARCHAR)
    private String discribe;

    /**
     * 0默认携程,1去哪儿,2 龙艺龙，99 携程租车, 其他是小程序appid
     */
    @Column(name = "UnionType")
    @Type(value = Types.VARCHAR)
    private String unionType;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 要跳转到的小城APPid
     */
    @Column(name = "JumpMiniAppId")
    @Type(value = Types.VARCHAR)
    private String jumpMiniAppId;

    public Long getUrlID() {
        return urlID;
    }

    public void setUrlID(Long urlID) {
        this.urlID = urlID;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getBusinessSubType() {
        return businessSubType;
    }

    public void setBusinessSubType(Integer businessSubType) {
        this.businessSubType = businessSubType;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Integer getUrlType() {
        return urlType;
    }

    public void setUrlType(Integer urlType) {
        this.urlType = urlType;
    }

    public String getUrlName() {
        return urlName;
    }

    public void setUrlName(String urlName) {
        this.urlName = urlName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public String getDiscribe() {
        return discribe;
    }

    public void setDiscribe(String discribe) {
        this.discribe = discribe;
    }

    public String getUnionType() {
        return unionType;
    }

    public void setUnionType(String unionType) {
        this.unionType = unionType;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getJumpMiniAppId() {
        return jumpMiniAppId;
    }

    public void setJumpMiniAppId(String jumpMiniAppId) {
        this.jumpMiniAppId = jumpMiniAppId;
    }

}
