package com.ctrip.car.market.job.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.math.BigDecimal;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2023-06-07
 */
@Entity
@Database(name = "carbidb_dalcluster")
@Table(name = "car_sale_rank")
public class CarSaleRank implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 车型名称
     */
    @Column(name = "vehicle_name")
    @Type(value = Types.VARCHAR)
    private String vehicleName;

    /**
     * 车型组名称
     */
    @Column(name = "vehicle_group_name")
    @Type(value = Types.VARCHAR)
    private String vehicleGroupName;

    /**
     * 车型id
     */
    @Column(name = "vehicle_id")
    @Type(value = Types.INTEGER)
    private Integer vehicleId;

    /**
     * 车辆排挡：0手动挡 1自动挡
     */
    @Column(name = "trans")
    @Type(value = Types.TINYINT)
    private Integer trans;

    /**
     * 排量
     */
    @Column(name = "displacement")
    @Type(value = Types.VARCHAR)
    private String displacement;

    /**
     * 座位数
     */
    @Column(name = "seat")
    @Type(value = Types.INTEGER)
    private Integer seat;

    /**
     * 车门数
     */
    @Column(name = "door_no")
    @Type(value = Types.INTEGER)
    private Integer doorNo;

    /**
     * 三厢
     */
    @Column(name = "carriage_desc")
    @Type(value = Types.VARCHAR)
    private String carriageDesc;

    /**
     * 所有城市的排名
     */
    @Column(name = "vehicle_rank")
    @Type(value = Types.INTEGER)
    private Integer vehicleRank;

    /**
     * 城市id
     */
    @Column(name = "city_id")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    /**
     * 车型图片链接
     */
    @Column(name = "vehicle_image_url")
    @Type(value = Types.VARCHAR)
    private String vehicleImageUrl;

    /**
     * 是否有效
     */
    @Column(name = "active")
    @Type(value = Types.BIT)
    private Boolean active;

    /**
     * 创建时间
     */
    @Column(name = "datachage_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachageCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 服务标签，英文逗号分隔，例：押金双免,免费取消
     */
    @Column(name = "service_labels")
    @Type(value = Types.VARCHAR)
    private String serviceLabels;

    /**
     * 城市内销量最好的供应商id
     */
    @Column(name = "vendor_id")
    @Type(value = Types.INTEGER)
    private Integer vendorId;

    /**
     * 城市内的最低价
     */
    @Column(name = "floor_price")
    @Type(value = Types.DECIMAL)
    private BigDecimal floorPrice;

    /**
     * 是否卡拉比数据源
     */
    @Column(name = "isCalabi")
    @Type(value = Types.BIT)
    private Boolean isCalabi;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }

    public String getVehicleGroupName() {
        return vehicleGroupName;
    }

    public void setVehicleGroupName(String vehicleGroupName) {
        this.vehicleGroupName = vehicleGroupName;
    }

    public Integer getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(Integer vehicleId) {
        this.vehicleId = vehicleId;
    }

    public Integer getTrans() {
        return trans;
    }

    public void setTrans(Integer trans) {
        this.trans = trans;
    }

    public String getDisplacement() {
        return displacement;
    }

    public void setDisplacement(String displacement) {
        this.displacement = displacement;
    }

    public Integer getSeat() {
        return seat;
    }

    public void setSeat(Integer seat) {
        this.seat = seat;
    }

    public Integer getDoorNo() {
        return doorNo;
    }

    public void setDoorNo(Integer doorNo) {
        this.doorNo = doorNo;
    }

    public String getCarriageDesc() {
        return carriageDesc;
    }

    public void setCarriageDesc(String carriageDesc) {
        this.carriageDesc = carriageDesc;
    }

    public Integer getVehicleRank() {
        return vehicleRank;
    }

    public void setVehicleRank(Integer vehicleRank) {
        this.vehicleRank = vehicleRank;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getVehicleImageUrl() {
        return vehicleImageUrl;
    }

    public void setVehicleImageUrl(String vehicleImageUrl) {
        this.vehicleImageUrl = vehicleImageUrl;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Timestamp getDatachageCreatetime() {
        return datachageCreatetime;
    }

    public void setDatachageCreatetime(Timestamp datachageCreatetime) {
        this.datachageCreatetime = datachageCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getServiceLabels() {
        return serviceLabels;
    }

    public void setServiceLabels(String serviceLabels) {
        this.serviceLabels = serviceLabels;
    }

    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    public BigDecimal getFloorPrice() {
        return floorPrice;
    }

    public void setFloorPrice(BigDecimal floorPrice) {
        this.floorPrice = floorPrice;
    }

    public Boolean getIsCalabi() {
        return isCalabi;
    }

    public void setIsCalabi(Boolean isCalabi) {
        this.isCalabi = isCalabi;
    }

}
