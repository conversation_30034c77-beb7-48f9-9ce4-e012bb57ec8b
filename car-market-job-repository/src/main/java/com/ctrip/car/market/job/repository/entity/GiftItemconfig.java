package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "gift_itemconfig")
public class GiftItemconfig implements DalPojo {
    /**
     * 标识列
     */
    @Id
    @Column(name = "itemid")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long itemid;

    /**
     * 项目编号
     */
    @Column(name = "giftNo")
    @Type(value = Types.VARCHAR)
    private String giftNo;

    /**
     * 是否保底
     */
    @Column(name = "isdefault")
    @Type(value = Types.SMALLINT)
    private Integer isdefault;

    /**
     * 1-8
     */
    @Column(name = "itemno")
    @Type(value = Types.INTEGER)
    private Integer itemno;

    /**
     * 百分比
     */
    @Column(name = "percent")
    @Type(value = Types.INTEGER)
    private Integer percent;

    /**
     * url地址
     */
    @Column(name = "url")
    @Type(value = Types.VARCHAR)
    private String url;

    /**
     * 地址
     */
    @Column(name = "urlh5")
    @Type(value = Types.VARCHAR)
    private String urlh5;

    /**
     * 发放策略号
     */
    @Column(name = "promotion")
    @Type(value = Types.INTEGER)
    private Integer promotion;

    /**
     * 备注
     */
    @Column(name = "itemremark")
    @Type(value = Types.VARCHAR)
    private String itemremark;

    /**
     * 创建时间
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 修改时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 图片地址
     */
    @Column(name = "imageUrl")
    @Type(value = Types.VARCHAR)
    private String imageUrl;

    /**
     * json格式，存放一些复杂的，多变的，不是查询条件的配置
     */
    @Column(name = "config")
    @Type(value = Types.VARCHAR)
    private String config;

    public Long getItemid() {
        return itemid;
    }

    public void setItemid(Long itemid) {
        this.itemid = itemid;
    }

    public String getGiftNo() {
        return giftNo;
    }

    public void setGiftNo(String giftNo) {
        this.giftNo = giftNo;
    }

    public Integer getIsdefault() {
        return isdefault;
    }

    public void setIsdefault(Integer isdefault) {
        this.isdefault = isdefault;
    }

    public Integer getItemno() {
        return itemno;
    }

    public void setItemno(Integer itemno) {
        this.itemno = itemno;
    }

    public Integer getPercent() {
        return percent;
    }

    public void setPercent(Integer percent) {
        this.percent = percent;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrlh5() {
        return urlh5;
    }

    public void setUrlh5(String urlh5) {
        this.urlh5 = urlh5;
    }

    @Deprecated
    public Integer getPromotion() {
        return promotion;
    }

    public void setPromotion(Integer promotion) {
        this.promotion = promotion;
    }

    public String getItemremark() {
        return itemremark;
    }

    public void setItemremark(String itemremark) {
        this.itemremark = itemremark;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }
}

