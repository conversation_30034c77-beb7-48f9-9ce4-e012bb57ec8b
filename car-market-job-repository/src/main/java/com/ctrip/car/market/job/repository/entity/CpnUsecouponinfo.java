package com.ctrip.car.market.job.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.math.BigDecimal;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2022-03-15
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_usecouponinfo")
public class CpnUsecouponinfo implements DalPojo {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "UseCouponID")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long useCouponID;

    /**
     * 用户id
     */
    @Column(name = "UID")
    @Type(value = Types.VARCHAR)
    private String uID;

    /**
     * 优惠券策略id
     */
    @Column(name = "PromotionID")
    @Type(value = Types.INTEGER)
    private Integer promotionID;

    /**
     * 优惠券代码
     */
    @Column(name = "CouponCode")
    @Type(value = Types.VARCHAR)
    private String couponCode;

    /**
     * 交易流水号
     */
    @Column(name = "TransactionID")
    @Type(value = Types.BIGINT)
    private Long transactionID;

    /**
     * 订单号
     */
    @Column(name = "OrderID")
    @Type(value = Types.BIGINT)
    private Long orderID;

    /**
     * 最晚可取消时间
     */
    @Column(name = "AllowCancelTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp allowCancelTime;

    /**
     * 使用代码
     */
    @Column(name = "UseCode")
    @Type(value = Types.VARCHAR)
    private String useCode;

    /**
     * 优惠券状态，状态包含：0-未发放、1-未使用、2-已过期、3-已使用、4-已废弃、5-已冻结
     */
    @Column(name = "CouponStatus")
    @Type(value = Types.TINYINT)
    private Integer couponStatus;

    /**
     * 创建时间
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 修改时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 字符串订单号
     */
    @Column(name = "ItemOrderID")
    @Type(value = Types.VARCHAR)
    private String itemOrderID;

    /**
     * 优惠金额
     */
    @Column(name = "DeductionAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal deductionAmount;

    public Long getUseCouponID() {
        return useCouponID;
    }

    public void setUseCouponID(Long useCouponID) {
        this.useCouponID = useCouponID;
    }

    public String getUID() {
        return uID;
    }

    public void setUID(String uID) {
        this.uID = uID;
    }

    public Integer getPromotionID() {
        return promotionID;
    }

    public void setPromotionID(Integer promotionID) {
        this.promotionID = promotionID;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public Long getTransactionID() {
        return transactionID;
    }

    public void setTransactionID(Long transactionID) {
        this.transactionID = transactionID;
    }

    public Long getOrderID() {
        return orderID;
    }

    public void setOrderID(Long orderID) {
        this.orderID = orderID;
    }

    public Timestamp getAllowCancelTime() {
        return allowCancelTime;
    }

    public void setAllowCancelTime(Timestamp allowCancelTime) {
        this.allowCancelTime = allowCancelTime;
    }

    public String getUseCode() {
        return useCode;
    }

    public void setUseCode(String useCode) {
        this.useCode = useCode;
    }

    public Integer getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(Integer couponStatus) {
        this.couponStatus = couponStatus;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getItemOrderID() {
        return itemOrderID;
    }

    public void setItemOrderID(String itemOrderID) {
        this.itemOrderID = itemOrderID;
    }

    public BigDecimal getDeductionAmount() {
        return deductionAmount;
    }

    public void setDeductionAmount(BigDecimal deductionAmount) {
        this.deductionAmount = deductionAmount;
    }

}