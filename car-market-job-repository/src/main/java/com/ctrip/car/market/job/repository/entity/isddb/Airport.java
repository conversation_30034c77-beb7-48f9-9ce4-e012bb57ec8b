package com.ctrip.car.market.job.repository.entity.isddb;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "carisddb_dalcluster")
@Table(name = "airport")
public class Airport implements DalPojo {

    /**
     * 机场编码
     */
    @Id
    @Column(name = "AirportCode")
    @Type(value = Types.CHAR)
    private String airportCode;

    /**
     * 城市Id
     */
    @Column(name = "CityId")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    /**
     * 机场名称
     */
    @Column(name = "AirportName")
    @Type(value = Types.VARCHAR)
    private String airportName;

    /**
     * 英文名称
     */
    @Column(name = "EnglishName")
    @Type(value = Types.VARCHAR)
    private String englishName;

    /**
     * 首字母
     */
    @Column(name = "FirstChar")
    @Type(value = Types.CHAR)
    private String firstChar;

    /**
     * 经度
     */
    @Column(name = "Longitude")
    @Type(value = Types.DECIMAL)
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @Column(name = "Latitude")
    @Type(value = Types.DECIMAL)
    private BigDecimal latitude;

    /**
     * 是否可用
     */
    @Column(name = "IsActive")
    @Type(value = Types.BIT)
    private Boolean isActive;

    /**
     * 排序列
     */
    @Column(name = "SortNum")
    @Type(value = Types.INTEGER)
    private Integer sortNum;

    /**
     * 修改时间戳
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public String getAirportCode() {
        return airportCode;
    }

    public void setAirportCode(String airportCode) {
        this.airportCode = airportCode;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getAirportName() {
        return airportName;
    }

    public void setAirportName(String airportName) {
        this.airportName = airportName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getFirstChar() {
        return firstChar;
    }

    public void setFirstChar(String firstChar) {
        this.firstChar = firstChar;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Integer getSortNum() {
        return sortNum;
    }

    public void setSortNum(Integer sortNum) {
        this.sortNum = sortNum;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}