package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "seo_hot_information")
public class SeoHotInformation implements DalPojo {

    /**
     * 自增主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * poi类型 1:机场
     */
    @Column(name = "poiType")
    @Type(value = Types.TINYINT)
    private Integer poiType;

    /**
     * poiCode
     */
    @Column(name = "poiCode")
    @Type(value = Types.VARCHAR)
    private String poiCode;

    /**
     * 国家id
     */
    @Column(name = "countryId")
    @Type(value = Types.INTEGER)
    private Integer countryId;

    /**
     * 城市id
     */
    @Column(name = "cityId")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    /**
     * 供应商id
     */
    @Column(name = "vendorId")
    @Type(value = Types.INTEGER)
    private Integer vendorId;

    /**
     * 供应商名称
     */
    @Column(name = "vendorName")
    @Type(value = Types.VARCHAR)
    private String vendorName;

    /**
     * 车型组id
     */
    @Column(name = "vehicleGroupId")
    @Type(value = Types.INTEGER)
    private Integer vehicleGroupId;

    /**
     * 车型组名称
     */
    @Column(name = "vehicleGroupName")
    @Type(value = Types.VARCHAR)
    private String vehicleGroupName;

    /**
     * 常见租期
     */
    @Column(name = "tenancy")
    @Type(value = Types.INTEGER)
    private Integer tenancy;

    /**
     * 状态
     */
    @Column(name = "status")
    @Type(value = Types.TINYINT)
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getPoiType() {
        return poiType;
    }

    public void setPoiType(Integer poiType) {
        this.poiType = poiType;
    }

    public String getPoiCode() {
        return poiCode;
    }

    public void setPoiCode(String poiCode) {
        this.poiCode = poiCode;
    }

    public Integer getCountryId() {
        return countryId;
    }

    public void setCountryId(Integer countryId) {
        this.countryId = countryId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public Integer getVehicleGroupId() {
        return vehicleGroupId;
    }

    public void setVehicleGroupId(Integer vehicleGroupId) {
        this.vehicleGroupId = vehicleGroupId;
    }

    public String getVehicleGroupName() {
        return vehicleGroupName;
    }

    public void setVehicleGroupName(String vehicleGroupName) {
        this.vehicleGroupName = vehicleGroupName;
    }

    public Integer getTenancy() {
        return tenancy;
    }

    public void setTenancy(Integer tenancy) {
        this.tenancy = tenancy;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }
}
