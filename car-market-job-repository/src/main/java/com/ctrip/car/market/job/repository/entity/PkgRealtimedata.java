package com.ctrip.car.market.job.repository.entity;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.math.BigDecimal;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2021-07-08
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "pkg_realtimedata")
public class PkgRealtimedata implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long iD;

    /**
     * 34 海外，35国内
     */
    @Column(name = "BusinessType")
    @Type(value = Types.INTEGER)
    private Integer businessType;

    /**
     * 城市ID
     */
    @Column(name = "CityId")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    /**
     * 车辆名称
     */
    @Column(name = "vehicleName")
    @Type(value = Types.VARCHAR)
    private String vehicleName;

    /**
     * 车辆code
     */
    @Column(name = "vehcileCode")
    @Type(value = Types.VARCHAR)
    private String vehcileCode;

    /**
     * 车图片
     */
    @Column(name = "vehicleImgPc")
    @Type(value = Types.CHAR)
    private String vehicleImgPc;

    /**
     * 车图片
     */
    @Column(name = "vehicleImgh5")
    @Type(value = Types.VARCHAR)
    private String vehicleImgh5;

    /**
     * 供应商code
     */
    @Column(name = "bizVendorCode")
    @Type(value = Types.VARCHAR)
    private String bizVendorCode;

    /**
     * 供应商logo
     */
    @Column(name = "vendorLogo")
    @Type(value = Types.VARCHAR)
    private String vendorLogo;

    /**
     * 门店code
     */
    @Column(name = "storeCode")
    @Type(value = Types.VARCHAR)
    private String storeCode;

    /**
     * 门店名称
     */
    @Column(name = "storename")
    @Type(value = Types.VARCHAR)
    private String storename;

    /**
     * 所在车型组
     */
    @Column(name = "vehicleGroupCode")
    @Type(value = Types.VARCHAR)
    private String vehicleGroupCode;

    /**
     * 所在车型组名称
     */
    @Column(name = "vehicleGroupName")
    @Type(value = Types.VARCHAR)
    private String vehicleGroupName;

    /**
     * 价格
     */
    @Column(name = "vehiclePrice")
    @Type(value = Types.VARCHAR)
    private String vehiclePrice;

    /**
     * 评分
     */
    @Column(name = "vehicleScore")
    @Type(value = Types.VARCHAR)
    private String vehicleScore;

    /**
     * listPageH5Url
     */
    @Column(name = "listPageH5Url")
    @Type(value = Types.VARCHAR)
    private String listPageH5Url;

    /**
     * listPagePcUrl
     */
    @Column(name = "listPagePcUrl")
    @Type(value = Types.VARCHAR)
    private String listPagePcUrl;

    /**
     * listPageMiniAppUrl
     */
    @Column(name = "listPageMiniAppUrl")
    @Type(value = Types.VARCHAR)
    private String listPageMiniAppUrl;

    /**
     * listPageAppUrl
     */
    @Column(name = "listPageAppUrl")
    @Type(value = Types.VARCHAR)
    private String listPageAppUrl;

    /**
     * fillPageH5url
     */
    @Column(name = "fillPageH5url")
    @Type(value = Types.VARCHAR)
    private String fillPageH5url;

    /**
     * fillPagePcUrl
     */
    @Column(name = "fillPagePcUrl")
    @Type(value = Types.VARCHAR)
    private String fillPagePcUrl;

    /**
     * fillPageMiniAppUrl
     */
    @Column(name = "fillPageMiniAppUrl")
    @Type(value = Types.VARCHAR)
    private String fillPageMiniAppUrl;

    /**
     * fillPageAppUrl
     */
    @Column(name = "fillPageAppUrl")
    @Type(value = Types.VARCHAR)
    private String fillPageAppUrl;

    /**
     * 库存 剩余库存数量
     */
    @Column(name = "stockNumber")
    @Type(value = Types.INTEGER)
    private Integer stockNumber;

    /**
     * 取车方式
     */
    @Column(name = "takeCarWay")
    @Type(value = Types.VARCHAR)
    private String takeCarWay;

    /**
     * 特色/标签
     */
    @Column(name = "servicelabels")
    @Type(value = Types.VARCHAR)
    private String servicelabels;

    /**
     * 0 自动，1手动
     */
    @Column(name = "automatic")
    @Type(value = Types.TINYINT)
    private Integer automatic;

    /**
     * 排量
     */
    @Column(name = "displacement")
    @Type(value = Types.VARCHAR)
    private String displacement;

    /**
     * 座位数
     */
    @Column(name = "seating")
    @Type(value = Types.TINYINT)
    private Integer seating;

    /**
     * 产品名称
     */
    @Column(name = "productname")
    @Type(value = Types.VARCHAR)
    private String productname;

    /**
     * 行李数
     */
    @Column(name = "luggageCount")
    @Type(value = Types.TINYINT)
    private Integer luggageCount;

    /**
     * startDate
     */
    @Column(name = "startDate")
    @Type(value = Types.TIMESTAMP)
    private Timestamp startDate;

    /**
     * enDate
     */
    @Column(name = "endDate")
    @Type(value = Types.TIMESTAMP)
    private Timestamp endDate;

    /**
     * 0 无效，1有效
     */
    @Column(name = "status")
    @Type(value = Types.TINYINT)
    private Integer status;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 供应商名字
     */
    @Column(name = "vendorName")
    @Type(value = Types.VARCHAR)
    private String vendorName;

    /**
     * 产品价格
     */
    @Column(name = "saleprice")
    @Type(value = Types.DECIMAL)
    private BigDecimal saleprice;

    public Long getID() {
        return iD;
    }

    public void setID(Long iD) {
        this.iD = iD;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }

    public String getVehcileCode() {
        return vehcileCode;
    }

    public void setVehcileCode(String vehcileCode) {
        this.vehcileCode = vehcileCode;
    }

    public String getVehicleImgPc() {
        return vehicleImgPc;
    }

    public void setVehicleImgPc(String vehicleImgPc) {
        this.vehicleImgPc = vehicleImgPc;
    }

    public String getVehicleImgh5() {
        return vehicleImgh5;
    }

    public void setVehicleImgh5(String vehicleImgh5) {
        this.vehicleImgh5 = vehicleImgh5;
    }

    public String getBizVendorCode() {
        return bizVendorCode;
    }

    public void setBizVendorCode(String bizVendorCode) {
        this.bizVendorCode = bizVendorCode;
    }

    public String getVendorLogo() {
        return vendorLogo;
    }

    public void setVendorLogo(String vendorLogo) {
        this.vendorLogo = vendorLogo;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getStorename() {
        return storename;
    }

    public void setStorename(String storename) {
        this.storename = storename;
    }

    public String getVehicleGroupCode() {
        return vehicleGroupCode;
    }

    public void setVehicleGroupCode(String vehicleGroupCode) {
        this.vehicleGroupCode = vehicleGroupCode;
    }

    public String getVehicleGroupName() {
        return vehicleGroupName;
    }

    public void setVehicleGroupName(String vehicleGroupName) {
        this.vehicleGroupName = vehicleGroupName;
    }

    public String getVehiclePrice() {
        return vehiclePrice;
    }

    public void setVehiclePrice(String vehiclePrice) {
        this.vehiclePrice = vehiclePrice;
    }

    public String getVehicleScore() {
        return vehicleScore;
    }

    public void setVehicleScore(String vehicleScore) {
        this.vehicleScore = vehicleScore;
    }

    public String getListPageH5Url() {
        return listPageH5Url;
    }

    public void setListPageH5Url(String listPageH5Url) {
        this.listPageH5Url = listPageH5Url;
    }

    public String getListPagePcUrl() {
        return listPagePcUrl;
    }

    public void setListPagePcUrl(String listPagePcUrl) {
        this.listPagePcUrl = listPagePcUrl;
    }

    public String getListPageMiniAppUrl() {
        return listPageMiniAppUrl;
    }

    public void setListPageMiniAppUrl(String listPageMiniAppUrl) {
        this.listPageMiniAppUrl = listPageMiniAppUrl;
    }

    public String getListPageAppUrl() {
        return listPageAppUrl;
    }

    public void setListPageAppUrl(String listPageAppUrl) {
        this.listPageAppUrl = listPageAppUrl;
    }

    public String getFillPageH5url() {
        return fillPageH5url;
    }

    public void setFillPageH5url(String fillPageH5url) {
        this.fillPageH5url = fillPageH5url;
    }

    public String getFillPagePcUrl() {
        return fillPagePcUrl;
    }

    public void setFillPagePcUrl(String fillPagePcUrl) {
        this.fillPagePcUrl = fillPagePcUrl;
    }

    public String getFillPageMiniAppUrl() {
        return fillPageMiniAppUrl;
    }

    public void setFillPageMiniAppUrl(String fillPageMiniAppUrl) {
        this.fillPageMiniAppUrl = fillPageMiniAppUrl;
    }

    public String getFillPageAppUrl() {
        return fillPageAppUrl;
    }

    public void setFillPageAppUrl(String fillPageAppUrl) {
        this.fillPageAppUrl = fillPageAppUrl;
    }

    public Integer getStockNumber() {
        return stockNumber;
    }

    public void setStockNumber(Integer stockNumber) {
        this.stockNumber = stockNumber;
    }

    public String getTakeCarWay() {
        return takeCarWay;
    }

    public void setTakeCarWay(String takeCarWay) {
        this.takeCarWay = takeCarWay;
    }

    public String getServicelabels() {
        return servicelabels;
    }

    public void setServicelabels(String servicelabels) {
        this.servicelabels = servicelabels;
    }

    public Integer getAutomatic() {
        return automatic;
    }

    public void setAutomatic(Integer automatic) {
        this.automatic = automatic;
    }

    public String getDisplacement() {
        return displacement;
    }

    public void setDisplacement(String displacement) {
        this.displacement = displacement;
    }

    public Integer getSeating() {
        return seating;
    }

    public void setSeating(Integer seating) {
        this.seating = seating;
    }

    public String getProductname() {
        return productname;
    }

    public void setProductname(String productname) {
        this.productname = productname;
    }

    public Integer getLuggageCount() {
        return luggageCount;
    }

    public void setLuggageCount(Integer luggageCount) {
        this.luggageCount = luggageCount;
    }

    public Timestamp getStartDate() {
        return startDate;
    }

    public void setStartDate(Timestamp startDate) {
        this.startDate = startDate;
    }

    public Timestamp getEndDate() {
        return endDate;
    }

    public void setEndDate(Timestamp endDate) {
        this.endDate = endDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public BigDecimal getSaleprice() {
        return saleprice;
    }

    public void setSaleprice(BigDecimal saleprice) {
        this.saleprice = saleprice;
    }

}
