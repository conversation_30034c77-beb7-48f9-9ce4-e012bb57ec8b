package com.ctrip.car.market.job.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2023-06-05
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "channel_number")
public class ChannelNumber implements <PERSON><PERSON><PERSON>jo {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 渠道号名称
     */
    @Column(name = "channel_name")
    @Type(value = Types.VARCHAR)
    private String channelName;

    /**
     * 第三级渠道id
     */
    @Column(name = "tertiary_channel_id")
    @Type(value = Types.BIGINT)
    private Long tertiaryChannelId;

    /**
     * 合作BU，关联dictionary，dict_type=cooperation的
     */
    @Column(name = "cooperation_id")
    @Type(value = Types.VARCHAR)
    private String cooperationId;

    /**
     * 合作BU名称
     */
    @Column(name = "cooperation_name")
    @Type(value = Types.VARCHAR)
    private String cooperationName;

    /**
     * 合作方式API对接、页面嵌入、倒流，关联dictionary，
     */
    @Column(name = "cooperation_mode_id")
    @Type(value = Types.VARCHAR)
    private String cooperationModeId;

    /**
     * 合作方式名称
     */
    @Column(name = "cooperation_mode_name")
    @Type(value = Types.VARCHAR)
    private String cooperationModeName;

    /**
     * 页面端app、h5、online、小程序，关联dictionary，dict_type=page_location的dict_key字段
     */
    @Column(name = "page_location_id")
    @Type(value = Types.VARCHAR)
    private String pageLocationId;

    /**
     * 页面端名称
     */
    @Column(name = "page_location_name")
    @Type(value = Types.VARCHAR)
    private String pageLocationName;

    /**
     * 市场信息id
     */
    @Column(name = "channel_market_id")
    @Type(value = Types.BIGINT)
    private Long channelMarketId;

    /**
     * 备注
     */
    @Column(name = "remark")
    @Type(value = Types.VARCHAR)
    private String remark;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    @Type(value = Types.VARCHAR)
    private String operatorId;

    /**
     * 操作人
     */
    @Column(name = "operator")
    @Type(value = Types.VARCHAR)
    private String operator;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public Long getTertiaryChannelId() {
        return tertiaryChannelId;
    }

    public void setTertiaryChannelId(Long tertiaryChannelId) {
        this.tertiaryChannelId = tertiaryChannelId;
    }

    public String getCooperationId() {
        return cooperationId;
    }

    public void setCooperationId(String cooperationId) {
        this.cooperationId = cooperationId;
    }

    public String getCooperationName() {
        return cooperationName;
    }

    public void setCooperationName(String cooperationName) {
        this.cooperationName = cooperationName;
    }

    public String getCooperationModeId() {
        return cooperationModeId;
    }

    public void setCooperationModeId(String cooperationModeId) {
        this.cooperationModeId = cooperationModeId;
    }

    public String getCooperationModeName() {
        return cooperationModeName;
    }

    public void setCooperationModeName(String cooperationModeName) {
        this.cooperationModeName = cooperationModeName;
    }

    public String getPageLocationId() {
        return pageLocationId;
    }

    public void setPageLocationId(String pageLocationId) {
        this.pageLocationId = pageLocationId;
    }

    public String getPageLocationName() {
        return pageLocationName;
    }

    public void setPageLocationName(String pageLocationName) {
        this.pageLocationName = pageLocationName;
    }

    public Long getChannelMarketId() {
        return channelMarketId;
    }

    public void setChannelMarketId(Long channelMarketId) {
        this.channelMarketId = channelMarketId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}
