package com.ctrip.car.market.job.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-04-15
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "channel")
public class Channel implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 第一级渠道id
     */
    @Column(name = "primary_channel_id")
    @Type(value = Types.BIGINT)
    private Long primaryChannelId;

    /**
     * 第一级渠道名称
     */
    @Column(name = "primary_channel_name")
    @Type(value = Types.VARCHAR)
    private String primaryChannelName;

    /**
     * 第二级渠道id
     */
    @Column(name = "secondary_channel_id")
    @Type(value = Types.BIGINT)
    private Long secondaryChannelId;

    /**
     * 第二级渠道名称
     */
    @Column(name = "secondary_channel_name")
    @Type(value = Types.VARCHAR)
    private String secondaryChannelName;

    /**
     * 第三级渠道id
     */
    @Column(name = "tertiary_channel_id")
    @Type(value = Types.BIGINT)
    private Long tertiaryChannelId;

    /**
     * 第三级渠道名称
     */
    @Column(name = "tertiary_channel_name")
    @Type(value = Types.VARCHAR)
    private String tertiaryChannelName;

    /**
     * 是否有效 0-无效 1-有效
     */
    @Column(name = "valid")
    @Type(value = Types.TINYINT)
    private Integer valid;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    @Type(value = Types.VARCHAR)
    private String operatorId;

    /**
     * 操作人
     */
    @Column(name = "operator")
    @Type(value = Types.VARCHAR)
    private String operator;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPrimaryChannelId() {
        return primaryChannelId;
    }

    public void setPrimaryChannelId(Long primaryChannelId) {
        this.primaryChannelId = primaryChannelId;
    }

    public String getPrimaryChannelName() {
        return primaryChannelName;
    }

    public void setPrimaryChannelName(String primaryChannelName) {
        this.primaryChannelName = primaryChannelName;
    }

    public Long getSecondaryChannelId() {
        return secondaryChannelId;
    }

    public void setSecondaryChannelId(Long secondaryChannelId) {
        this.secondaryChannelId = secondaryChannelId;
    }

    public String getSecondaryChannelName() {
        return secondaryChannelName;
    }

    public void setSecondaryChannelName(String secondaryChannelName) {
        this.secondaryChannelName = secondaryChannelName;
    }

    public Long getTertiaryChannelId() {
        return tertiaryChannelId;
    }

    public void setTertiaryChannelId(Long tertiaryChannelId) {
        this.tertiaryChannelId = tertiaryChannelId;
    }

    public String getTertiaryChannelName() {
        return tertiaryChannelName;
    }

    public void setTertiaryChannelName(String tertiaryChannelName) {
        this.tertiaryChannelName = tertiaryChannelName;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}