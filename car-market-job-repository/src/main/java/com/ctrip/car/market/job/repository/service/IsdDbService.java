package com.ctrip.car.market.job.repository.service;

import com.ctrip.car.market.job.repository.entity.isddb.Airport;
import com.ctrip.car.market.job.repository.entity.isddb.ExtCity;
import com.ctrip.car.market.job.repository.entity.isddb.IsdLocation;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;

@Repository
public class IsdDbService {

    private final DalTableOperations<ExtCity> cityOperations = DalOperationsFactory.getDalTableOperations(ExtCity.class);

    private final DalTableOperations<IsdLocation> locationOperations = DalOperationsFactory.getDalTableOperations(IsdLocation.class);

    private final DalTableOperations<Airport> airportOperations = DalOperationsFactory.getDalTableOperations(Airport.class);

    public List<ExtCity> getCityProvince() throws SQLException {
        return cityOperations.query("select c.CityId, c.CityName,p.ProvinceName from city c inner join province p on c.ProvinceId=p.ProvinceId where c.IsActive=1 and p.IsActive=1;", new DalHints(), Maps.newHashMap());
    }

    public List<IsdLocation> getAllLocation() throws SQLException {
        return locationOperations.query("select * from isd_location where isActive=1", new DalHints(), Maps.newHashMap());
    }

    public List<Airport> getAllAirport() throws Exception {
        return airportOperations.query("select * from airport where IsActive=1", new DalHints(), Maps.newHashMap());
    }
}
