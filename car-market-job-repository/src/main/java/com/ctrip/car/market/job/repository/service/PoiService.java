package com.ctrip.car.market.job.repository.service;

import com.ctrip.car.market.job.repository.entity.CarPoiMapping;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PoiService {

    private final DalTableOperations<CarPoiMapping> mappingOperations = DalOperationsFactory.getDalTableOperations(CarPoiMapping.class);

    private final static int limit = 1000;

    public List<CarPoiMapping> query(long poiId) throws Exception {
        String sql = "select * from car_poi_mapping where poiId>? and isActive=0 order by poiId limit ?;";
        return mappingOperations.query(sql, new DalHints(), poiId, limit);
    }

    public boolean update(CarPoiMapping mapping) throws Exception {
        return mappingOperations.update(new DalHints(), mapping) > 0;
    }
}
