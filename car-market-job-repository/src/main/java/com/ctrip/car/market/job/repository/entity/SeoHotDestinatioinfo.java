package com.ctrip.car.market.job.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "seo_hot_destinatioinfo")
public class SeoHotDestinatioinfo implements DalPojo {

    /**
     * 自增主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 1:机场。后续会有火车站
     */
	@Column(name = "poiType")
	@Type(value = Types.TINYINT)
	private Integer poiType;


	/**
	 * poiId
	 */
	@Column(name = "poiId")
	@Type(value = Types.BIGINT)
	private Long poiId;


    /**
     * 机场三字码、车站code
     */
	@Column(name = "poiCode")
	@Type(value = Types.VARCHAR)
	private String poiCode;

    /**
     * 机场名称、火车站名称
     */
	@Column(name = "poiName")
	@Type(value = Types.VARCHAR)
	private String poiName;

    /**
     * 国家id
     */
	@Column(name = "countryId")
	@Type(value = Types.INTEGER)
	private Integer countryId;

    /**
     * 城市id
     */
	@Column(name = "cityId")
	@Type(value = Types.INTEGER)
	private Integer cityId;

    /**
     * url
     */
	@Column(name = "url")
	@Type(value = Types.VARCHAR)
	private String url;

    /**
     * 0:激活，1:失效
     */
	@Column(name = "status")
	@Type(value = Types.TINYINT)
	private Integer status;

    /**
     * 预定量
     */
	@Column(name = "orderNum")
	@Type(value = Types.INTEGER)
	private Integer orderNum;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getPoiType() {
		return poiType;
	}

	public void setPoiType(Integer poiType) {
		this.poiType = poiType;
	}

	public String getPoiCode() {
		return poiCode;
	}

	public void setPoiCode(String poiCode) {
		this.poiCode = poiCode;
	}

	public String getPoiName() {
		return poiName;
	}

	public void setPoiName(String poiName) {
		this.poiName = poiName;
	}

	public Integer getCountryId() {
		return countryId;
	}

	public void setCountryId(Integer countryId) {
		this.countryId = countryId;
	}

	public Integer getCityId() {
		return cityId;
	}

	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(Integer orderNum) {
		this.orderNum = orderNum;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public Long getPoiId() {
		return poiId;
	}

	public void setPoiId(Long poiId) {
		this.poiId = poiId;
	}
}