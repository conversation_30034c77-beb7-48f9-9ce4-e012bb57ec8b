package com.ctrip.car.market.job.repository.entity.activity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2025-06-19
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "vendor_skuinfo")
public class VendorSkuinfo implements DalPojo {

    /**
     * 自增主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 供应商id
     */
    @Column(name = "vendorId")
    @Type(value = Types.BIGINT)
    private Long vendorId;

    /**
     * skuId
     */
    @Column(name = "skuId")
    @Type(value = Types.BIGINT)
    private Long skuId;

    /**
     * 门店id
     */
    @Column(name = "storeId")
    @Type(value = Types.BIGINT)
    private Long storeId;

    /**
     * 标准产品id
     */
    @Column(name = "standardProductId")
    @Type(value = Types.BIGINT)
    private Long standardProductId;

    /**
     * 是否有效，0无效1有效
     */
    @Column(name = "isActive")
    @Type(value = Types.INTEGER)
    private Integer isActive;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getVendorId() {
        return vendorId;
    }

    public void setVendorId(Long vendorId) {
        this.vendorId = vendorId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Long getStandardProductId() {
        return standardProductId;
    }

    public void setStandardProductId(Long standardProductId) {
        this.standardProductId = standardProductId;
    }

    public Integer getIsActive() {
        return isActive;
    }

    public void setIsActive(Integer isActive) {
        this.isActive = isActive;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}
