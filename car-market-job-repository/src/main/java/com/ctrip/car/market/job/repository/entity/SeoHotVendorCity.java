package com.ctrip.car.market.job.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR> yao （姚苏滨）
 * @date 2025-05-16
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "seo_hot_vendor_city")
public class SeoHotVendorCity implements DalPojo {

    /**
     * 自增主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 供应商id
     */
    @Column(name = "vendorId")
    @Type(value = Types.VARCHAR)
    private String vendorId;

    /**
     * 供应商名称
     */
    @Column(name = "vendorName")
    @Type(value = Types.VARCHAR)
    private String vendorName;

    /**
     * 城市id
     */
    @Column(name = "cityId")
    @Type(value = Types.INTEGER)
    private Integer cityId;

    /**
     * 城市名称
     */
    @Column(name = "cityName")
    @Type(value = Types.VARCHAR)
    private String cityName;

    /**
     * 页面地址
     */
    @Column(name = "url")
    @Type(value = Types.VARCHAR)
    private String url;

    /**
     * 0-激活1-未激活
     */
    @Column(name = "status")
    @Type(value = Types.INTEGER)
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVendorId() {
        return vendorId;
    }

    public void setVendorId(String vendorId) {
        this.vendorId = vendorId;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}