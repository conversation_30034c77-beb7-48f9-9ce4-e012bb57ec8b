package com.ctrip.car.market.job.repository.entity;

import java.sql.Timestamp;
import java.sql.Types;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_restricted_condition")
public class CpnRestrictedCondition implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "conditionId")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.INTEGER)
    private Integer conditionId;

    /**
     * 策略id
     */
    @Column(name = "promotionId")
    @Type(value = Types.INTEGER)
    private Integer promotionId;

    /**
     * 策略名称
     */
    @Column(name = "conditionName")
    @Type(value = Types.VARCHAR)
    private String conditionName;

    /**
     * 内容
     */
    @Column(name = "content")
    @Type(value = Types.LONGVARCHAR)
    private String content;

    /**
     * 创建时间
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 是否有效
     */
    @Column(name = "isValid")
    @Type(value = Types.TINYINT)
    private Integer isValid;

    /**
     * groupid
     */
    @Column(name = "groupId")
    @Type(value = Types.INTEGER)
    private Integer groupId;

    /**
     * 版本号
     */
    @Column(name = "groupVersion")
    @Type(value = Types.INTEGER)
    private Integer groupVersion;

    public Integer getConditionId() {
        return conditionId;
    }

    public void setConditionId(Integer conditionId) {
        this.conditionId = conditionId;
    }

    public Integer getPromotionId() {
        return promotionId;
    }

    public void setPromotionId(Integer promotionId) {
        this.promotionId = promotionId;
    }

    public String getConditionName() {
        return conditionName;
    }

    public void setConditionName(String conditionName) {
        this.conditionName = conditionName;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Integer getGroupVersion() {
        return groupVersion;
    }

    public void setGroupVersion(Integer groupVersion) {
        this.groupVersion = groupVersion;
    }

}

