package com.ctrip.car.market.job.repository.entity;

/**
 * <AUTHOR>
 * @Description:
 * @create 2025/7/8 17:33
 */

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2025-07-08
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "mkt_task_assist_info")
public class MktTaskAssistInfo implements DalPojo {

    /**
     * 自增主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 活动id
     */
    @Column(name = "projectId")
    @Type(value = Types.BIGINT)
    private Long projectId;

    /**
     * 任务id
     */
    @Column(name = "taskId")
    @Type(value = Types.BIGINT)
    private Long taskId;

    /**
     * 订单id
     */
    @Column(name = "orderId")
    @Type(value = Types.BIGINT)
    private Long orderId;

    /**
     * 0:租车；1:包车；2:接送机
     */
    @Column(name = "productLine")
    @Type(value = Types.INTEGER)
    private Integer productLine;

    /**
     * 用户id
     */
    @Column(name = "userId")
    @Type(value = Types.VARCHAR)
    private String userId;

    /**
     * 携程头像
     */
    @Column(name = "avatarPictureURL")
    @Type(value = Types.VARCHAR)
    private String avatarPictureURL;

    /**
     * 助力人携程昵称
     */
    @Column(name = "nickname")
    @Type(value = Types.VARCHAR)
    private String nickname;

    /**
     * 被邀请人id
     */
    @Column(name = "beInvitedUid")
    @Type(value = Types.VARCHAR)
    private String beInvitedUid;

    /**
     * 用户任务领取表主键
     */
    @Column(name = "userReceivedTaskId")
    @Type(value = Types.BIGINT)
    private Long userReceivedTaskId;

    /**
     * 事件触发时间(毫秒值)
     */
    @Column(name = "triggerTime")
    @Type(value = Types.BIGINT)
    private Long triggerTime;

    /**
     * 当前进度
     */
    @Column(name = "currentProcess")
    @Type(value = Types.INTEGER)
    private Integer currentProcess;

    /**
     * 事件目标 任务总进度
     */
    @Column(name = "eventTarget")
    @Type(value = Types.INTEGER)
    private Integer eventTarget;

    /**
     * 返现状态 0:等待订单完成，才返现 1:即将返现 2:已返现 3:取消返现
     */
    @Column(name = "cashbackStatus")
    @Type(value = Types.INTEGER)
    private Integer cashbackStatus;

    /**
     * 本次助力砍价的比例
     */
    @Column(name = "cashbackRatio")
    @Type(value = Types.DECIMAL)
    private BigDecimal cashbackRatio;

    /**
     * 本次助力砍价的实际金额
     */
    @Column(name = "cashbackMoney")
    @Type(value = Types.DECIMAL)
    private BigDecimal cashbackMoney;

    /**
     * 打款时间
     */
    @Column(name = "cashbackTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp cashbackTime;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getProductLine() {
        return productLine;
    }

    public void setProductLine(Integer productLine) {
        this.productLine = productLine;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAvatarPictureURL() {
        return avatarPictureURL;
    }

    public void setAvatarPictureURL(String avatarPictureURL) {
        this.avatarPictureURL = avatarPictureURL;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getBeInvitedUid() {
        return beInvitedUid;
    }

    public void setBeInvitedUid(String beInvitedUid) {
        this.beInvitedUid = beInvitedUid;
    }

    public Long getUserReceivedTaskId() {
        return userReceivedTaskId;
    }

    public void setUserReceivedTaskId(Long userReceivedTaskId) {
        this.userReceivedTaskId = userReceivedTaskId;
    }

    public Long getTriggerTime() {
        return triggerTime;
    }

    public void setTriggerTime(Long triggerTime) {
        this.triggerTime = triggerTime;
    }

    public Integer getCurrentProcess() {
        return currentProcess;
    }

    public void setCurrentProcess(Integer currentProcess) {
        this.currentProcess = currentProcess;
    }

    public Integer getEventTarget() {
        return eventTarget;
    }

    public void setEventTarget(Integer eventTarget) {
        this.eventTarget = eventTarget;
    }

    public Integer getCashbackStatus() {
        return cashbackStatus;
    }

    public void setCashbackStatus(Integer cashbackStatus) {
        this.cashbackStatus = cashbackStatus;
    }

    public BigDecimal getCashbackRatio() {
        return cashbackRatio;
    }

    public void setCashbackRatio(BigDecimal cashbackRatio) {
        this.cashbackRatio = cashbackRatio;
    }

    public BigDecimal getCashbackMoney() {
        return cashbackMoney;
    }

    public void setCashbackMoney(BigDecimal cashbackMoney) {
        this.cashbackMoney = cashbackMoney;
    }

    public Timestamp getCashbackTime() {
        return cashbackTime;
    }

    public void setCashbackTime(Timestamp cashbackTime) {
        this.cashbackTime = cashbackTime;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}
