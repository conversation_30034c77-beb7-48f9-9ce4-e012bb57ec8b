package com.ctrip.car.market.job.repository.service;

import com.ctrip.car.market.job.repository.entity.MsgAllianceinfo;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class LogDBService {

    private final DalTableOperations<MsgAllianceinfo> msgOperations = DalOperationsFactory.getDalTableOperations(MsgAllianceinfo.class);

    public List<MsgAllianceinfo> queryAllMsgAllianceinfo() throws Exception {
        return msgOperations.query("select * from msg_allianceinfo", new DalHints(), Maps.newHashMap());
    }
}
