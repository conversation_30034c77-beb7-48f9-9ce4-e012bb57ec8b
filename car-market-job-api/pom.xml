<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <artifactId>car-market-job</artifactId>
        <groupId>com.ctrip.car.market</groupId>
        <version>1.0.13</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>car-market-job-api</artifactId>
    <packaging>war</packaging>

    <properties>
        <mockito-core.version>2.8.47</mockito-core.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>car-market-job-domain</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>car-market-job-api</finalName>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.4</version>
                <executions>
                    <execution>
                        <id>jacoco-initialize</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <phase>verify</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

    </build>
</project>
