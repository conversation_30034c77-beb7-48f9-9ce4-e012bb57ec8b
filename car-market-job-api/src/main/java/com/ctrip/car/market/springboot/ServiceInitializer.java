package com.ctrip.car.market.springboot;

import com.ctrip.framework.cdubbo.spring.annotation.EnableCDubbo;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:53
 */
@SpringBootApplication
@ServletComponentScan
@DubboComponentScan
@EnableCDubbo
@ComponentScan("com.ctrip")
public class ServiceInitializer extends SpringBootServletInitializer {

    @Override
    public SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(ServiceInitializer.class);
    }
}
